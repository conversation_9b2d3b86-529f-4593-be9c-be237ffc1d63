const qs = require('qs');
const URL = require('url');
const _ = require('lodash');
const axios = require('axios');
const config = require('config');
const client = require('../../client');
const logger = require('../../utils/logger');
const ResponseWrapper = require('../../middlewares/response_wrapper');

const knowTitlePrefix = ["知识点一", "知识点二", "知识点三", "知识点四", "知识点五", "知识点六"];

async function generatePaper(req, res) {
    let resWrapper = new ResponseWrapper(req, res);
    const user_id = req.user.id;
    const period = req.body.period
    const subject = req.body.subject

    // 分析结果中的原题ID集合
    let quesIds = [];
    let knowledges = req.body.knowledges || [];
    knowledges = knowledges.slice(0, 5)
    knowledges.forEach(know_dict => {
        let questions = know_dict.questions || [];
        questions.forEach(ques_dict => {
            quesIds.push(ques_dict.id);
        })
    })

    let searchParams = [];
    let knowQuesType = [];
    let recomTypeNum = [];
    knowledges.forEach(knowDict => {
        let knowId = knowDict.id || ''
        let knowName = knowDict.name || ''

        // 每个知识点选择的试题数量
        let knowQuesNum = knowDict.ques_num || 0;
        let selectQuesNum = Math.max(Math.min(6, knowQuesNum), 4)
        
        // 知识点难度 和 推荐试题难度
        let knowDiff = knowDict.avg_diff || 0.45
        let RecoDiff = getQuesDiff(knowDiff)
        
        // 每个类型的试题都推荐一些试题
        let quesType = knowDict.ques_type || [];
        quesType.forEach(typeDict => {
            let typeName = typeDict.name || ''
            let typeQuesNum = typeDict.num || 0;
            let targetQuesNum = Math.ceil((typeQuesNum/knowQuesNum)*selectQuesNum)
            // 搜索试题参数
            let searchQues = {
                "period": period,
                "subject": subject,
                "type": typeName,
                "knowledges": `${knowId}`,
                "sort_by": "year",
                "offset": 0,
                "limit": 200,
                "difficulty": RecoDiff,
                "set_mode": {
                    "knowledges": "intersection"
                }
            }
            searchParams.push(searchQues)
            recomTypeNum.push(targetQuesNum)
            knowQuesType.push(`${knowName}__${typeName}`)
        })
    })

    // 试题搜索
    let algo_result = [];
    try {
        for (const arr of _.chunk(searchParams, 10)) {
            const res = await Promise.all(arr.map(e => SearchQuestions(e)));
            algo_result.push(...res);
        }
    } catch (err) {
        logger.error(err);
        algo_result = [];
    }
    // 获取结果试题ID集合
    let recomAllQues = [];
    algo_result.forEach(element => {
        let ques_list = element.questions || [];
        ques_list.forEach(ques => {
            if (!recomAllQues.includes(ques.id)){
                recomAllQues.push(ques.id)
            }
        })
    })
    // 查重
    let saveQuesIds = await filterRepeatQues(recomAllQues)

    // 筛选试题
    let knowTypeQues = {}
    let selectedQuesIds = []
    for (let i = 0; i < searchParams.length; i++) {
        let targetNum = recomTypeNum[i];
        let knowTypeName = knowQuesType[i];
        let recoQues = algo_result[i]['questions'] || []
        for (let j=0; j < recoQues.length; j++){
            let id = recoQues[j].id;
            // 已过滤重复试题
            if (!saveQuesIds.includes(id)){
                continue
            }

            // 已选试题
            if (selectedQuesIds.includes(id)){
                continue
            }
            selectedQuesIds.push(id)

            // 50%的概率允许原题出现
            if (quesIds.includes(id) && Math.random() > 0.5) {
                continue
            }
            
            // 试题来源
            let oneQues = recoQues[j]
            if (oneQues.hasOwnProperty('from')){
                delete oneQues['from']
            }

            // 引用试卷
            let newRefer = [];
            let refer_exampapers = oneQues.refer_exampapers || [];
            refer_exampapers.forEach(refer => {
                if (refer.hasOwnProperty('from')){
                    delete refer['from']
                }
                refer['name'] = refer['vague_name']
                newRefer.push(refer)
            });
            oneQues['refer_exampapers'] = newRefer

            let quesList = knowTypeQues[knowTypeName] || []
            quesList.push(oneQues)
            knowTypeQues[knowTypeName] = quesList

            // 选够停止
            targetNum = targetNum - 1;
            if (targetNum == 0){
                break
            }
        }
    }

    // 专题结构
    let blocks = []
    knowledges.forEach(knowDict => {
        let knowId = knowDict.id || ''
        let knowName = knowDict.name || ''
        // 获取试题
        let questions = []
        let quesType = knowDict.ques_type || [];
        quesType.forEach(typeDict => {
            let typeName = typeDict.name || ''
            questions = questions.concat(knowTypeQues[`${knowName}__${typeName}`] || [])
        })
        if (!questions.length==0){
            questions = _.reverse(_.sortBy(questions, function(it) {
                return it.type
            }))
            let block = {
                'id': knowId,
                'name': knowName,
                'title': `${knowTitlePrefix[blocks.length]}：${knowName}`,
                'note': '',
                'type': '',
                'default_score': 0,
                'questions': questions
            }
            blocks.push(block)
        }
    })

    let dateTime = new Date()
    let exampaper = {
        user_id: user_id,
        period: req.body.period,
        subject: req.body.subject,
        grade: '',
        press_version: '',
        knowledge_tree: '',
        type: '',
        name: `考情分析专题练习卷（${dateTime.getFullYear()}年${dateTime.getMonth()}月${dateTime.getDate()}日）`,
        subtitle: '',
        score: 0,
        duration: 0,
        paper_info: '',
        cand_info: '',
        score_info: '',
        attentions: '',
        secret_tag: '',
        gutter: 0,
        display: 0,
        ctime: dateTime,
        utime: dateTime,
        volumes:[{
            title: '',
            note: '',
            blocks: blocks
        }]
    }
    return resWrapper.succ(exampaper)
}


const getQuesDiff = (diff) => {
    let diffName = '中等,较难,较易'
    if (0 < diff <= 0.35){
        diffName = '困难,较难,中等'
    } else if  (0.35 < diff <= 0.55){
        diffName = '较难,中等,较易'
    } else if  (0.55 < diff <= 0.7){
        diffName = '中等,较难,较易'
    } else if  (0.7 < diff <= 0.85){
        diffName = '较易,容易,中等'
    } else {
        diffName = '容易,较易,中等'
    }
    return diffName
}

async function filterRepeatQues(quesIds) {
    try {
        let body = {}
        body['sim_ques_ids'] = quesIds
        // 全部试题查重过滤
        let server = config.get('AI_KB_SERVER');
        let url = URL.format({
            protocol: server.protocol,
            hostname: server.hostname,
            port: server.port,
            pathname: 'ai_dispatch/v1/exam/ctb_dup',
            search: qs.stringify({
                appKey: server.appKey,
            })
        });
        let saveQues = []
        let filterQues = [];
        let result = await axios.post(url, JSON.stringify([body]));
        if (result.data && result.data.code == 0 && result.data.data){
            let repeatArray = result.data.data.repeat_array || [];
            repeatArray.forEach(element => {
                let repeatQues = [];
                // 一组重复的试题索引集合
                element.forEach(indexDouble => {
                    indexDouble = indexDouble.replace(':', '')
                    let indexList = indexDouble.split('_')
                    indexList.forEach(index => {
                        if (!repeatQues.includes(index)){
                            repeatQues.push(index)
                        }
                    })
                });
                // 保留最后一个索引，删除之前的索引
                if (repeatQues.length != 0){
                    filterQues = filterQues.concat(repeatQues.slice(0, -1));
                }
            })
        }

        // 过滤后保留的试题
        if (filterQues.length != 0){
            for (let i=0; i< quesIds.length; i++){
                if (filterQues.includes(i.toString())){
                    continue
                }
                saveQues.push(quesIds[i])
            }
        }
        if (saveQues.length == 0){
            saveQues = quesIds
        }
        return saveQues;
    } catch (err) {
        logger.error(err);
        return quesIds;
    }
}


async function SearchQuestions(params) {
    let server = config.get('KB_API_SERVER');
    let url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        pathname: '/kb_api/v2/questions/by_search',
        port: server.port,
        search: qs.stringify({
            api_key: server.appKey,
        })
    });
    let opstions = { headers: {}, timeout: 50000 };
    let result = await axios.post(url, params, opstions);
    if (!result.data) {
        logger.error(`搜索试题失败,url:[${url}], params：${JSON.stringify(params)}`);
        return null;
    }
    return _.get(result, 'data', null);
}

module.exports = {
    generatePaper: generatePaper
}