var crypto = require('crypto');
var qs = require('qs');

var url = require('url');
var _ = require('underscore');
var config = require('config');

var ResponseWrapper = require('../../middlewares/response_wrapper');
var getExampaperFromTiku = require('./exampaper').get_exampaper_from_tiku;

function OTQ(otqType, opCount, score, shaoXuan){
    this.otqType = otqType ? otqType : 0;
    this.opCount = opCount ? opCount : 1;
    this.score = score ? score : 3;
    this.shaoXuan = shaoXuan ? shaoXuan : 0;
}

function BFQ(score, vacate){
    this.score = score ? score : 3;
    this.vacate = vacate ? vacate : [];
}

function Other(score, rows){
    this.score = score ? score : 3;
    this.rows = rows ? rows : [];
}

function Question(groupId, title, type, score) {
    this.groupId = groupId;
    this.title = title;
    if (type == '选择题') {
        type = 'OTQ';
    }else if(type == '填空题') {
        type = 'BFQ';
    }else if(type == '写作'){
        type = 'CPQ';
    }else if(type.indexOf('表达') >= 0) {
        type = 'ECPQ';
    }else {
        type = 'SAQ';
    }
    this.type = type;
    this.score = score;
    this.items = [];
}

Question.prototype.setItems = function(question) {
    this.items = [];
    var multiQuestion = false;
    if(!question.blocks) {
        throw new TypeError('question并不是一个合法的结构');
    }
    if(!Array.isArray(question.blocks.stems) || question.blocks.stems.length === 0){
        throw new TypeError('question的子题不能为空');
    }
    try{
        var score = question.score / question.blocks.stems.length;
        for(var i in question.blocks.stems) {
            var type = question.blocks.types[i];
            if(question.blocks.stems[i].options) {
                var otqType = 1;
                var shaoXuan = 0;
                var opCount = Object.keys(question.blocks.stems[i]).length;
                if (type.indexOf('多') == 0){
                    otqType = 2;
                    shaoXuan = parseInt(score / 3);
                }
                this.items.push(new OTQ(otqType, opCount, score, shaoXuan));
            } else if(type.indexOf('填空') == 0) {
                var vacate = [];
                if(Array.isArray(question.blocks.answers[i])){
                    for(var x in question.blocks.answers[i]){
                        var answer = question.blocks.answers[i][x];
                        var v = [];
                        for(var ix in answer){
                            v.push(answer[ix].length);
                        }
                        vacate.push(v);
                    }
                }else{
                    vacate.push([question.blocks.answers[i].length]);
                }   
                this.items.push(new BFQ(score, vacate));
            } else if(type.indexOf('写作') >= 0) {
                var letter = _.range(0, 40).map(function(x){ return 20;});
                this.items.push(new Other(score, letter));
            } else if(type.indexOf('书面表达') >= 0) {
                var letter = _.range(0, 10).map(function(x){ return 20;});
                this.items.push(new Other(score, letter));
            } else {        
                var answer = question.blocks.answers[i];
                answer = answer.replace(/<p/g, '<br')
                var lines = answer.split('<br');
                var rows = lines.map(function(x){
                    return x.length;
                });
                this.items.push(new Other(score, rows));
            }
        }
    }catch(err){
        throw err;
    }
    
}

function Scantron(name){
    this.name = name;
    this.questions = [];
}

function block2question(id, block){
    var question = new Question(id, block.title, block.type);
    for(var x in block.questions){
        var question = block.questions[x];
        
    }
}

function exam2scan(exampaper){
    var scantron = new Scantron(exampaper.name);
    var blocks = _.flatten(_.pluck(exampaper.volumes, 'blocks'));
    for(var bx in blocks){
        var block = blocks[bx];
        var groupId = (bx * 1) + 1;
        var title = block.title;
        
        for(var qx in block.questions){
            var ques = block.questions[qx];
            var question = new Question(groupId, title, ques.type, ques.score);
            question.setItems(ques);
            scantron.questions.push(question);
        }
    }
    return scantron;
}

function getScantron(req, res) {
    var exampaperId = req.params.exampaper_id;
    var userId = req.user.id;
    var responseWrapper = new ResponseWrapper(req, res);
    return getExampaperFromTiku(userId, exampaperId, function(err, exampaper){
        if(err){
            return responseWrapper.error(err);
        }
        var scantron = exam2scan(exampaper);
        return responseWrapper.succ(scantron);
    });
}

function _GetDTKGateway(user, callback){
    
    var query = {
        id: user.id,
        name: user.name,
        schoolId: user.schoolId,
        schoolName: user.schoolName,
        timestamp: Date.now()
    }

    var token = '';
    token += user.id;
    token += query.timestamp;
	token += config.get('SCANTRON.sk');
    var md5 = crypto.createHash('md5');
    var dtk = md5.update(token).digest('hex');
    query.dtk = dtk;
    return callback(null, query);
}

function getDTKGateway(req, res){
    var responseWrapper = new ResponseWrapper(req, res);
    var user = req.user;
	var scantronUrl = url.format({
		protocol: config.get('SCANTRON').protocol,		
		hostname: config.get('SCANTRON').hostname,		
		port: config.get('SCANTRON').port
	});
    return _GetDTKGateway(user, function(err, query){
        return responseWrapper.succ({
			url: scantronUrl,
            query: qs.stringify(query),
            input: query
        })
    });
}

module.exports = {
    getScantron: getScantron,
    getDTKGateway: getDTKGateway,
    _GetDTKGateway,
}
