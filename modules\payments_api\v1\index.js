const mongodber = require('../../utils/mongodber');
const ResponseWrapper = require('../../middlewares/response_wrapper');
const logger = require('../../utils/logger');
const enums = require('../../../bin/enum');
const Payment = require('../../utils/wx_pay');
const user_log_service = require('../../user_api/v1/user_log_service');
const user_right_service = require('../../user_api/v2/user_right_service');
const client = require('../../client/index');
const schema = require('../../../bin/schema');
const resource_service = require('../../resource/resource_service');
const notice_message = require('../../notice_message_api/index');


const ObjectId = require('mongodb').ObjectId;
const db = mongodber.use('tiku');
const _ = require('lodash');
const Joi = require('@hapi/joi');
const config = require('config');
const util = require('util');
const moment = require('moment');
const fs = require('fs');
const uuid = require('uuid');
const { create, all } = require('mathjs');
const utils = require('../../utils/utils');
const mathConfig = {
    number: 'BigNumber',
};
const math = create(all, mathConfig);

const postPaymentsSchema = Joi.object({
    goods_id: Joi.string().required(),
    amount: Joi.number().required(),
    pay_through: Joi.string().valid(...Object.values(enums.PayThrough)).required(),
    resource_id: Joi.string().optional(),
    user_coupon_id: Joi.string().optional(),
});

const postPayment = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let { resource_id, goods_id, amount, pay_through,user_coupon_id } = await Joi.validate(req.body, postPaymentsSchema);

        const userId = req.user.id;
        const vipType = req.user.vipType;
        if (!resource_id && vipType && Object.values(enums.SchoolVipType).includes(vipType)) {
            throw new Error('阅卷关联校成员无需购买');
        }
        if (!userId) {
            throw new Error('用户信息错误');
        }
        // const ucId = req.user.ucId;
        const userInfo = await db.collection('user').findOne({ _id: userId });
        if (!userInfo) {
            throw new Error('用户信息错误');
        }
        const cond = {
            user_id: userId,
            status: enums.TransactionStatus.PROCESSING, // 支付中
            out_time: { // 未超时
                $gt: new Date()
            }
        }
        const transactionList = await db.collection('@TransactionRecord').find(cond).toArray();
        if (_.size(transactionList)) {
            throw new Error('当前有未支付订单!');
        }
        // 检查商品配置和价格
        const goodsInfo = await db.collection('@Goods').findOne({ _id: ObjectId(goods_id) });
        // if (goodsInfo.final_price !== amount) {
        //     throw new Error('价格错误');
        // }
        amount = goodsInfo.final_price;
        if (!goodsInfo.resource_type && resource_id) {
            throw new Error('商品错误，请联系研发处理');
        }
        if (goodsInfo.type === enums.GoodsType.DOWNLOAD && !resource_id) {
            throw new Error('参数错误，需要resource_id');
        }
        let coupon_fee = 0;
        let userCoupon = null;
        if (user_coupon_id) {
            userCoupon = await db.collection('user_coupons').findOne({ _id: ObjectId(user_coupon_id) });
            if (_.isEmpty(userCoupon) || userCoupon.user_id !== userId) {
                throw new Error('参数错误，优惠券不存在!');
            }
            // 优惠券是否可用
            if (userCoupon.usage_status !== enums.BooleanNumber.NO // 已使用
                || new Date().getTime() < userCoupon.valid_from.getTime() // 不在有效期内
                || new Date().getTime() > userCoupon.valid_to.getTime() // 不在有效期内
                || (_.size(userCoupon.target_goods) && !userCoupon.target_goods.find(it => it.id === goods_id)) // 限定商品
            ) {
                throw new Error('优惠券不可用!');
            }
            // 计算减免
            if (userCoupon.discount_type === 1) { // 减免固定金额
                coupon_fee = userCoupon.discount_count;
            } else if (userCoupon.discount_type === 2) { // 折扣
                coupon_fee = math.evaluate(`${amount} * (100 - ${userCoupon.discount_count}) / 100`).round().toNumber();
            }
            amount = amount - coupon_fee;
            if (amount <= 0) { // 最低支付金额
                amount = 1;
            }
        }

        // let userInfo;
        // if (ucId) userInfo = await db.collection('user').findOne({ uc_id: ucId, is_vip: true });
        // else userInfo = await db.collection('user').findOne({ _id: userId });
        // // 会员时间不可大于1年
        // if (userInfo && moment(userInfo.expired_time).add(goodsInfo.month, 'month').startOf('day').toDate() > moment().add(1, 'years').startOf('day').toDate()) {
        //     throw new Error('会员时长超过限制');
        // }

        // 发起支付
        const payment = await client.wly.getPayment();
        if (_.isEmpty(payment)) throw new Error('当前支付不可用');

        const tradeNo = _genOrderUUID();

        let qrUrl;
        let result = await payment.native({
            description: resource_id ? '好分数题库下载-支付' : '好分数题库会员-支付',
            out_trade_no: tradeNo,
            amount: {
                total: amount
            }
        });
        if (result.status !== 200) {
            logger.error(JSON.stringify(result));
            throw new Error('未获取到支付二维码');
        }
        qrUrl = result.data && JSON.parse(result.data).code_url;

        const now = new Date();
        let insertData = {
            ctime: now,
            utime: now,
            goods_id: goods_id,

            status: enums.TransactionStatus.PROCESSING,
            amount: amount,
            pay_through: pay_through,
            order: tradeNo,
            // remark: String,
            user_id: userId,
            qr_url: qrUrl,
            out_time: moment().add(1, 'hours').toDate()
        };
        if (goodsInfo.resource_type) insertData.resource_type = goodsInfo.resource_type;
        if (resource_id) insertData.resource_id = resource_id;
        if (goodsInfo.month) insertData.month = goodsInfo.month;
        if (goodsInfo.count) insertData.count = goodsInfo.count;
        if (userCoupon) { // 优惠券信息
            insertData.user_coupon_id = user_coupon_id; // 用户优惠券ID
            insertData.coupon_id = userCoupon.coupon_id; // 优惠券ID
            insertData.coupon_name = userCoupon.coupon_name; // 优惠券名称
            insertData.coupon_fee = coupon_fee; // 优惠金额
        }
        const insertResult = await db.collection('@TransactionRecord').insertOne(insertData);
        insertResult._id = insertResult.insertedId;
        await _hanldeCoupon(insertData, enums.TransactionStatus.PROCESSING);

        return responseWrapper.succ({
            transaction_id: insertResult.insertedId.toString(),
            qr_url: qrUrl,
        });
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

const POST_PAYMENT_V2 = Joi.object({
    sku_id: Joi.string().required(),
    pay_through: Joi.string().valid(...Object.values(enums.PayThrough)).required(),
    user_coupon_id: Joi.string().optional(),
    resource: Joi.object({
        id: [Joi.number().required(), Joi.string().required()],
        type: Joi.string().required(),
        parent_id: Joi.string().optional().allow('')
    }).optional()
});


const postPaymentV2 = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let { sku_id, resource, pay_through, user_coupon_id } = await Joi.validate(req.body, POST_PAYMENT_V2);
        const userId = req.user.id;
        const sku = await db.collection(schema.pms_sku).findOne({_id: new ObjectId(sku_id)});
        if (_.isEmpty(sku) || sku.status === enums.BooleanNumber.NO) {
            return responseWrapper.error('PARAMETERS_ERROR', '商品信息不存在');
        }
        const spu = await db.collection(schema.pms_spu).findOne({_id: new ObjectId(sku.spu_id)});
        if (!Object.values(enums.NewVipType).includes(spu.code) && _.isEmpty(resource)) {
            return responseWrapper.error('PARAMETERS_ERROR', '资源信息不能为空');
        }
        // 检查商品配置和价格
        let amount = sku.final_price;
        let discount = 0;
        let discount_fee = 0;
        // 校验商品类型
        if (!_.isEmpty(resource)) {
            //
            // 校验资源状态
            const data = await resource_service.getById(resource);
            if (_.isEmpty(data)) {
                return responseWrapper.error('PARAMETERS_ERROR', '资源信息不存在');
            }
            // 是否有下载功能
            const fun_params = {
                type: enums.NewRightType.resource_download_fun,
                period: data.period,
                subject: data.subject
            };
            const fun_status = await user_right_service.get_fun_right_status(req.user.id, fun_params);
            if (!fun_status) return responseWrapper.error('NEED_VIP_ERROR', '需要购买会员后下载');
            const right_params = {
                type: enums.NewRightType.download_discount_num,
                period: data.period,
                subject: data.subject
            }
            discount = await user_right_service.get_right_value_min(req.user.id, right_params);
            if (discount > 0) {
                discount_fee =  math.evaluate(`${amount} * (100 - ${discount}) / 100`).round().toNumber();
                amount = amount - discount_fee;
            }
        }

        // 计算价格
        // 如果是资源下载购买，计算会员折扣
        // 计算优惠券

        let coupon_fee = 0;
        let userCoupon = null;
        if (user_coupon_id) {
            userCoupon = await db.collection(schema.user_coupons).findOne({ _id: ObjectId(user_coupon_id) });
            if (_.isEmpty(userCoupon) || userCoupon.user_id !== userId) {
                return responseWrapper.error('PARAMETERS_ERROR', '优惠券不存在');
            }
            // 优惠券是否可用
            if (userCoupon.usage_status !== enums.BooleanNumber.NO // 已使用
                || new Date().getTime() < userCoupon.valid_from.getTime() // 不在有效期内
                || new Date().getTime() > userCoupon.valid_to.getTime() // 不在有效期内
                || (_.size(userCoupon.target_goods) && !userCoupon.target_goods.find(it => it.id === spu._id.toString())) // 限定商品
            ) {
                return responseWrapper.error('PARAMETERS_ERROR', '优惠券不可用');
            }
            // 计算减免
            if (userCoupon.discount_type === 1) { // 减免固定金额
                coupon_fee = userCoupon.discount_count;
            } else if (userCoupon.discount_type === 2) { // 折扣
                coupon_fee = math.evaluate(`${amount} * (100 - ${userCoupon.discount_count}) / 100`).round().toNumber();
            }
            amount = amount - coupon_fee;
        }
        amount = amount <= 0 ? 1 : amount;
        // 发起支付
        const env = process.env.NODE_ENV;
        let qrUrl = '';
        const tradeNo = _genOrderUUID();
        if (env === 'test') {
            qrUrl = 'test';
        } else {
            const payment = await client.wly.getPayment();
            if (_.isEmpty(payment)) return responseWrapper.error('HANDLE_ERROR', '当前支付不可用');
            let description = spu.name;
            if (resource) {
                description += ' ' + resource.id;
            } else {
                description += ' ' +sku.name;
            }
            let result = await payment.native({
                description: description,
                out_trade_no: tradeNo,
                amount: {
                    total: amount
                }
            });
            if (result.status !== 200) {
                logger.error(JSON.stringify(result));
                return responseWrapper.error('HANDLE_ERROR', '未获取到支付二维码');
            }
            qrUrl = result.data && JSON.parse(result.data).code_url;
        }
        const now = new Date();
        let insertData = {
            ctime: now,
            utime: now,
            spu_id: spu._id.toString(),
            spu_name: spu.name,
            sku_id: sku._id.toString(),
            sku_name: sku.name,
            status: enums.TransactionStatus.PROCESSING,
            amount: amount,
            pay_through: pay_through,
            order: tradeNo,
            user_id: userId,
            qr_url: qrUrl,
            out_time: moment().add(1, 'hours').toDate()
        };
        if (resource) {
            insertData.resource_type = resource.type;
            insertData.resource_id = resource.id;
            insertData.resource_parent_id = resource.parent_id;
        }
        if (userCoupon) { // 优惠券信息
            insertData.user_coupon_id = user_coupon_id; // 用户优惠券ID
            insertData.coupon_id = userCoupon['coupon_id']; // 优惠券ID
            insertData.coupon_name = userCoupon['coupon_name']; // 优惠券名称
            insertData.coupon_fee = coupon_fee; // 优惠金额
        }
        insertData.discount = discount;
        insertData.discount_fee = discount_fee;

        const insertResult = await db.collection('@TransactionRecord').insertOne(insertData);
        insertResult._id = insertResult.insertedId;
        await _hanldeCoupon(insertData, enums.TransactionStatus.PROCESSING);

        return responseWrapper.succ({
            transaction_id: insertResult.insertedId.toString(),
            qr_url: qrUrl,
        });
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

const JOI_POST_PAYMENT_RESOURCE_H5 = Joi.array().items(Joi.object({
    type: Joi.number().required(), // 类型
    resource_id: [Joi.number(), Joi.string()], // 资源ID
})).required().min(1)

const postPaymentResourceH5 = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let params = await Joi.validate(req.body, JOI_POST_PAYMENT_RESOURCE_H5);
        const user_id = req.user.id;
        const cart = await db.collection(schema.user_resource_cart).findOne({user_id: user_id});
        // 过滤
        // 付费策略：5份以内0.99元，超过5份每份按照0.5元计费
        const order = await db.collection('@Order').findOne({user_id: user_id, status: enums.OrderStatus.SUCCESS, type: 'resource'});
        const count = _.size(params);
        const price = 50; // 单价
        let amount = 0, discount = 0, discount_fee = 0, coupon_fee = 0, userCoupon = null;
        if (_.isEmpty(order)) {
            const base_count = 5;
            amount = 99; // 5份以内 0.99 元
            if (count > base_count) { // 超过5份
                amount += (count - base_count) * price;
            }
        } else {
            amount = count * price;
        }
        // 发起支付
        const env = process.env.NODE_ENV;
        let h5_url = '';
        const tradeNo = _genOrderUUID();
        if (env === 'test') {
            h5_url = 'test';
        } else {
            const payment = await client.wly.getPaymentByMchId(config.get('payment.wechatPay.h5.mch_id'));
            if (_.isEmpty(payment)) return responseWrapper.error('HANDLE_ERROR', '当前支付不可用');
            let description = `资源下载支付`;
            let result = await payment.h5({
                description: description,
                out_trade_no: tradeNo,
                amount: {
                    total: amount
                },
                scene_info: { // 场景信息
                    payer_client_ip: utils.getClientIp(req),
                    h5_info: {
                        type: 'Wap' // Wap、IOS、Android、WindowsPhone等；
                    }
                }
            });
            if (result.status !== 200) {
                logger.error(JSON.stringify(result));
                return responseWrapper.error('HANDLE_ERROR', '未获取到支付链接');
            }
            h5_url = result.data && JSON.parse(result.data).h5_url;
        }

        // 订单数据处理
        const now = new Date();
        let insertData = {
            ctime: now,
            utime: now,
            goods_id: '0',
            goods_name: '资源下载',
            items: cart.items.filter(e => params.find(p => p.type === e.type && p.resource_id === e.resource_id)),
            status: enums.TransactionStatus.PROCESSING,
            amount: amount,
            pay_through: enums.PayThrough.WECHAT_PAY_H5,
            order: tradeNo,
            user_id: user_id,
            h5_url: h5_url,
            out_time: moment().add(5, 'minute').toDate()
        };

        insertData.discount = discount;
        insertData.discount_fee = discount_fee;
        const insertResult = await db.collection('@TransactionRecord').insertOne(insertData);
        insertResult._id = insertResult.insertedId;
        // 清理购物车数据
        // const items = cart.items.filter(e => !params.find(p => p.type === e.type && p.resource_id === e.resource_id));
        // await db.collection(schema.user_resource_cart).updateOne({user_id: user_id}, {$set: {items: items}});
        return responseWrapper.succ({
            transaction_id: insertResult.insertedId.toString(),
            h5_url: h5_url,
        });
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};


const JOI_POST_PAYMENT_RESOURCE_APP = Joi.array().items(Joi.object({
    type: Joi.number().required(), // 类型
    resource_id: [Joi.number(), Joi.string()], // 资源ID
})).required().min(1)

const postPaymentResourceApp = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let params = await Joi.validate(req.body, JOI_POST_PAYMENT_RESOURCE_APP);
        const user_id = req.user.id;
        const cart = await db.collection(schema.user_resource_cart).findOne({user_id: user_id});
        // 过滤
        // 付费策略：5份以内0.99元，超过5份每份按照0.5元计费
        const order = await db.collection('@Order').findOne({user_id: user_id, status: enums.OrderStatus.SUCCESS, type: 'resource'});
        const count = _.size(params);
        const price = 50; // 单价
        let amount = 0, discount = 0, discount_fee = 0, coupon_fee = 0, userCoupon = null;
        
        if (_.isEmpty(order)) {
            const base_count = 5;
            amount = 99; // 5份以内 0.99 元
            if (count > base_count) { // 超过5份
                amount += (count - base_count) * price;
            }
        } else {
            amount = count * price;
        }
        
        // 发起支付
        let app_params = {};
        const tradeNo = _genOrderUUID();
        
        const payment = await client.wly.getPaymentByMchId(config.get('payment.wechatPay.app.mch_id'));
        if (_.isEmpty(payment)) return responseWrapper.error('HANDLE_ERROR', '当前支付不可用');
        
        let description = `资源下载支付`;
        let result = await payment.app({
            description: description,
            out_trade_no: tradeNo,
            amount: {
                total: amount
            }
        });
        
        if (result.status !== 200) {
            logger.error(JSON.stringify(result));
            return responseWrapper.error('HANDLE_ERROR', '未获取到支付参数');
        }
        
        app_params = JSON.parse(result.data);

        // 订单数据处理
        const now = new Date();
        let insertData = {
            ctime: now,
            utime: now,
            goods_id: '0',
            goods_name: '资源下载',
            items: cart.items.filter(e => params.find(p => p.type === e.type && p.resource_id === e.resource_id)),
            status: enums.TransactionStatus.PROCESSING,
            amount: amount,
            pay_through: enums.PayThrough.WECHAT_PAY_APP,
            order: tradeNo,
            user_id: user_id,
            app_params: app_params,
            out_time: moment().add(5, 'minute').toDate()
        };

        insertData.discount = discount;
        insertData.discount_fee = discount_fee;
        
        const insertResult = await db.collection('@TransactionRecord').insertOne(insertData);
        insertResult._id = insertResult.insertedId;
        const curAppParams = {
            appId: payment.appid,
            nonceStr: payment.generate(32),
            prepayId: app_params.prepay_id,
            timeStamp: parseInt(Date.now() / 1000),
        }
        curAppParams.sign = payment.rsaSign(`${curAppParams.appId}\n${curAppParams.timeStamp}\n${curAppParams.nonceStr}\n${curAppParams.prepayId}\n`, payment.private_key);
        curAppParams.partnerId = payment.mchid
        curAppParams.packageValue = 'Sign=WXPay'

        // console.log(JSON.stringify(curAppParams))
        return responseWrapper.succ({
            transaction_id: insertResult.insertedId.toString(),
            app_params: curAppParams,
        });
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};


const getPaymentStatusSchema = Joi.object({
    id: Joi.string().required(),
}).unknown(true);
/**
 * 获取支付状态
 */
const getPaymentStatus = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const { id } = await Joi.validate(req.query, getPaymentStatusSchema);

        const recordInfo = await db.collection('@TransactionRecord').findOne({ _id: ObjectId(id) });

        if (!recordInfo) {
            throw new Error('id错误');
        }

        return responseWrapper.succ({
            status: recordInfo.status,
        });
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

const postNotificationQrSchema = Joi.object({
    resource: Joi.object().required(),
}).unknown(true);
/**
 * 提交支付回调 微信支付 QR
 */
const postNotificationQr = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const { resource } = await Joi.validate(req.body, postNotificationQrSchema);
        const now = new Date();

        const payment = await client.wly.getPayment();
        const notification = payment.decodeResource(resource);
        const tradeNo = notification.out_trade_no;

        const transactionRecord = await db.collection('@TransactionRecord').findOne({ order: tradeNo });
        if (!transactionRecord) {
            throw new Error('找不到交易记录');
        }

        if (transactionRecord.status !== enums.TransactionStatus.PROCESSING) {
            throw new Error('交易记录状态错误');
        }

        if (notification.amount && notification.amount.total !== transactionRecord.amount) {
            throw new Error('交易记录金额错误');
        }

        const userInfo = await db.collection('user').findOne({ _id: transactionRecord.user_id });
        if (!userInfo) {
            throw new Error('找不到用户');
        }
        const userId = userInfo._id;
        const ucId = userInfo.uc_id;

        // 交易完成
        await db.collection('@TransactionRecord').updateOne({ _id: transactionRecord._id }, {
            $set: {
                notify_time: now,
                status: notification.trade_state === 'SUCCESS' ? enums.TransactionStatus.SUCCESS : enums.TransactionStatus.FAILED
            },
        });

        if (notification.trade_state !== 'SUCCESS') {
            return responseWrapper.succ(null);
        }
        // 优惠券标记使用
        await _hanldeCoupon(transactionRecord, enums.TransactionStatus.SUCCESS);

        const goodsInfo = await db.collection('@Goods').findOne({ _id: ObjectId(transactionRecord.goods_id) });
        if (!goodsInfo) {
            throw new Error('交易商品错误');
        }

        const orderNo = _createOrderNo();
        let goods = {
            id: goodsInfo._id.toString(),
            name: goodsInfo.name,
        };
        if (goodsInfo.type === enums.GoodsType.DOWNLOAD) {
            goods.resource_type = transactionRecord.resource_type;
            goods.resource_id = transactionRecord.resource_id;
        } else {
            goods.month = goodsInfo.month;
            goods.count = goodsInfo.count;
        }
        const insertOrder = {
            ctime: now,
            utime: now,

            no: orderNo,
            user_id: userId,
            uc_id: ucId,
            status: enums.OrderStatus.DOING,
            type: goodsInfo.type,

            original_price: goodsInfo.original_price,
            discount_price: goodsInfo.discount_price,
            final_price: goodsInfo.final_price,
            transaction_id: transactionRecord._id.toString(),
            pay_through: transactionRecord.pay_through,

            goods: goods,
        };
        // 处理优惠券
        if (transactionRecord.user_coupon_id) {
            insertOrder.user_coupon_id = transactionRecord.user_coupon_id; // 用户优惠券ID
            insertOrder.coupon_id = transactionRecord.coupon_id; // 优惠券ID
            insertOrder.coupon_name = transactionRecord.coupon_name; // 优惠券名称
            insertOrder.coupon_fee = transactionRecord.coupon_fee; // 优惠金额
        }
        const orderInsertResult = await db.collection('@Order').insertOne(insertOrder);
        insertOrder._id = orderInsertResult.insertedId;
        const orderId = insertOrder._id.toString();

        if (goodsInfo.type === enums.GoodsType.DOWNLOAD) {
            if (transactionRecord.resource_type === enums.ResourceType.ASSEMBLE_EXAMPAPER) {
                await db.collection('user').updateOne({ _id: userId }, {
                    $set: { use_time: new Date() },
                    $inc: { assemble_download_num: 1 }
                });
                await db.collection('exampaper').update({
                    _id: new ObjectId(transactionRecord.resource_id)
                }, { $set: { expired_time: moment().add(1, 'month').startOf('day').toDate() } });
            } else {
                await db.collection('user').updateOne({ _id: userId }, {
                    $set: { use_time: new Date() },
                    $inc: {
                        que_download_num: transactionRecord.resource_type === enums.ResourceType.QUESTION ? 1 : 0,
                        exampaper_download_num: transactionRecord.resource_type === enums.ResourceType.EXAMPAPER ? 1 : 0,
                    }
                });

                let record = await db.collection('user_download').findOne({ user_id: userId, resource_type: transactionRecord.resource_type, resource_id: Number(transactionRecord.resource_id) });
                if (record) {
                    await db.collection('user_download').update({
                        _id: record._id,
                    }, { $set: { expired_time: moment().add(1, 'month').startOf('day').toDate() } });
                } else {
                    if (transactionRecord.resource_type === enums.ResourceType.EDU_ASSISTANT_FILE) {
                        const eduFile = await queryEduFileById(Number(transactionRecord.resource_id));
                        let tem = {
                            user_id: userId,
                            sch_id: userInfo.school_id,
                            resource_type: transactionRecord.resource_type,
                            resource_id: eduFile.id,
                            download_times: 0,
                            ctime: new Date(),
                            utime: new Date(),
                            expired_time: moment().add(1, 'month').startOf('day').toDate(),
                            type: eduFile.category,
                            period: eduFile.period,                           // 学段
                            subject: eduFile.subject,                         // 学科
                            press_version: eduFile.press_version,             // 教材版本
                            book_id: eduFile.book_id,                         // 教材ID
                            chapter_id: eduFile.chapter_id,                   // 章节id
                        };
                        await db.collection('user_download').insertOne(tem);
                    } else {
                        let infos = await queryResourcesByType([Number(transactionRecord.resource_id)], transactionRecord.resource_type);
                        let newArr = _.map(infos, (info) => {
                            const saveKeys = ['period', 'subject', 'type', 'grade', 'press_version'];
                            let tem = {
                                user_id: userId,
                                sch_id: userInfo.school_id,
                                resource_type: transactionRecord.resource_type,
                                download_times: 0,
                                ctime: new Date(),
                                utime: new Date(),
                                expired_time: moment().add(1, 'month').startOf('day').toDate()
                            };
                            let t = _.clone(tem);
                            _.each(saveKeys, (key) => {
                                if (!info[key]) {
                                    return;
                                }
                                t[key] = info[key];
                            });
                            t.resource_id = info.id;
                            return t;
                        });
                        await db.collection('user_download').insertMany(newArr);
                    }
                }
            }
        } else {
            // 生成下载券
            let noCount = 0;
            let ticketItem = [];
            for (let index = 0; index < goodsInfo.count; index++) {
                let curNow = new Date();
                let item = {
                    ctime: curNow,
                    utime: curNow,

                    status: enums.TicketItemStatus.AVAILABLE,
                    user_id: userId,
                    uc_id: ucId,
                    no: Math.floor(`${curNow.getTime()}${++noCount}`),
                    order: orderId,
                };
                ticketItem.push(item);
            }
            await db.collection('@TicketItem').insertMany(ticketItem);

            // 增加用户会员权益
            let expireTime;
            let start_time = userInfo && userInfo.start_time;
            if (!start_time || moment().isAfter(userInfo.expired_time)) { // 没有开始时间或者会员已过期
                start_time = moment().startOf('day').toDate();
            }
            if (userInfo && new Date(userInfo.expired_time) > new Date()) {
                expireTime = moment(userInfo.expired_time).add(transactionRecord.month, 'month').toDate();
            } else {
                expireTime = moment().add(transactionRecord.month, 'month').startOf('day').toDate();
            }

            await db.collection('user').updateOne({ _id: userId }, {
                $set: {
                    start_time: start_time,
                    expired_time: expireTime,
                    is_vip: true,
                    vip_type: _.get(insertOrder, 'goods.id'),
                    vip_rights: [{ download_num: _.get(insertOrder, 'goods.count', 0)}]
                },
                $setOnInsert: {
                    utime: now,
                    _id: userId,
                    uc_id: ucId,
                },
            }, {
                upsert: true
            });
            // 记录日志
            await user_log_service.openVip(userId, insertOrder, expireTime);
        }
        // 完成订单
        await db.collection('@Order').updateOne({ _id: ObjectId(orderId) }, {
            $set: {
                status: enums.OrderStatus.SUCCESS,
            },
        });

        return responseWrapper.succ(null);
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

const postNotificationQrV2 = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const { resource } = await Joi.validate(req.body, postNotificationQrSchema);
        const now = new Date();

        const payment = await client.wly.getPayment();
        const notification = payment.decodeResource(resource);
        const tradeNo = notification.out_trade_no;

        const transactionRecord = await db.collection('@TransactionRecord').findOne({ order: tradeNo });
        if (!transactionRecord) {
            throw new Error('找不到交易记录');
        }

        if (transactionRecord.status !== enums.TransactionStatus.PROCESSING) {
            throw new Error('交易记录状态错误');
        }

        if (notification.amount && notification.amount.total !== transactionRecord.amount) {
            throw new Error('交易记录金额错误');
        }

        const userInfo = await db.collection('user').findOne({ _id: transactionRecord.user_id });
        if (!userInfo) {
            throw new Error('找不到用户');
        }
        const userId = userInfo._id;
        const ucId = userInfo.uc_id;

        // 交易完成
        await db.collection('@TransactionRecord').updateOne({ _id: transactionRecord._id }, {
            $set: {
                notify_time: now,
                status: notification.trade_state === 'SUCCESS' ? enums.TransactionStatus.SUCCESS : enums.TransactionStatus.FAILED
            }
        });

        if (notification.trade_state !== 'SUCCESS') {
            return responseWrapper.succ(null);
        }
        // 优惠券标记使用
        await _hanldeCoupon(transactionRecord, enums.TransactionStatus.SUCCESS);
        let target = null;
        if (transactionRecord.resource_id) {
            target = {
                id: transactionRecord.resource_id,
                type: transactionRecord.resource_type,
                parent_id: transactionRecord.resource_parent_id,
            }
        }

        const sku = await db.collection('pms_sku').findOne({_id: new ObjectId(transactionRecord.sku_id)});
        const spu = await db.collection('pms_spu').findOne({_id: new ObjectId(sku.spu_id)});
        const orderNo = _createOrderNo();
        let goods = {
            spu: {
                id: transactionRecord.spu_id,
                name: transactionRecord.spu_name,
            },
            sku: {
                id: transactionRecord.sku_id,
                name: transactionRecord.sku_name,
                key: sku.key,
                specs: sku.specs,
                rights: sku.rights
            }
        };

        const insertOrder = {
            ctime: now,
            utime: now,
            no: orderNo,
            user_id: userId,
            uc_id: ucId,
            status: enums.OrderStatus.DOING,
            type: spu.code,
            original_price: sku.original_price,
            discount_price: sku.discount_price,
            final_price: sku.final_price,
            transaction_id: transactionRecord._id.toString(),
            pay_through: transactionRecord.pay_through,
            goods: goods,
            target: target,
            discount: transactionRecord.discount || 0,
            discount_fee: transactionRecord.discount_fee || 0,
        };
        // 处理优惠券
        if (transactionRecord.user_coupon_id) {
            insertOrder.user_coupon_id = transactionRecord.user_coupon_id; // 用户优惠券ID
            insertOrder.coupon_id = transactionRecord.coupon_id; // 优惠券ID
            insertOrder.coupon_name = transactionRecord.coupon_name; // 优惠券名称
            insertOrder.coupon_fee = transactionRecord.coupon_fee; // 优惠金额
        }
        const orderInsertResult = await db.collection('@Order').insertOne(insertOrder);
        // 发放权益
        await user_right_service.add_sku_right(userId, orderInsertResult.insertedId.toString(), transactionRecord.sku_id, target);
        // 完成订单
        await db.collection('@Order').updateOne({ _id: orderInsertResult.insertedId }, {
            $set: {
                status: enums.OrderStatus.SUCCESS,
                utime: new Date()
            }
        });
        return responseWrapper.succ(null);
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const postNotificationH5 = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const { resource } = await Joi.validate(req.body, postNotificationQrSchema);
        const now = new Date();

        const payment = await client.wly.getPaymentByMchId(config.get('payment.wechatPay.h5.mch_id'));
        const notification = payment.decodeResource(resource);
        const tradeNo = notification.out_trade_no;

        const transactionRecord = await db.collection('@TransactionRecord').findOne({ order: tradeNo });
        if (!transactionRecord) throw new Error('找不到交易记录');
        if (transactionRecord.status !== enums.TransactionStatus.PROCESSING) throw new Error('交易记录状态错误');
        if (notification.amount && notification.amount.total !== transactionRecord.amount) throw new Error('交易记录金额错误');

        const userInfo = await db.collection('user').findOne({ _id: transactionRecord.user_id });
        if (!userInfo) throw new Error('找不到用户');

        const userId = userInfo._id;
        const ucId = userInfo.uc_id;
        const out_trade_no = notification.transaction_id || ''; // 微信订单ID
        // 交易完成
        await db.collection('@TransactionRecord').updateOne({ _id: transactionRecord._id }, {
            $set: {
                out_trade_no: out_trade_no,
                notify_time: now,
                status: notification.trade_state === 'SUCCESS' ? enums.TransactionStatus.SUCCESS : enums.TransactionStatus.FAILED
            }
        });

        if (notification.trade_state !== 'SUCCESS') {
            return responseWrapper.succ(null);
        }

        const orderNo = _createOrderNo();
        let goods = {
            id: transactionRecord.goods_id,
            name: transactionRecord.goods_name,
            items: transactionRecord.items
        };

        const insertOrder = {
            ctime: now,
            utime: now,
            no: orderNo,
            user_id: userId,
            uc_id: ucId,
            status: enums.OrderStatus.DOING,
            type: 'resource',
            original_price: transactionRecord.amount,
            discount_price: transactionRecord.amount,
            final_price: transactionRecord.amount,
            transaction_id: transactionRecord._id.toString(),
            pay_through: transactionRecord.pay_through,
            goods: goods,
            discount: 0,
            discount_fee: 0,
            trade_no: tradeNo,
            out_trade_no: out_trade_no // 微信订单ID
        };

        const orderInsertResult = await db.collection('@Order').insertOne(insertOrder);
        // 方法购买列表
        const date = new Date();
        const user_resource_items = [];
        for (const item of goods.items) {
            user_resource_items.push({
                user_id: userId,
                transaction_id: transactionRecord._id.toString(), // 交易记录ID
                order_id: orderInsertResult.insertedId.toString(), // 订单ID
                ...item,
                ctime: date,
                utime: date,
            })
        }
        await db.collection(schema.user_payment_resource).insertMany(user_resource_items); //
        // 清理购物车数据
        const cart = await db.collection(schema.user_resource_cart).findOne({user_id: userId});
        const items = cart.items.filter(e => !goods.items.find(p => p.type === e.type && p.resource_id === e.resource_id));
        await db.collection(schema.user_resource_cart).updateOne({user_id: userId}, {$set: {items: items}});
        // 完成订单
        await db.collection('@Order').updateOne({ _id: orderInsertResult.insertedId }, {
            $set: {
                status: enums.OrderStatus.SUCCESS,
                utime: new Date()
            }
        });
        return responseWrapper.succ(null);
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const postNotificationApp = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const { resource } = await Joi.validate(req.body, postNotificationQrSchema);
        const now = new Date();

        const payment = await client.wly.getPaymentByMchId(config.get('payment.wechatPay.app.mch_id'));
        const notification = payment.decodeResource(resource);
        const tradeNo = notification.out_trade_no;

        const transactionRecord = await db.collection('@TransactionRecord').findOne({ order: tradeNo });
        if (!transactionRecord) throw new Error('找不到交易记录');
        if (transactionRecord.status !== enums.TransactionStatus.PROCESSING) throw new Error('交易记录状态错误');
        if (notification.amount && notification.amount.total !== transactionRecord.amount) throw new Error('交易记录金额错误');

        const userInfo = await db.collection('user').findOne({ _id: transactionRecord.user_id });
        if (!userInfo) throw new Error('找不到用户');

        const userId = userInfo._id;
        const ucId = userInfo.uc_id;
        const out_trade_no = notification.transaction_id || ''; // 微信订单ID
        // 交易完成
        await db.collection('@TransactionRecord').updateOne({ _id: transactionRecord._id }, {
            $set: {
                out_trade_no: out_trade_no,
                notify_time: now,
                status: notification.trade_state === 'SUCCESS' ? enums.TransactionStatus.SUCCESS : enums.TransactionStatus.FAILED
            }
        });

        if (notification.trade_state !== 'SUCCESS') {
            return responseWrapper.succ(null);
        }

        const orderNo = _createOrderNo();
        let goods = {
            id: transactionRecord.goods_id,
            name: transactionRecord.goods_name,
            items: transactionRecord.items
        };

        const insertOrder = {
            ctime: now,
            utime: now,
            no: orderNo,
            user_id: userId,
            uc_id: ucId,
            status: enums.OrderStatus.DOING,
            type: 'resource',
            original_price: transactionRecord.amount,
            discount_price: transactionRecord.amount,
            final_price: transactionRecord.amount,
            transaction_id: transactionRecord._id.toString(),
            pay_through: transactionRecord.pay_through,
            goods: goods,
            discount: transactionRecord.discount || 0,
            discount_fee: transactionRecord.discount_fee || 0,
            trade_no: tradeNo,
            out_trade_no: out_trade_no // 微信订单ID
        };

        const orderInsertResult = await db.collection('@Order').insertOne(insertOrder);
        // 添加购买列表
        const date = new Date();
        const user_resource_items = [];
        for (const item of goods.items) {
            user_resource_items.push({
                user_id: userId,
                transaction_id: transactionRecord._id.toString(), // 交易记录ID
                order_id: orderInsertResult.insertedId.toString(), // 订单ID
                ...item,
                ctime: date,
                utime: date,
            })
        }
        await db.collection(schema.user_payment_resource).insertMany(user_resource_items);
        // 清理购物车数据
        const cart = await db.collection(schema.user_resource_cart).findOne({user_id: userId});
        const items = cart.items.filter(e => !goods.items.find(p => p.type === e.type && p.resource_id === e.resource_id));
        await db.collection(schema.user_resource_cart).updateOne({user_id: userId}, {$set: {items: items}});
        // 完成订单
        await db.collection('@Order').updateOne({ _id: orderInsertResult.insertedId }, {
            $set: {
                status: enums.OrderStatus.SUCCESS,
                utime: new Date()
            }
        });
        return responseWrapper.succ(null);
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const deletePaymentSchema = Joi.object({
    id: Joi.string().required(),
});
/**
 * 取消订单
 */
const deletePayment = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const { id } = await Joi.validate(req.params, deletePaymentSchema);

        const transactionRecord = await db.collection('@TransactionRecord').findOne({ _id: ObjectId(id) });

        if (!transactionRecord) {
            throw new Error('找不到交易记录');
        }

        if (transactionRecord.status !== enums.TransactionStatus.PROCESSING) {
            throw new Error('交易记录状态错误');
        }

        await db.collection('@TransactionRecord').updateOne({ _id: ObjectId(id) }, {
            $set: {
                utime: new Date(),
                status: enums.TransactionStatus.CANCEL,
            },
        });
        await _hanldeCoupon(transactionRecord, enums.TransactionStatus.CANCEL);

        return responseWrapper.succ(null);
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

const refundPaymentSchema = Joi.object({
    id: Joi.string().required(),
});

const refundPaymentBodySchema = Joi.object({
    reason: Joi.string().required(),
    operator: Joi.string().required(),
    force: Joi.bool().optional(),
});

/**
 * 订单退费
 */
const refundPayment = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const { id } = await Joi.validate(req.params, refundPaymentSchema);
        const { reason, operator, force } = await Joi.validate(req.body, refundPaymentBodySchema);
        const now = new Date();

        const transactionRecord = await db.collection('@TransactionRecord').findOne({ _id: ObjectId(id) });

        if (!transactionRecord) {
            throw new Error('找不到交易记录');
        }

        if (transactionRecord.status !== enums.TransactionStatus.SUCCESS) {
            throw new Error('交易记录状态错误');
        }

        const orderInfo = await db.collection('@Order').findOne({ 'transaction_id': id, });
        const userInfo = await db.collection('user').findOne({ _id: transactionRecord.user_id });
        if (!userInfo) {
            throw new Error('找不到用户');
        }
        // if (!force && moment(userInfo.expired_time).subtract(transactionRecord.month, 'month').startOf('day').toDate() < moment().subtract(1, 'w').startOf('day').toDate()) {
        //     throw new Error('过期时间超过限制');
        // }

        const refundNo = _genOrderUUID();
        if (orderInfo.final_price > 0) {
            let payment
            if (transactionRecord.pay_through === enums.PayThrough.WECHAT_PAY_APP) {
                payment = await client.wly.getPaymentByMchId(config.get('payment.wechatPay.app.mch_id'));
            } else {
                payment = await client.wly.getPayment(true);
            }
            let result = await payment.refund({
                out_trade_no: transactionRecord.order,
                out_refund_no: refundNo,
                reason: reason,
                amount: {
                    refund: transactionRecord.amount,
                    total: transactionRecord.amount,
                    currency: 'CNY',
                }
            });
            if (result.status !== 200) {
                throw new Error(`发起微信退费失败: ${result.data}`);
            }
        }

        const insertResult = await db.collection('@RefundRecord').insertOne({
            ctime: now,
            utime: now,

            status: enums.TransactionStatus.PROCESSING,
            user_id: transactionRecord.user_id,

            order_id: orderInfo._id.toString(),
            order: refundNo,
            transaction_id: id,
            reason: reason,
            amount: transactionRecord.amount,
            operator: operator,
        });

        // 处理交易记录
        await db.collection('@TransactionRecord').updateOne({ _id: ObjectId(id) }, {
            $set: {
                utime: now,
                status: enums.TransactionStatus.APPLY_REFUND,
                refund_order: refundNo,
                refund_id: insertResult.insertedId.toString()
            },
        });
        // 处理订单
        await db.collection('@Order').updateOne({
            'transaction_id': id,
        }, {
            $set: {
                status: enums.OrderStatus.REFUND,
                ctime: now,
            },
        });
        // 处理权益
        await user_right_service.sub_sku_right(orderInfo.user_id, orderInfo._id.toString());
        // 处理优惠券
        await _hanldeCoupon(transactionRecord, enums.TransactionStatus.APPLY_REFUND);
        return responseWrapper.succ(null);
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

/**
 * 处理订单优惠券信息
 * @param transactionRecord 交易记录信息
 * @param currTransactionStatus 当前订单状态
 * @returns {Promise<void>}
 * @private
 */
async function _hanldeCoupon(transactionRecord, currTransactionStatus) {
    //
    if (!transactionRecord.user_coupon_id) return;
    const now = new Date();
    let userCouponsUpdateData = null;
    let updateUsageCount = 0;
    if (currTransactionStatus === enums.TransactionStatus.PROCESSING) { // 发起支付
        userCouponsUpdateData = {
            usage_status: 2, // 使用中
            usage_time: now,
            utime: now,
            transaction_id: transactionRecord._id.toString(),
            coupon_fee: transactionRecord.coupon_fee
        };
    } else if (currTransactionStatus === enums.TransactionStatus.SUCCESS) { // 支付成功
        userCouponsUpdateData = {
            usage_status: 1,
            utime: now
        }
        updateUsageCount = 1;
    } else if (currTransactionStatus === enums.TransactionStatus.CANCEL // 取消
        || enums.TransactionStatus.APPLY_REFUND) { // 退款
        userCouponsUpdateData = {
            utime: now,
            usage_status: enums.BooleanNumber.NO,
            transaction_id: '',
            coupon_fee: 0,
            usage_time: null,
        };
        if (currTransactionStatus === enums.TransactionStatus.APPLY_REFUND) {
            updateUsageCount = -1;
        }
    }
    if (!_.isEmpty(userCouponsUpdateData)) {
        await db.collection('user_coupons').updateOne({_id: ObjectId(transactionRecord.user_coupon_id)}, {
            $set: userCouponsUpdateData
        });
    }
    if (updateUsageCount !== 0) {
        // 优惠券使用数量
        // const redis_lock_key = `coupons_usage_lock:${transactionRecord.coupon_id}`;
        // const lock = await rediser.lock(redis_lock_key, '1', 5);
        // if (lock) {
        //     try {
        //         await db.collection('coupons').updateOne({ _id: ObjectId(transactionRecord.coupon_id) }, {
        //             $inc:{ usage_count: updateUsageCount }
        //         });
        //     } finally {
        //         await rediser.unlock(redis_lock_key, '1');
        //     }
        // }
        await db.collection('coupons').updateOne({ _id: ObjectId(transactionRecord.coupon_id) }, {
            $inc:{ usage_count: updateUsageCount }
        });
    }
}

const postRefundNotificationSchema = Joi.object({
    resource: Joi.object().required(),
}).unknown(true);

/**
 * 提交支付回调 微信支付 QR
 */
const postRefundNotification = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const { resource } = await Joi.validate(req.body, postRefundNotificationSchema);
        const now = new Date();
        const payment = await client.wly.getPayment(true);
        const notification = payment.decodeResource(resource);
        const tradeNo = notification.out_trade_no;

        const transactionRecord = await db.collection('@TransactionRecord').findOne({ order: tradeNo });
        if (!transactionRecord) {
            throw new Error('找不到交易记录');
        }

        if (transactionRecord.status !== enums.TransactionStatus.APPLY_REFUND) {
            throw new Error('交易记录状态错误');
        }

        if (notification.amount && notification.amount.total !== transactionRecord.amount) {
            throw new Error('交易记录金额错误');
        }

        const userInfo = await db.collection('user').findOne({ _id: transactionRecord.user_id });
        if (!userInfo) {
            throw new Error('找不到用户');
        }

        // 交易完成
        await db.collection('@TransactionRecord').updateOne({ _id: transactionRecord._id }, {
            $set: {
                utime: now,
                status: notification.refund_status === 'SUCCESS' ? enums.TransactionStatus.REFUND : enums.TransactionStatus.APPLY_REFUND
            },
        });
        await db.collection('@RefundRecord').updateOne({ order: transactionRecord.refund_order }, {
            $set: {
                utime: now,
                status: notification.refund_status === 'SUCCESS' ? enums.TransactionStatus.REFUND : enums.TransactionStatus.PROCESSING
            },
        });
        return responseWrapper.succ(null);
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

const getUnpaidTransaction = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const userId = req.user.id;
        const now = new Date();
        const cond = {
            user_id: userId,
            status: enums.TransactionStatus.PROCESSING, // 支付中
            out_time: { // 未超时
                $gt: now
            }
        }
        const transactionList = await db.collection('@TransactionRecord').find(cond).toArray();
        if (!_.size(transactionList)) { //
            return responseWrapper.succ(null);
        }
        const trans = transactionList[0];
        const goodsInfo = await db.collection('@Goods').findOne({ _id: ObjectId(trans.goods_id) });
        const resObj = {
            id: trans._id.toString(),
            amount: trans.amount,
            pay_through: trans.pay_through,
            qr_url: trans.qr_url,
            goods_id: trans.goods_id,
            goods_name: goodsInfo.name,
            ctime: trans.ctime.getTime(),
            out_time: trans.out_time.getTime()
        };
        if (trans.user_coupon_id) {
            resObj.user_coupon_id = trans.user_coupon_id;
            resObj.coupon_id = trans.coupon_id;
            resObj.coupon_name = trans.coupon_name;
            resObj.coupon_fee = trans.coupon_fee;
        }
        return responseWrapper.succ(resObj);
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

async function manualFinish(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const env = process.env.NODE_ENV;
        if (env !== 'test') return responseWrapper.error('HANDLE_ERROR', '非法操作');
        const userId = req.user.id;
        const ucId = req.user.ucId;
        const id = req.params.id;
        if(!id) throw new Error('订单不存在');

        const transactionRecord = await db.collection('@TransactionRecord').findOne({ _id: new ObjectId(id) });
        if (!transactionRecord) {
            throw new Error('找不到交易记录');
        }
        if (transactionRecord.status === enums.TransactionStatus.SUCCESS) {
            throw new Error('订单已完成');
        }
        // 交易完成
        const now = new Date();
        await db.collection('@TransactionRecord').updateOne({ _id: transactionRecord._id }, {
            $set: {
                notify_time: new Date(),
                status: enums.TransactionStatus.SUCCESS
            }
        });

        // 优惠券标记使用
        await _hanldeCoupon(transactionRecord, enums.TransactionStatus.SUCCESS);
        let target = null;
        if (transactionRecord.resource_id) {
            target = {
                id: transactionRecord.resource_id,
                type: transactionRecord.resource_type,
            }
        }

        const sku = await db.collection('pms_sku').findOne({_id: new ObjectId(transactionRecord.sku_id)});
        const spu = await db.collection('pms_spu').findOne({_id: new ObjectId(sku.spu_id)});
        const orderNo = _createOrderNo();
        let goods = {
            spu: {
                id: transactionRecord.spu_id,
                name: transactionRecord.spu_name,
            },
            sku: {
                id: transactionRecord.sku_id,
                name: transactionRecord.sku_name,
                key: sku.key,
                specs: sku.specs,
                rights: sku.rights
            }
        };

        const insertOrder = {
            ctime: now,
            utime: now,
            no: orderNo,
            user_id: userId,
            uc_id: ucId,
            status: enums.OrderStatus.DOING,
            type: spu.code,
            original_price: sku.original_price,
            discount_price: sku.discount_price,
            final_price: 0,
            transaction_id: transactionRecord._id.toString(),
            pay_through: transactionRecord.pay_through,
            goods: goods,
            target: target,
            discount: transactionRecord.discount || 0,
            discount_fee: transactionRecord.discount_fee || 0,
        };
        // 处理优惠券
        if (transactionRecord.user_coupon_id) {
            insertOrder.user_coupon_id = transactionRecord.user_coupon_id; // 用户优惠券ID
            insertOrder.coupon_id = transactionRecord.coupon_id; // 优惠券ID
            insertOrder.coupon_name = transactionRecord.coupon_name; // 优惠券名称
            insertOrder.coupon_fee = transactionRecord.coupon_fee; // 优惠金额
        }

        const orderInsertResult = await db.collection('@Order').insertOne(insertOrder);
        // 完成订单
        await db.collection('@Order').updateOne({ _id: orderInsertResult.insertedId }, {
            $set: {
                status: enums.OrderStatus.SUCCESS,
            },
        });
        // 发放权益
        await user_right_service.add_sku_right(transactionRecord.user_id, orderInsertResult.insertedId.toString(), transactionRecord.sku_id, target);
        return responseWrapper.succ({id: id});
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
}

async function manualFinishH5(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const env = process.env.NODE_ENV;
        if (env !== 'test') return responseWrapper.error('HANDLE_ERROR', '非法操作');
        const userId = req.user.id;
        const ucId = req.user.ucId;
        const id = req.params.id;
        if(!id) throw new Error('订单不存在');

        const transactionRecord = await db.collection('@TransactionRecord').findOne({ _id: new ObjectId(id) });
        if (!transactionRecord) {
            throw new Error('找不到交易记录');
        }
        if (transactionRecord.status === enums.TransactionStatus.SUCCESS) {
            throw new Error('订单已完成');
        }
        // 交易完成
        const now = new Date();
        await db.collection('@TransactionRecord').updateOne({ _id: transactionRecord._id }, {
            $set: {
                notify_time: new Date(),
                status: enums.TransactionStatus.SUCCESS
            }
        });
        const orderNo = _createOrderNo();
        let goods = {
            id: transactionRecord.goods_id,
            name: transactionRecord.goods_name,
            items: transactionRecord.items
        };

        const insertOrder = {
            ctime: now,
            utime: now,
            no: orderNo,
            user_id: userId,
            uc_id: ucId,
            status: enums.OrderStatus.DOING,
            type: 'resource',
            original_price: transactionRecord.amount,
            discount_price: transactionRecord.amount,
            final_price: transactionRecord.amount,
            transaction_id: transactionRecord._id.toString(),
            pay_through: transactionRecord.pay_through,
            goods: goods,
            discount: 0,
            discount_fee: 0,
            trade_no: transactionRecord.order,
            out_trade_no: '' // 支付平台订单ID
        };

        const orderInsertResult = await db.collection('@Order').insertOne(insertOrder);
        // 方法购买列表
        const user_resource_items = [];
        for (const item of goods.items) {
            user_resource_items.push({
                user_id: userId,
                transaction_id: '', // 交易记录ID
                order_id: orderInsertResult.insertedId.toString(), // 订单ID
                ...item,
                ctime: now,
                utime: now,
            })
        }
        await db.collection(schema.user_payment_resource).insertMany(user_resource_items); //
        // 清理购物车数据
        const cart = await db.collection(schema.user_resource_cart).findOne({user_id: userId});
        const items = cart.items.filter(e => !goods.items.find(p => p.type === e.type && p.resource_id === e.resource_id));
        await db.collection(schema.user_resource_cart).updateOne({user_id: userId}, {$set: {items: items}});
        // 完成订单
        await db.collection('@Order').updateOne({ _id: orderInsertResult.insertedId }, {
            $set: {
                status: enums.OrderStatus.SUCCESS,
                utime: new Date()
            }
        });
        // 发放权益
        return responseWrapper.succ({id: id});
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
}

// helper

// 生成商户订单号（UUID）
function _genOrderUUID() {
    return uuid.v4().split('-').join('');               // 商户系统内部的订单号,32个字符内、可包含字母
}


// 生成订单编号
const orderNoCache = {
    updateTime: new Date(),
    noCache: [],
};

function _createOrderNo() {
    const createNo = function () {
        let sixRandom = '';
        for (let i = 0; i < 6; i++) {
            sixRandom += Math.floor(Math.random() * 10);
        }
        const prefix = moment().format('YYYYMMDDHH');
        return 'TK' + prefix + sixRandom;
    };
    if (new Date().getTime() - orderNoCache.updateTime.getTime > 60 * 60 * 1000) {// 最少每小时清空一次
        orderNoCache.noCache = [];
        orderNoCache.updateTime = new Date();
    }
    let currentNo = createNo();
    while (orderNoCache.noCache.indexOf(currentNo) > 0) { // 重复后重新选
        currentNo = createNo();
    }
    orderNoCache.noCache.push(currentNo);
    return currentNo;
}

const queryResourcesByType = async (ids, type) => {
    const extendQues = require('../../assemble_api/v1/extend_ques');
    const extendExam = require('../../favorite_api/v1/extend_exampaper');
    try {
        let typeHandle = {
            question: util.promisify(extendQues.get_kb_questions),
            exampaper: extendExam.getExampapersByIds
        };
        if (!typeHandle[type]) {
            throw new Error(type + '类型的统计数据为定义');
        }
        if (!ids.length) {
            return [];
        }
        let _body = await typeHandle[type](ids);
        return _body;
    } catch (err) {
        console.error(err.stack);
    }
}

const queryEduFileById = async (id) => {
    const edu_file_service = require('../../edu_assistant_file_api/v1/index');
    try {
       const result = await edu_file_service.getById(id);
       return result;
    } catch (err) {
        console.error(err.stack);
    }
}

module.exports = {
    postPayment,
    postPaymentV2,
    getPaymentStatus,
    postNotificationQr,
    postNotificationQrV2,
    refundPayment,
    postRefundNotification,
    deletePayment,
    getUnpaidTransaction,
    manualFinish,
    postPaymentResourceH5,
    postPaymentResourceApp,
    postNotificationH5,
    postNotificationApp, // 新增的APP支付回调方法
    manualFinishH5
};
