const _ = require('lodash');
const rediser = require('./rediser');
const config = require('config');

// 默认数据有效时间为 5 min
const defaultValidTime = 60 * 5;
// 默认缓存有效时间为 1 day
const defaultCacheTime = 60 * 60 * 24;
// 接口最大 pending 时间为 5 min
const maxPendingTime = 60 * 5;

/*
    缓存机制
    - 默认缓存响应数据 1 day & 数据有效时间为 5 min
    - 每次接受请求，如果找到缓存数据，则返回缓存数据（cacheTime）
    - 如果发现缓存数据过期（validTime）
        - 依然先返回缓存数据
        - 缓存数据置入 pending 标识并🔄刷新缓存数据
        - 调用接口获取数据并🔄刷新缓存数据
    - 如果没有缓存数据
        - 调用接口获取数据并🔄刷新缓存数据
*/

/**
 * 自动包装路由缓存
 * 暂时只支持简单 JSON 请求
 * @param {Function} handler 路由处理函数
 * @param {Object} options 配置项
 * @param {number} [options.cacheTime=] 缓存时间
 * @param {number} [options.validTime=] 数据有效时间
 * @return {Function}
 * @example
 * cacheWrapper(getData, {validTime: 60});
 */
function cacheWrapper(handler, options) {

    // 默认有效时间为1
    options.cacheTime = options.cacheTime || defaultCacheTime;
    options.validTime = options.validTime || defaultValidTime;

    // 包装 req, res
    return async function (req, res) {
        let key = `${req.baseUrl + req.path}:${process.env.NODE_PORT}:${JSON.stringify(req.query)}`;
        let response = {};
        let responseFlag = false;

        const doResponse = () => {
            if (responseFlag) return;
            res.status(response.status);
            res.json(response.data);
            responseFlag = true;
        };

        let fakeRes = {
            status(status) {
                response.status = status || 200;
            },
            json(data) {
                response.data = data;
                doResponse();
                response.createTime = +new Date();
                // 缓存
                rediser.set(key, response, options.cacheTime);
            },
        };
        rediser.get(key, (err, result) => {
            if (err) throw err;
            // 如果存在数据
            if (result) {
                // 则直接返回数据
                response.status = result.status;
                response.data = result.data;
                doResponse();
                // 之后检查数据是否过期，如果数据过期
                if (_.isNumber(result.createTime)
                    && (new Date() - result.createTime > options.validTime * 1000)) {
                    // 并且数据并不在刷新中
                    if (!result.pending || (new Date() - result.pending > maxPendingTime * 1000)) {
                        // 就将数据置入刷新状态
                        result.pending = +new Date();
                        rediser.set(key, result, options.cacheTime);
                        // 并刷新数据
                        handler(req, fakeRes);
                    }
                }
            } else {
                handler(req, fakeRes);
            }
        });
    };
}

module.exports = cacheWrapper;