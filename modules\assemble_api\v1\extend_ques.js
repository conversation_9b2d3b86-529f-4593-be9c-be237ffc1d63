var http = require('http');
var config = require('config');
var HttpWrapper = require('../../utils/http_wrapper.js');
var _ = require('underscore');
const lodash = require('lodash');

const mongodber = require('../../utils/mongodber');
const db = mongodber.use('tiku');
const db_kb_zyk = mongodber.use('kb_zyk');
const db_tiku_open = mongodber.use('tiku_open');
const ObjectId = require('mongodb').ObjectId;
const Thenjs = require('thenjs');
const client = require('../../client');
const paper_utils = require('../../utils/paper_utils');
const enums = require('../../../bin/enum');

const TIMEOUT = 1000;
const RETRY_TIMES = 1;

function get_kb_questions(qids, callback){
    let data = JSON.stringify({
        question_ids: qids,
        fields_type: 'full',
    });

    let server = config.get('KB_API_SERVER');
    let options = {
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        path: '/kb_api/v2/questions/?api_key=' + server.appKey,
        method: 'POST',
        headers: {
            'content-type': 'application/json',
            'content-length': Buffer.byteLength(data),
        },
        body: data,
        timeout: TIMEOUT,
        retryTimes: RETRY_TIMES,
    };

    let httpWrapper = new HttpWrapper();
    return httpWrapper.request(options, (err, res) => {
        if (err) {
           return callback(err);
        }
        if (!res) {
            return callback('获取kb试题出错');
        }
        if (res.statusCode === 200){
            let ret = JSON.parse(res.data);
            callback(null, ret);
        } else {
            callback(err, null);
        }
    });
}

function traverse_questions(basket, fun){
    var profile = [];
    if (!basket){
        return profile;
    }
    var volumes = basket.volumes;
    if (!volumes){
        return profile;
    }
    for (var v in volumes){
        var blocks = volumes[v]['blocks'];
        for (var b in blocks){
            var block = blocks[b];
            var type = block['type'];
            var questions = block['questions'];
            profile.push(fun(questions));
        }
    }
    return profile;
}

function extract_ques_id(questions){
    return _.filter(questions.map(function(x){return x['id']}), function(m){
		return m != undefined && m != 'undefined';
	});
}

function extract_ques(questions){
    return questions;
}

function extend_ques(basket, callback){
    let qids = [];
    let stringQids = [];
    const ques_map = {};

    Thenjs((cont) => {
        // get all qids
        let blocks = traverse_questions(basket, extract_ques_id);
        for (let i in blocks){
            let questions = blocks[i];
            for (let q of questions){
                if (lodash.isNumber(q))
                    qids.push(q);
                else
                    stringQids.push(q);
            }
        }
        if (!lodash.size(qids) && !lodash.size(stringQids)) return callback();
        cont();
    }).then((cont, args) => {
        if (!qids || qids.length === 0) return cont();
        get_kb_questions(qids, (err, results) => {
            if (err){
                return callback(err);
            }
            for (const q in results){
                const ques = results[q];
                ques_map[ques['id']] = ques;
            }
            cont();
        });
    }).then((cont, args) => {
        if (!stringQids || stringQids.length === 0) return cont();
        stringQids = stringQids.map(item => new ObjectId(item));
        if (basket && basket.isZyk) {
            db_kb_zyk.collection('questions').find({ _id: { $in: stringQids }}).toArray( (err, results) => {
                if (err){
                    return callback(err);
                }
                results = results.map(item => {
                    item.id = item._id.toString();
                    delete item._id;
                    return item;
                });

                for (let q in results){
                    const ques = results[q];
                    ques_map[ques['id']] = ques;
                }
                cont();
            });
        } else {
            db.collection('upload_questions').find({ _id: { $in: stringQids }}).toArray( (err, results) => {
                if (err){
                    return callback(err);
                }
                results = results.map(item => {
                    item.id = item._id.toString();
                    delete item._id;
                    return item;
                });

                for (let q in results){
                    const ques = results[q];
                    ques_map[ques['id']] = ques;
                }
                cont();
            });
        }
    }).then((cont, args) => {
        let blocks = traverse_questions(basket, extract_ques);
        for (let i in blocks){
            let questions = blocks[i];
            for (let q in questions){
                let score = questions[q].score;
                let ques = ques_map[questions[q]['id']];
                if (!ques){
                    delete questions[q];
                    continue;
                }
                questions[q] = ques;
                questions[q]['score'] = score;
            }
        }
        cont();
    }).finally((cont, err) => {
        if (err) {
            return callback(err);
        }
        return callback(null);
    });
}

async function extend_ques_async(basket, from = 'zx') {
    const quesMap = {}, kb_ids = [] ,zyk_ids = [], zx_ids = [];
    for (const volume of basket.volumes) {
        for (const block of volume.blocks) {
            for (const q of block.questions) {
                if (lodash.isNumber(q.id)) {
                    kb_ids.push(q.id);
                } else {
                    if (from === 'zyk') { // 校本
                        zyk_ids.push(q.id);
                    } else if (from === 'zx') { // 智学
                        if (q.source && q.source === 'zx') {
                            zx_ids.push(q.id);
                        } else if (q.source && q.source === 'zyk') {
                            zyk_ids.push(q.id);
                        } else if (q.source === enums.QuestionSource.UPLOAD) {
                            quesMap[q.id] = q;
                        // } else { 移除默认逻辑
                        //     // 上传数据异常block.questions 没有 source，默认为 zx题目
                        //     zx_ids.push(q.id);
                        }
                    }
                }
            }
        }
    }
    if (_.size(kb_ids)) {
        const questions = await client.kb.getQuestions(kb_ids);
        if (_.size(questions)) questions.forEach(q => quesMap[q.id] = q);
    }
    // if (_.size(tk_up_ids)) {
    //     const questions = await getQuestions(from, tk_up_ids);
    //     if (_.size(questions)) questions.forEach(q => quesMap[q.id] = q);
    // }
    if (_.size(zyk_ids)) {
        // const questions = await getQuestions('zyk', zyk_ids);
        const questions = await paper_utils.getZykQuestionByIds(zyk_ids);
        if (_.size(questions)) questions.forEach(q => quesMap[q.id] = q);
    }
    if (_.size(zx_ids)) {
        // const questions = await getQuestions(from, zx_ids);
        const questions = await paper_utils.getZxQuestionByIds(zx_ids);
        if (_.size(questions)) questions.forEach(q => quesMap[q.id] = q);
    }
    paper_utils.traverse_questions(basket, quesMap, true);
    // for (const volume of basket.volumes) {
    //     for (const block of volume.blocks) {
    //         let questions = block.questions;
    //         for (let q in questions){
    //             const question = questions[q];
    //             let score = question.score;
    //             let ques = quesMap[question['id']];
    //             if (!ques){
    //                 delete questions[q];
    //                 continue;
    //             }
    //             questions[q] = ques;
    //             questions[q]['score'] = score;
    //         }
    //     }
    // }
}


function ques_process(exampaper) {
    let subject = exampaper.subject || '';
    let volumes = exampaper.volumes || [];
    if (subject !== '英语') {
        return exampaper;
    }
    let ques_num = 1;
    for (let volume of volumes) {
        let blocks = volume.blocks || []
        for (let block of blocks) {
            let ques_type = block.type || '';
            let questions = block.questions || []
            for (let question of questions) {
                let ques_blocks = question.blocks || {};
                let stems = ques_blocks.stems || [];
                let description = question.description || '';
                let ques_number = [];
                if (ques_type === '单选题') {
                    ques_number.push(ques_num);
                    ques_num += 1
                } else if (ques_type === '填空题' || ques_type === '阅读理解') {
                    for (let one_stem of stems) {
                        ques_number.push(ques_num);
                        let regex = /[（\(]\d+?[\)）]/g;
                        let result = one_stem.stem.match(regex)
                        if (result && result.length !== 0) {
                            one_stem.stem = one_stem.stem.replace(result[0], ' ');
                        }
                        ques_num += 1
                    }
                } else if (ques_type === '语法填空' || ques_type === '七选五') {
                    let regex = /[（\(]\d+?[\)）]/g;
                    let result = stems[0].stem.match(regex)
                    if (result) {
                        for (let i = 0; i < result.length; i++) {
                            ques_number.push(ques_num)
                            question.blocks.stems[0].stem = question.blocks.stems[0].stem.replace(result[i], '(' + ques_num + ')');
                            ques_num += 1
                        }
                    }
                } else if (ques_type === '完形填空') {
                    // 删除小题号
                    for (let one_stem of stems) {
                        ques_number.push(ques_num)
                        one_stem.stem = ' '
                        ques_num += 1
                    }
                    // 替换试题中的题号
                    let regex = /[（\(]\d+?[\)）]/g;
                    let result = description.match(regex)
                    if (result) {
                        for (let i = 0; i < result.length; i++) {
                            if (result.length === ques_number.length) {
                                question.description = question.description.replace(result[i], '(' + ques_number[i] + ')');
                            } else {
                                question.description = question.description.replace(result[i], '');
                            }
                        }
                    }
                } else {
                    ques_number.push(ques_num);
                    ques_num += 1
                }
                question['ques_number'] = ques_number;
            }
        }
    }
}

module.exports = {
    extend_ques: extend_ques,
    get_kb_questions: get_kb_questions,
    ques_process: ques_process,
    extend_ques_async: extend_ques_async,
}
