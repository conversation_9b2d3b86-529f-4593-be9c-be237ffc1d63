/**
 * Desc: User operations to resources, such as favorite, erratum
 * Author: guochanghui
 **/
var http = require('http');
var config = require('config');
var qs = require('querystring');
var ResponseWrapper = require('../../middlewares/response_wrapper');
var rediser = require('../../utils/rediser');
var mongodber = require('../../utils/mongodber');
var request = require('request');
var URL = require('url');
const util = require('util');
const loadsh = require('lodash');
const Joi = require('@hapi/joi');
const client = require('zipkin-middleware').client;
const Logger = require('../../utils/logger');
const extendQues = require('../../assemble_api/v1/extend_ques');
const extendExam = require('../../favorite_api/v1/extend_exampaper');
const _ =require('underscore');
const promiseGetKbQuestions = util.promisify(extendQues.get_kb_questions);
const mapExampaperName = require('../../utils/mapExampaperName.js');
const enums = require('../../../bin/enum');
const ObjectID = require("mongodb").ObjectID;

var db = mongodber.use('tiku');
var db_open = mongodber.use('tiku_open');

const KBSERVER = config.get('KB_API_SERVER');

function postErratum(req, res){
    var resWrapper = new ResponseWrapper(req, res);
    try {
        var id = req.body.id;
		id = Number(id);
        var type = req.body.type;
        var comment = req.body.comment;
        if (comment.hasOwnProperty('err_fields')){
            comment['fields'] = comment['err_fields'];
            delete comment['err_fields'];
        }

        var legal_type = {
            exampaper:1, question:1, knowledge:1,
            book:1, knowledge_tree:1,
        };
        if (!(type in legal_type)){
            throw new Error('type not legal');
        }
        if (!id || !comment){
            throw new Error('id and comment cannot empty');
        }
    } catch(err){
        Logger.error(err);
        return resWrapper.error('PARAMETERS_ERROR', err.message);
    }

	var KBPSERV = config.get('KBPLAT_API_SERVER');

    var erratumsUrl = URL.format({
        protocol: KBPSERV.protocol,
        hostname: KBPSERV.hostname,
        pathname: '/kb_api/v2/erratums',
        port: KBPSERV.port,
        search: qs.stringify({
            api_key: req.apiKey
        })
    });

    var postBody = {
        'user_id': req.user.id,       // 提交者
        'from': "tiku",       // 来源
        'resource_id': id,   // 必输项。资源id
        'resource_type': type, // 必输项。资源类型：book, knowledge_tree, knowledge, exampaper, question
        'comment': comment
    }
    request.post({
        url: erratumsUrl,
        headers: {
            'content-type': 'application/json'
        },
        body: JSON.stringify(postBody),
        timeout: 5000
    }, function(error, response, _body){
        if (error) {
            Logger.error(error);
            return resWrapper.error('HANDLE_ERROR');
        }

        if(error || response.statusCode >= 500  ){
            return resWrapper.error('HANDLE_ERROR');
        } else if (response.statusCode >=400 && response.statusCode<500) {
            var ret = JSON.parse(_body);
            return resWrapper.send(ret);
        }
        var body = JSON.parse(_body);
        return resWrapper.send(body);
    });

}

const getErratum = async (req, res) => {
    var resWrapper = new ResponseWrapper(req, res);
    try {
        let listUrl = URL.format({
			protocol: KBSERVER.protocol,
			hostname: KBSERVER.hostname,
			port: KBSERVER.port,
			pathname: '/kb_api/v2/erratums',
			search: qs.stringify({
				api_key: req.apiKey
			})
		});
        let _body = await client.axios.get(listUrl);
        let resObj = _body.data;
        return resWrapper.succ(resObj);
    } catch (error) {
		return resWrapper.error('HANDLE_ERROR', '查找勘误信息失败')
    }
}

function postFavorite(req, res){
    var resWrapper = new ResponseWrapper(req, res);
    return resWrapper.succ({});
}

function getFavorite(req, res){
    var resWrapper = new ResponseWrapper(req, res);
    return resWrapper.succ({});
}

async function fillQuestionInfo(records) {
    let ids = _.pluck(records, 'resource_id');
    let questions = await promiseGetKbQuestions(ids);
    const result = [];
    if (!loadsh.size(ids) || !loadsh.size(questions)) return result;
    const questMap = loadsh.keyBy(questions, 'id');
    for (const r of records) {
        const q = questMap[r.resource_id];
        if (!q) continue;
        const tmp = loadsh.assign({}, q);
        tmp.ctime = r.ctime.getTime();
        if (tmp.blocks) {
            delete tmp.blocks.answers;
            delete tmp.blocks.solutions;
            delete tmp.blocks.explanations;
            delete tmp.blocks.knowledges;
        }
        result.push(tmp);
    }
    return result;
}
async function getQuestionsDownloadRecord(req, res) {
    let rw = new ResponseWrapper(req, res);
    let keys = ['subject', 'period', 'type'];
    let limit = req.query.limit * 1 || 10;
    let offset = req.query.offset * 1 || 0;
    let cond = {
        user_id: req.user.id,
        resource_type: 'question'
    };
    for (let key of keys) {
        if (!req.query[key]) {
            continue;
        }
        cond[key] = req.query[key];
    }
    let col = db.collection('user_download');
    let corsor = col.find(cond);
    let total_num = await corsor.count();
    let records = await corsor.sort({ctime: -1}).skip(offset).limit(limit).toArray();
    records = await fillQuestionInfo(records);
    return rw.succ({total_num, records});
}
async function fillExampaperInfo(records) {
    let kbIds = [];
    let tkIds = [];
    for (const r of records) {
        if (r.category === 1) {
            kbIds.push(r.resource_id);
        } else {
            tkIds.push(r.resource_id);
        }
    }
    let buf = {};
    if (loadsh.size(kbIds)) {
        const exams = await extendExam.getExampapersByIds(kbIds);
        _.each(exams, (e) => {
            buf[e.id] = e;
        });
    }
    if (loadsh.size(tkIds)) {
        const tkExams = await db_open.collection(enums.OpenSchema.user_paper).find({_id: {$in: tkIds.map(e => ObjectID(e))}}).toArray();
        _.each(tkExams, (e) => {
            buf[e._id.toString()] = e;
        });
    }

    records = _.map(records, (r) => {
        let e = buf[r.resource_id];
        if (!e) {
            return null;
        }
        let ques_num = 0;
        if (r.category === 1) {
            ques_num = e.ques_num;
        } else {
            for (const v of e.volumes) {
                for (const b of v.blocks) {
                    ques_num += loadsh.size(b.questions);
                }
            }
        }
        let obj = {
            id: r.resource_id,
            // utime: r.utime.toLocaleString(),
            ctime: r.ctime.getTime(),
            // expired_time: r.expired_time && r.expired_time.toLocaleString(),
            name: e.name,
            // region: e.province,
            type: e.type,
            // from_year: e.from_year,
            // to_year: e.to_year,
            // view_times: e.view_times,
            period: r.period,
            subject: r.subject,
            category: r.category,
            ques_num: ques_num,
        };
        return obj;
    });
    records = records.filter(u => u);
    return records;
}

const JOI_GET_PAPER_DOWNLOAD_LIST = Joi.object({
    limit: Joi.number().required(),
    offset: Joi.number().required(),
    period: Joi.string().optional().allow(''),
    subject: Joi.string().optional().allow(''),
    type: Joi.string().optional().allow(''),
    press_version: Joi.string().optional().allow(''),
    grade: Joi.string().optional().allow(''),
    category: Joi.number().optional().allow(''),
}).unknown(true);

async function getExamapapersDownloadRecord(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        let keys = ['subject', 'period', 'type','press_version', 'grade', 'category'];
        const params = await JOI_GET_PAPER_DOWNLOAD_LIST.validate(req.query);
        const { limit, offset} = params;
        let cond = {
            user_id: req.user.id,
            resource_type: 'exampaper'
        };
        for (let key of keys) {
            if (!params[key]) {
                continue;
            }
            cond[key] = req.query[key];
        }
        if (params.category) cond.category = params.category;
        let col = db.collection('user_download');
        let corsor = col.find(cond);
        let total_num = await corsor.count();
        let records = await corsor.sort({ctime: -1}).skip(offset).limit(limit).toArray();
        records = await fillExampaperInfo(records);
        return responseWrapper.succ({total_num, records});
    } catch (e) {
        logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }

}

async function getResourceRecord(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const user_id = req.user.id;
        const { resource_type, resource_id } = req.query;
        if (!resource_type || !resource_id) return responseWrapper.error('PARAMETERS_ERROR', '资源类型或资源ID不能为空');
        const record = await db.collection('user_download').findOne({user_id, resource_type, resource_id: Number(resource_id)});
        let result = null;
        if (!_.isEmpty(record)) {
            result = {
                id: record._id.toString(),
                resource_type: record.resource_type,
                resource_id: record.resource_id,
                expired_time: record.expired_time && record.expired_time.getTime(),
                ctime: record.ctime.getTime()
            }
        }
        return responseWrapper.succ(result);
    } catch (e) {
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

/**
 * 下载数量统计
 * @param req
 * @param res
 * @returns {Promise<void>}
 */
async function getUserDownloadStat(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const result = {
            exampaper: 0,
            question: 0,
            edu_file: 0,
            edu_tool: 0,
            resource_album: 0,
        };
        const list = await db.collection('user_download').find({user_id: req.user.id}).project({_id: 1, resource_type: 1, }).toArray();
        for (const data of list) {
            if (data.resource_type === enums.ResourceType.QUESTION) {
                result.question += 1;
            } else if (data.resource_type === enums.ResourceType.EXAMPAPER) {
                result.exampaper += 1;
            } else if (data.resource_type === enums.ResourceType.EDU_ASSISTANT_FILE) {
                result.edu_file += 1;
            } else if (data.resource_type === enums.ResourceType.EDU_ASSISTANT_TOOL) {
                result.edu_tool += 1;
            } else if (data.resource_type === enums.ResourceType.RESOURCE_ALBUM) {
                result.album += 1;
            }
        }
        return responseWrapper.succ(result);
    } catch (e) {
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const JOI_GET_EDU_FILE_DOWNLOAD_LIST = Joi.object({
    limit: Joi.number().required(),
    offset: Joi.number().required(),
    period: Joi.string().optional().allow(''),
    subject: Joi.string().optional().allow(''),
    category: Joi.number().optional().allow(''),
}).unknown(true);

async function getEduFileDownloadRecord(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const { offset, limit, period, subject, category } = await JOI_GET_EDU_FILE_DOWNLOAD_LIST.validate(req.query);

        const result = {
            total: 0,
            list: []
        };
        const cond = {
            user_id: req.user.id,
            resource_type: enums.ResourceType.EDU_ASSISTANT_FILE
        };
        if (period) cond.period = period;
        if (subject) cond.subject = subject;
        if (category) cond.category = category;
        const total = await db.collection('user_download').find(cond).count();
        if (!total) return responseWrapper.succ(result);
        result.total = total;
        const list = await db.collection('user_download').find(cond).sort({ctime: -1}).skip(offset).limit(limit).toArray();
        for (const data of list) {
            result.list.push({
                id: data.resource_id,
                name: data.resource_name,
                category: data.category,
                ctime: data.ctime.getTime(),
            });
        }
        return responseWrapper.succ(result);
    } catch (e) {
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const JOI_GET_EDU_TOOL_DOWNLOAD_LIST = Joi.object({
    limit: Joi.number().required(),
    offset: Joi.number().required(),
}).unknown(true);

async function getEduToolDownloadRecord(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const { offset, limit } = await JOI_GET_EDU_TOOL_DOWNLOAD_LIST.validate(req.query);

        const result = {
            total: 0,
            list: []
        };
        const cond = {
            user_id: req.user.id,
            resource_type: enums.ResourceType.EDU_ASSISTANT_TOOL
        };
        const total = await db.collection('user_download').find(cond).count();
        if (!total) return responseWrapper.succ(result);
        result.total = total;
        const list = await db.collection('user_download').find(cond).sort({ctime: -1}).skip(offset).limit(limit).toArray();
        for (const data of list) {
            result.list.push({
                id: data.resource_id,
                name: data.resource_name,
                ...(data.resource_info || {}),
                ctime: data.ctime.getTime(),
            });
        }
        return responseWrapper.succ(result);
    } catch (e) {
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}


const JOI_GET_ALBUM_DOWNLOAD_LIST = Joi.object({
    limit: Joi.number().required(),
    offset: Joi.number().required(),
    period: Joi.string().optional().allow(''),
    subject: Joi.string().optional().allow(''),
}).unknown(true);

async function getAlbumDownloadRecord(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const { offset, limit, period, subject } = await JOI_GET_ALBUM_DOWNLOAD_LIST.validate(req.query);

        const result = {
            total: 0,
            list: []
        };
        const cond = {
            user_id: req.user.id,
            resource_type: enums.ResourceType.RESOURCE_ALBUM
        };
        if (period) cond.period = period;
        if (subject) cond.subject = subject;
        const total = await db.collection('user_download').find(cond).count();
        if (!total) return responseWrapper.succ(result);
        result.total = total;
        const list = await db.collection('user_download').find(cond).sort({ctime: -1}).skip(offset).limit(limit).toArray();
        for (const data of list) {
            result.list.push({
                id: data.resource_id,
                name: data.resource_name,
                period: data.period,
                grade: data.grade,
                subject: data.subject,
                ...(data.resource_info || {}),
                ctime: data.ctime.getTime(),
            });
        }
        return responseWrapper.succ(result);
    } catch (e) {
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

module.exports = {
    postErratum: postErratum,
    getErratum: getErratum,
    postFavorite: postFavorite,
    getFavorite: getFavorite,
    getQuestionsDownloadRecord: getQuestionsDownloadRecord,
    getExamapapersDownloadRecord: getExamapapersDownloadRecord,
    getResourceRecord: getResourceRecord,
    getUserDownloadStat: getUserDownloadStat,
    getEduFileDownloadRecord,
    getEduToolDownloadRecord,
    getAlbumDownloadRecord,
}
