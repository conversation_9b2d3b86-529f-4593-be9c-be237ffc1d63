#!/usr/bin/env node

var program = require('commander');
var chalk = require('chalk');
var aes = require('../modules/utils/aes');
var jwt = require('../modules/utils/jwt');

program
	.usage('<option> [code string]')
	.option('-e, --encode', 'encode a object')
	.option('-d, --decode', 'decode a string');

program.on('--help', function () {
	console.log('# Example');
	console.log();
	console.log(chalk.gray('	node cookieParser -e \'{"userId": '+
			'"000000000000000000207540","salt": 0.7202953133988539,"isMember": true}\''));
	console.log();
})

function help () {
	program.parse(process.argv);
	if (program.args.length < 1) 
		return program.help();
}
help();

if(program.encode){
	var strIn = program.args[0];
	if(!strIn){
		program.help();
		return;
	}
	var obj = null;
	try{
		obj = JSON.parse(strIn);
	}catch(err){
		obj = strIn;
	}
	var strOut = aes.encript(jwt.encode(obj));
	console.log(chalk.green(strOut));
	return;
}

if(program.decode){
	var strIn = program.args[0];
	if(!strIn){
		program.help();
		return;
	}

	try{
		var obj = jwt.decode(aes.decript(strIn));
		if(typeof obj === 'object')
			console.log(chalk.yellow(JSON.stringify(obj, '\t', 3)));
		else
			console.log(chalk.yellow(obj));
	}catch(err){
		console.log(chalk.red('INVALID PARAMETER VALUE'));
	}
	return;
}

if(!program.decode && !program.encode){
	program.help();
	return;
}
