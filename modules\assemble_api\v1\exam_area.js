const logger = require('../../utils/logger');
const utils = require('../../utils/utils');

const mongodber = require('../../utils/mongodber');
const db = mongodber.use('tiku');
const collection = db.collection('exam_area');

const enums = require('../../../bin/enum');
const ResponseWrapper = require('../../middlewares/response_wrapper');
const ipRegion = require('../../utils/ip2region');

const _ = require('lodash');
const Joi = require('@hapi/joi');

const exam_area_data = [];

const JOI_GET_EXAM_AREA = Joi.object({
    period: Joi.string().required(), //  学段
    subject: Joi.string().required(), // 科目
}).unknown(true);
/**
 * 获取考区
 * @param req
 * @param res
 * @returns {Promise<void>}
 */
async function get_exam_area (req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const { period, subject } = await JOI_GET_EXAM_AREA.validate(req.query);
        // 查询
        const ip = utils.getClientIp(req);
        logger.info('client ip: ' + ip);
        const { region } = await ipRegion.searchRegionByIp(ip);
        logger.info('region: ' + region);
        const regionArr = region.split('|');
        let province = regionArr[2]; // 省份 优先使用用户信息省份，如果没有则使用IP定位信息
        if (province.endsWith('省')) province = province.substring(0, province.length - 1);
        let city = regionArr[2]; // 市
        if (city.endsWith('市')) city = city.substring(0, city.length - 1);
        const list = await collection.find({}).toArray();
        const result = {
            user_area: '', // 用户所在考区
            province: province, // 省
            city: city, // 市
            area_list: [], // 考区列表
        };
        // 添加默认选项
        const defaultData = list.find(e => e.period === period && _.size(e.subjects) === 0);
        result.user_area = defaultData.name;
        result.area_list.push({
            id: defaultData._id.toString(),
            name: defaultData.name,
            regions: defaultData.regions
        });
        for (const data of list) {
            if (period !== data.period) continue;
            if (!_.size(data.subjects)) continue;
            if (!data.subjects.includes(subject)) continue; // 科目不匹配
            result.area_list.push({
                id: data._id.toString(),
                name: data.name,
                regions: data.regions,
            });
            // 匹配用户考区
            if (data.subjects.includes(subject) && data.regions.includes(province)) {
                result.user_area = data.name;
            }
        }
        // 返回结果
        return responseWrapper.succ(result);
    } catch (err) {
        logger.error(err);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }

}

module.exports = {
    get_exam_area: get_exam_area
}
