const crypto = require('crypto');
const iv = CryptoJS.enc.Utf8.parse('AES');
const algorithm = 'aes-256-ctr';
const ENCRYPTION_KEY = Buffer.concat([Buffer.from('iyunxiao'), Buffer.alloc(32)], 32);
const IV_LENGTH = 16;

const axios = require('axios');
const config = require('config');
const URL = require('url');
const YJERV = config.get('YJ_SERVER');

const rediser = require('./rediser');
const utils = require('./utils');

function md5Encode(timestamp, token) {
    let md5 = crypto.createHash('md5');
    md5.update(`timestamp=${timestamp}&token=${token}`);
    return md5.digest('hex');
}

exports.getYjTeacherInfo = async function getYjTeacherInfo(teacherId) {
    const timestamp = Date.now();
    const token = {
        
    }
    let bossUrl = URL.format({
        protocol: BOSSSERV.protocol,
        hostname: BOSSSERV.hostname,
        pathname: '/external/api/customer/app_usage/get_by_school_id',
        port: BOSSSERV.port,
        query: {
            timestamp, token,
        }
    });
    let opstions = { headers: { 'apikey': BOSSSERV.apikey }, timeout: 50000 };
    let data = await axios.get(bossUrl, opstions).then(utils.bossHandler);

    return data;
};
