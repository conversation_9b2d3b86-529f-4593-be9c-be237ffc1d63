var Redis = require('ioredis');
var _ = require('lodash');

var g_caches = {};

function connect_cache(name, conf, callback){
	var redis = new Redis({
	    port: conf.port,
	    host: conf.host,
	    db: conf.db,
	    password: conf.password,
	    retryStrategy: retryStrategy,
	    reconnectOnError: reconnectOnError
	});
	redis.__proto__.kb_set = kb_set;
	redis.__proto__.kb_get = kb_get;
	redis.__proto__.kb_del = kb_del;
	redis.on('connect', onConnect);
	redis.on('error', onError);
	g_caches[name] = redis;
	return callback(null, name);
}

function get_cache(name) {
	return g_caches[name];
}

// Method Get
function kb_get(key, callback) {
    this.get(key, function (err, data) {
        if (err) return callback(err, null);
        if (!data) return callback(null, null);
        try {
            //console.log(data);
            var result = JSON.parse(data);
        } catch (e) {
            //logger.err('JSON Parse err : ' + e + 'on key' + key);
            return callback(e, null);
        }
        return callback(null, result);
    });
}

// Method Set : time 参数可选，单位:秒
function kb_set(key, value, time, callback) {
	if (!value) {
		return callback(null, null);
	}
    if (typeof time === 'function') {
        callback = time;
        time = null;
    }
    callback = callback || _.noop;
    try {
        value = JSON.stringify(value);
    } catch (e) {
        //logger.err('JSON Stringify err : ' + e + 'on key' + key);
        return callback(e, null);
    }

    if (!time) {
        this.set(key, value, callback);
    } else {
        this.setex(key, time, value, callback);
    }
}

// Method Set : time 参数可选，单位:秒
function kb_del(key) {
	this.set(key, 1, 'EX', 1);
}

// Error Handler:
function onConnect() {
    g_cacheEnable = true;
    //logger.info('Redis is ready now!');
}

function onError(err) {
    //logger.error('Init Redis Err:' + err);
    g_cacheEnable = false;
    return err;
}

function retryStrategy() {
    var delay = Math.min(redisConfig['retryTimes'] * 2, 2000);
    //logger.error('Redis disconnected; retry after', delay);
    return redisConfig.enable ? delay: false;
}

function reconnectOnError(err) {
    //logger.error('Redis reconnect error!');
    g_cacheEnable = false;
    return err;
}

module.exports = {
	get_cache: get_cache,
	connect_cache: connect_cache,
    getCacheStatus: function () {return g_cacheEnable;}
}
