/**
 * Desc: verify user is legal
 */
let crypto = require('crypto');
let config = require('config');
const pathToRegexp = require('path-to-regexp');
const axios = require('axios');
const moment = require('moment');
const URL = require('url');
const _ = require('lodash');
const Logger = require('../utils/logger');
let aes = require('../utils/aes');
let jwt = require('../utils/jwt');
const rediser = require('../utils/rediser');
let mongodber = require('../utils/mongodber');
const KBSERVER = config.get('KB_API_SERVER');
const QX_TOKEN_SECRET_KEY = config.get('QX_TOKEN_SECRET_KEY');
const VISIT_FREQ = config.get('VISIT_FREQ');
const VISIT_INTERFACE_LIMIT = config.get('VISIT_INTERFACE_LIMIT');
const VISIT_DEFAULT_LIMIT = config.get('VISIT_DEFAULT_LIMIT');
const VISIT_TRIAL_DEFAULT_LIMIT = config.get('VISIT_TRIAL_DEFAULT_LIMIT');
const DEFAULT_EXPIRE_MONTH = 3;

const ResponseWrapper = require('./response_wrapper');
const enums = require('../../bin/enum');
const utils = require('../utils/utils');

//初始化 METHOD_PATHREG
let METHOD_PATHREG = {};
let db;
(function () {
    let apis = VISIT_INTERFACE_LIMIT;

    for (let k in apis) {
        if (apis.hasOwnProperty(k)) {
            let arr = apis[k].split(' ');
            let method = arr[0].toUpperCase();
            let reg = pathToRegexp(arr[1], []);

            if (METHOD_PATHREG.hasOwnProperty(method)) {
                METHOD_PATHREG[method].push({
                    'reg': reg,
                    'api_name': k,
                });
            } else {
                METHOD_PATHREG[method] = [{
                    'reg': reg,
                    'api_name': k,
                }];
            }
        }
    }
}());

/**
 * Desc: 解析企业微信二维码token
 * @param {string} token
 * @returns {Array}
    ["吴金锋", // 姓名
    "2302", // 部门
    "后端开发工程师"
    "wujinfeng"] // 企信id
 */
const parseQXToken = (token) => {
    try {
        let decipher = crypto.createDecipher('aes192', QX_TOKEN_SECRET_KEY);
        let decrypted = decipher.update(token, 'hex', 'utf8');
        decrypted += decipher.final('utf8');
        let arr = decrypted.split('::');
        let now = Math.floor(Date.now() / 1000);
        if (arr.length === 5 && arr[0] > now) {
            arr.shift();
            return arr;
        }
        return null;
    } catch (e) {
        console.log('parse_jump_token error, token:' + token + ', error message:' + e);
    }
    return undefined;
};

function dataToken(req, res) {
    let token = '';
    token += Number(req.query.id);
    token += Number(req.query.timestamp);
    token += config.get('SCANTRON.sk');
    let decipher = crypto.createHash('md5');
    let tk = decipher.update(token).digest('hex');

    if (tk === req.query.dtk) {
        let cookie = config.get('TIKU_SERVER').sessionIdCookie;
        let _user = {
            id: parseInt(req.query.id),
            name: req.query.name,
            schoolId: parseInt(req.query.schoolId),
            schoolName: req.query.schoolName,
            role: '教师'
        };

        // set or update tiku cookie if necessary
        let newExp = Date.now() + cookie.options.maxAge;
        _user.exp = new Date(newExp);
        let value = aes.encript(jwt.encode(_user));
        res.cookie(cookie.name, value, cookie.options);
        req.user = _user;
        if (!req.headers.cookie) {
            req.headers.cookie = [cookie.name, value].join('=');
        }
        return true;
    }

    return false;
}

const verify = async function (req, res, next) {
    db = mongodber.use('tiku');
    let responseWrapper = new ResponseWrapper(req, res);

    if (req.query.dtk && dataToken(req, res)) {
        req.query.api_key = KBSERVER.appKey;
        return next();
    }

    const token = req.query.token;
    const hfsToken = req.query.hfsToken;
    const qixinUserid = req.query.userid; // 企信扫码登录会有userid字段
    const loginFrom = req.query.login_from; // kb-app登录
    const cookie = config.get('TIKU_SERVER').sessionIdCookie;
    const tikuSession = req.cookies && req.cookies[cookie.name];
    const HFS_STUDENT_SERVER = config.get('HFS_STUDENT_SERVER');
    const studentSession = req.cookies && req.cookies[HFS_STUDENT_SERVER.session];
    const HFS_TEACHER_SERVER = config.get('HFS_TEACHER_SERVER');
    const teacherSession = req.cookies && req.cookies[HFS_TEACHER_SERVER.session];

    let user;
    const qixinInfo = qixinUserid && token && parseQXToken(token);
    // 登录与权限校验
    // 企信扫码
    if (qixinUserid && token && qixinInfo && qixinInfo[0]) {
        if (qixinUserid !== qixinInfo[3]) {
            return responseWrapper.error('AUTH_ERROR', '企信id与token解析不对应。');
        }

        let userName = qixinInfo[0];
        let cond = { qixin_id: qixinUserid };
        let options = { fields: { _id: 1, qixin_id: 1, trace: 1, finished: 1 } };
        let userInfo = await db.collection('user').findOne(cond, options);

        let bind = userInfo ? false : true;
        let isVip = false;
        let userId = userInfo && userInfo._id || 0;
        let userSchoolId = userInfo && userInfo.sch_id || 1;
        let userSchoolName = userInfo && userInfo.sch_name || '企信';
        if (userInfo && userInfo.is_vip && userInfo.expired_time > new Date()) isVip = true;
        user = {
            id: userId, // 默认
            isVip: isVip,
            name: userName,
            grade: '',
            avatar: '',
            role: '教师',
            schoolId: userSchoolId, // 默认假的
            schoolName: userSchoolName, // 默认假的
            need_bind: bind, // 是否需要绑定
            qxid: qixinUserid
        }
        if (isVip) {
            user.vipType = enums.MemberType.TIKU_PERSONAL;
            user.startTime = userInfo.start_time && userInfo.start_time.getTime();
            user.expiredTime = userInfo.expired_time && userInfo.expired_time.getTime();
        }

        if (userInfo) {
            const accessSpot = {
                attachment: [],
                user_id: Number(user.id),
                school_id: Number(user.schoolId),
                province: '',
                city: '',
                ip: req.ip,
                timestamp: new Date(),
                event_id: 'land_web_times',
            };
            await db.collection('access_spot').insertOne(accessSpot).then();

            // //设置用户当前角色信息
            // try {
            let userId = Number(user.id);
            let userPeriod = '高中';
            let userExactSubject = '数学';

            if (userInfo.trace && userInfo.finished === 1) {
                let setData = {
                    name: user.name,
                    sch_id: user.schoolId,
                    sch_name: user.schoolName,
                    'curr.period': userInfo.trace.period,
                    'curr.subject': userInfo.trace.subject
                };
                await db.collection('user').update({ _id: userId }, { $set: setData });
            } else {
                let setData = {
                    name: user.name,
                    sch_id: user.schoolId,
                    sch_name: user.schoolName,
                    'trace.period': userPeriod,
                    'trace.subject': userExactSubject,
                    finished: 1,
                    'curr.period': userPeriod,
                    'curr.subject': userExactSubject
                };
                await db.collection('user').update({ _id: userId }, { $set: setData });
            }
        }
    } else if (token) {
        // 账号密码登录
        let SSOERV = config.get('SSO_SERVER');
        let passportUrl = URL.format({
            protocol: SSOERV.protocol,
            hostname: SSOERV.hostname,
            pathname: '/passport/v1/user/info/',
            port: SSOERV.port,
        });
        let opstions = { headers: { 'Content-Type': 'application/json', }, timeout: 50000 };
        let ssoData = await axios.post(passportUrl, { token: token, login_from: loginFrom, }, opstions);

        let schoolData = await axios.get(config.get('yunxiaoIoSchool'));
        let newSchoolData = {};
        for (let newId of Object.keys(schoolData.data)) {
            newSchoolData[schoolData.data[newId]] = newId;
        }

        let userPeriod = '高中';
        let userExactSubject = '数学';
        if (ssoData.data && ssoData.data.code === 0) {
            let data = ssoData.data.data;
            let schoolId = data.school_id && newSchoolData[data.school_id] || data.school_id;
            schoolId = Number(schoolId);
            user = {
                id: data.id,
                name: data.name,
                grade: data.grade,
                avatar: data.avatar,
                role: data.role,
                schoolId: schoolId,
                schoolName: data.school,
                phone: data.phone,
            };

            let userId = Number(data.id);
            // 使用kb-app登录的内部用户
            if (loginFrom === 'admin' && !config['ALLOW_ADMIN_LOGIN'].includes(Number(data.id))) {
                // return callback('admin');
                return responseWrapper.error('AUTH_ERROR', '没有题库后台管理系统权限！');
            }
            if (loginFrom === 'kbapp' && schoolId) {
                let schoolInfo = await db.collection('school_info').findOne({ _id: schoolId });
                if (schoolInfo && schoolInfo.type === '虚拟校') {
                    // 内部员工重定向至扫码登录界面
                    return res.redirect(config.get('QX_LOGIN'))
                }
            }

            const accessSpot = {
                attachment: [],
                user_id: Number(data.id),
                school_id: Number(schoolId),
                province: '',
                city: '',
                ip: req.ip,
                timestamp: new Date(),
                event_id: 'land_web_times',
            };
            try {
                await db.collection('access_spot').insert(accessSpot);
            } catch (err) {
                console.log(err.stack);
            }

            let userInfo = await db.collection('user').findOne({ _id: userId });
            if (userInfo && userInfo.is_vip && userInfo.expired_time > new Date()) {
                user.isVip = userInfo.is_vip;
                user.vipType = enums.MemberType.TIKU_PERSONAL;
                user.startTime = userInfo.start_time && userInfo.start_time.getTime();
                user.expiredTime = userInfo.expired_time && userInfo.expired_time.getTime();
            }

            if (!userInfo) {
                let userData = {
                    _id: userId,
                    name: user.name,
                    sch_id: user.schoolId,
                    sch_name: user.schoolName,
                    curr: {},
                    trace: {},
                    finished: 0,
                    utime: new Date()
                };
                await db.collection('user').insertOne(userData);
                userInfo = userData;
            }

            let setData = {};
            if (userInfo.trace && userInfo.finished === 1) {
                setData = {
                    name: user.name,
                    sch_id: user.schoolId,
                    sch_name: user.schoolName,
                    'curr.period': userInfo.trace.period,
                    'curr.subject': userInfo.trace.subject
                };
            } else {
                setData = {
                    name: user.name,
                    sch_id: user.schoolId,
                    sch_name: user.schoolName,
                    'trace.period': userPeriod,
                    'trace.subject': userExactSubject,
                    finished: 1,
                    'curr.period': userPeriod,
                    'curr.subject': userExactSubject
                };
            }
            await db.collection('user').update({ _id: userId }, { $set: setData });
        } else {
            // 生涯token跳转
            let data;
            try {
                let SY_AUTH_SERVER = config.get('SY_AUTH_SERVER');
                const ssoInfoUrl = URL.format({
                    protocol: SY_AUTH_SERVER.protocol,
                    hostname: SY_AUTH_SERVER.hostname,
                    pathname: '/api/platform/zsk/user',
                    port: SY_AUTH_SERVER.port,
                });
                const opstions = { headers: {}, timeout: 50000 };
                data = await axios.get(ssoInfoUrl, { params: { sytoken: token } }, opstions).then(utils.handler);
            } catch (err) {
                console.log(err.stack);
            }
            const schoolId = data.school_id && newSchoolData[data.school_id] || data.school_id;
            user = {
                id: Number(data.id),
                name: data.name,
                grade: data.grade,
                avatar: data.avatar,
                role: "学生",
                schoolId: schoolId,
                schoolName: data.school,
                logo: data.logo,
                // type: enums.UserSource.SY,
                source: enums.UserSource.SY,
                nav_bg_color: data.nav_bg_color,
            };
            if (data.sxb_info && enums.SyMemberTypeToMemberType[data.sxb_info.role] && new Date(data.sxb_info.role_expire) > new Date()) {
                user.isVip = true;
                user.vipType = enums.SyMemberTypeToMemberType[data.sxb_info.role];
                user.startTime = data.sxb_info.role_create_time && new Date(data.sxb_info.role_create_time).getTime();
                user.expiredTime = data.sxb_info.role_expire && new Date(data.sxb_info.role_expire).getTime();
            }

            let userInfo = await db.collection('user').findOne({ _id: user.id });
            if (!userInfo) {
                let userData = {
                    _id: user.id,
                    name: user.name,
                    sch_id: user.schoolId,
                    sch_name: user.schoolName,
                    curr: {},
                    trace: {},
                    finished: 0,
                    source: enums.UserSource.SY,
                    role: user.role,
                    utime: new Date()
                };
                await db.collection('user').insertOne(userData);
                userInfo = userData;
            }

            let setData = {
                source: enums.UserSource.SY,
                role: user.role,
                name: user.name,
                sch_id: user.schoolId,
                sch_name: user.schoolName,
                'trace.period': userPeriod,
                'trace.subject': userExactSubject,
                finished: 1,
                'curr.period': userPeriod,
                'curr.subject': userExactSubject
            };
            await db.collection('user').update({ _id: user.id }, { $set: setData });

            const accessSpot = {
                attachment: [],
                user_id: Number(data.id),
                school_id: Number(schoolId),
                province: '',
                city: '',
                ip: req.ip,
                timestamp: new Date(),
                event_id: 'land_web_times',
            };
            try {
                await db.collection('access_spot').insert(accessSpot);
            } catch (err) {
                console.log(err.stack);
            }
        }
    } else if (hfsToken) { // hfs教师端web跳转
        const ssoInfoUrl = URL.format({
            protocol: HFS_TEACHER_SERVER.protocol,
            hostname: HFS_TEACHER_SERVER.hostname,
            pathname: '/teacher-v2/teachers/info/hfs-token',
            port: HFS_TEACHER_SERVER.port,
        });
        const opstions = { params: { hfsToken: hfsToken }, headers: { Cookie: HFS_TEACHER_SERVER.session + '=' + teacherSession }, timeout: 50000 };
        try {
            const data = await axios.get(ssoInfoUrl, opstions).then(utils.handler);

            let schoolData = await axios.get(config.get('yunxiaoIoSchool'));
            let newSchoolData = {};
            for (let newId of Object.keys(schoolData.data)) {
                newSchoolData[schoolData.data[newId]] = newId;
            }
            const schoolId = data.schoolId && newSchoolData[data.schoolId] || data.schoolId;

            const class_ = data.classes[0] || { 'grade': '' };
            user = {
                id: data.id,
                name: data.name,
                grade: class_.grade,
                avatar: data.avatar,
                role: '教师',
                source: enums.UserSource.HFS,
                schoolId: schoolId,
                schoolName: data.schoolName,
                phone: data.phone,
            };
            const accessSpot = {
                attachment: [],
                user_id: Number(data.id),
                school_id: Number(schoolId),
                province: '',
                city: '',
                ip: req.ip,
                timestamp: new Date(),
                event_id: 'land_hfsjs_times',
            };
            await db.collection('access_spot').insert(accessSpot);

            //保存好分数教师角色信息
            let userId = Number(data.id);
            let gradePhase = data.classes[0] && enums.GradePhaseMap[data.classes[0].grade];
            let userPeriod = data.classes[0] ? (['高中', '初中', '小学'].indexOf(gradePhase) === -1 ? '高中' : gradePhase) : '高中';
            let userExactSubject = data.classes[0] && data.classes[0].roles ? (['数学', '语文', '英语', '物理', '化学', '生物', '政治', '历史', '地理', '科学'].indexOf(data.classes[0].roles[0]) === -1 ? '数学' : data.classes[0].roles[0]) : '数学';

            const userInfo = await db.collection('user').findOne({ _id: userId });
            if (userInfo && userInfo.is_vip && userInfo.expired_time > new Date()) {
                user.isVip = userInfo.is_vip;
                user.vipType = enums.MemberType.TIKU_PERSONAL;
                user.startTime = userInfo.start_time && userInfo.start_time.getTime();
                user.expiredTime = userInfo.expired_time && userInfo.expired_time.getTime();
            }

            if (userInfo && userInfo.trace && userInfo.finished === 1) {
                if (userInfo.trace.period !== userPeriod || userInfo.trace.subject !== userExactSubject) {
                    //hfs:表示从好分数带过来的角色信息覆盖了题库的角色, 0-已经通知用户，1-未通知用户
                    let setData = {
                        name: user.name,
                        sch_id: user.schoolId,
                        sch_name: user.schoolName,
                        'trace.period': userPeriod,
                        'trace.subject': userExactSubject,
                        finished: 1,
                        hfs: 1,
                        'curr.period': userPeriod,
                        'curr.subject': userExactSubject,
                        role: user.role,
                        sources: _.uniq((userInfo.sources || []).concat([enums.UserSource.HFS])),
                    };
                    await db.collection('user').update({ _id: userId }, { $set: setData });
                }
            } else {
                let setData = {
                    name: user.name,
                    sch_id: user.schoolId,
                    sch_name: user.schoolName,
                    'trace.period': userPeriod,
                    'trace.subject': userExactSubject,
                    finished: 1,
                    'curr.period': userPeriod,
                    'curr.subject': userExactSubject,
                    role: user.role,
                    sources: _.uniq((userInfo && userInfo.sources || []).concat([enums.UserSource.HFS])),
                };
                await db.collection('user').update({ _id: userId }, { $set: setData });
            }
        } catch (err) {
            Logger.error(err.stack);
        }
    } else if (tikuSession) { // 题库cookie
        try {
            user = jwt.decode(aes.decript(tikuSession));
        } catch (err) {
            Logger.error(err.stack);
            // return responseWrapper.error('AUTH_ERROR', err && err.message || 'cookie err');
        }
    } else if (studentSession) { // 好分数学生cookie
        const ssoInfoUrl = URL.format({
            protocol: HFS_STUDENT_SERVER.protocol,
            hostname: HFS_STUDENT_SERVER.hostname,
            pathname: '/v2/user-center/sso-info',
            port: HFS_STUDENT_SERVER.port,
        });
        const opstions = { headers: { Cookie: HFS_STUDENT_SERVER.session + '=' + studentSession }, timeout: 50000 };
        try {
            const data = await axios.get(ssoInfoUrl, opstions).then(utils.handler);
            const roleMap = { 1: '学生', 2: '家长', 3: '教师' };
            user = {
                id: Number(data.id),
                ucId: data.userId,
                name: data.name,
                grade: data.grade,
                avatar: data.avatar,
                role: roleMap[data.role],
                schoolId: data.schoolId,
                schoolName: data.school,
            };

            // 获取题库会员
            // const ucId = Number(data.userId);
            const userInfo = await db.collection('user').findOne({ _id: Number(data.id), is_vip: true });
            if (userInfo && userInfo.is_vip && userInfo.expired_time > new Date()) {
                user.isVip = userInfo.is_vip;
                user.vipType = enums.MemberType.TIKU_PERSONAL;
                user.startTime = userInfo.start_time && userInfo.start_time.getTime();
                user.expiredTime = userInfo.expired_time && userInfo.expired_time.getTime();
            }
        } catch (err) {
            Logger.error(err.stack);
            // return responseWrapper.error('AUTH_ERROR', err && err.message || 'cookie err');
        }
    } else if (teacherSession) { // 好分数老师cookie
        const ssoInfoUrl = URL.format({
            protocol: HFS_TEACHER_SERVER.protocol,
            hostname: HFS_TEACHER_SERVER.hostname,
            pathname: '/teacher-v2/teachers/info',
            port: HFS_TEACHER_SERVER.port,
        });
        const opstions = { headers: { Cookie: HFS_TEACHER_SERVER.session + '=' + teacherSession }, timeout: 50000 };
        try {
            const data = await axios.get(ssoInfoUrl, opstions).then(utils.handler);

            let schoolData = await axios.get(config.get('yunxiaoIoSchool'));
            let newSchoolData = {};
            for (let newId of Object.keys(schoolData.data)) {
                newSchoolData[schoolData.data[newId]] = newId;
            }
            const schoolId = data.schoolId && newSchoolData[data.schoolId] || data.schoolId;

            const class_ = data.classes[0] || { 'grade': '' };
            user = {
                id: data.id,
                name: data.name,
                grade: class_.grade,
                avatar: data.avatar,
                role: '教师',
                source: enums.UserSource.HFS,
                schoolId: schoolId,
                schoolName: data.schoolName,
                phone: data.phone,
            };
            const accessSpot = {
                attachment: [],
                user_id: Number(data.id),
                school_id: Number(schoolId),
                province: '',
                city: '',
                ip: req.ip,
                timestamp: new Date(),
                event_id: 'land_hfsjs_times',
            };

            await db.collection('access_spot').insert(accessSpot);

            //保存好分数教师角色信息
            let userId = Number(data.id);
            let gradePhase = data.classes[0] && enums.GradePhaseMap[data.classes[0].grade];
            let userPeriod = data.classes[0] ? (['高中', '初中', '小学'].indexOf(gradePhase) === -1 ? '高中' : gradePhase) : '高中';
            let userExactSubject = data.classes[0] && data.classes[0].roles ? (['数学', '语文', '英语', '物理', '化学', '生物', '政治', '历史', '地理', '科学'].indexOf(data.classes[0].roles[0]) === -1 ? '数学' : data.classes[0].roles[0]) : '数学';

            const userInfo = await db.collection('user').findOne({ _id: userId });
            if (userInfo && userInfo.is_vip && userInfo.expired_time > new Date()) {
                user.isVip = userInfo.is_vip;
                user.vipType = enums.MemberType.TIKU_PERSONAL;
                user.startTime = userInfo.start_time && userInfo.start_time.getTime();
                user.expiredTime = userInfo.expired_time && userInfo.expired_time.getTime();
            }

            if (userInfo && userInfo.trace && userInfo.finished === 1) {
                if (userInfo.trace.period !== userPeriod || userInfo.trace.subject !== userExactSubject) {
                    //hfs:表示从好分数带过来的角色信息覆盖了题库的角色, 0-已经通知用户，1-未通知用户
                    let setData = {
                        name: user.name,
                        sch_id: user.schoolId,
                        sch_name: user.schoolName,
                        'trace.period': userPeriod,
                        'trace.subject': userExactSubject,
                        finished: 1,
                        hfs: 1,
                        'curr.period': userPeriod,
                        'curr.subject': userExactSubject,
                        role: user.role,
                        sources: _.uniq((userInfo.sources || []).concat([enums.UserSource.HFS])),
                    };
                    // if (data.is_vip) {
                    //     setData[`members.${enums.MemberType.HFS_360}`] = {
                    //         source: enums.UserSource.HFS,
                    //         type: enums.MemberType.HFS_360,
                    //         begin_date: data.begin_date,
                    //         end_date: data.end_date,
                    //     }
                    // }
                    await db.collection('user').update({ _id: userId }, { $set: setData });
                }
            } else {
                let setData = {
                    name: user.name,
                    sch_id: user.schoolId,
                    sch_name: user.schoolName,
                    'trace.period': userPeriod,
                    'trace.subject': userExactSubject,
                    finished: 1,
                    'curr.period': userPeriod,
                    'curr.subject': userExactSubject,
                    role: user.role,
                    sources: _.uniq((userInfo && userInfo.sources || []).concat([enums.UserSource.HFS])),
                };
                // if (data.is_vip) {
                //     setData[`members.${enums.MemberType.HFS_360}`] = {
                //         source: enums.UserSource.HFS,
                //         type: enums.MemberType.HFS_360,
                //         begin_date: data.begin_date,
                //         end_date: data.end_date,
                //     }
                // }
                await db.collection('user').update({ _id: userId }, { $set: setData });
            }
        } catch (err) {
            Logger.error(err.stack);
            // return responseWrapper.error('AUTH_ERROR', err && err.message || 'cookie err');
        }
    }

    if (!user) {
        if (req.query.device === 'mobile') {
            return responseWrapper.error('AUTH_ERROR', '没有权限！');
        }
        res.setHeader('content-type', 'text/html');
        return res.end(require('fs').readFileSync('./dist/teacher.html').toString());
    }

    // set cookie
    req.query.api_key = KBSERVER.appKey;
    let _user = {
        id: parseInt(user.id),
        ucId: user.ucId,
        isVip: user.isVip || false,
        name: user.name,
        province: user.province,
        city: user.city,
        role: user.role,
        grade: user.grade,
        avatar: user.avatar,
        need_bind: user.need_bind,
        qxid: user.qxid,
        expireMonth: DEFAULT_EXPIRE_MONTH,

    };

    if (user.isVip) {
        _user.vipType = user.vipType;
        _user.startTime = user.startTime;
        _user.expiredTime = user.expiredTime;
    }
    if (user.schoolId) {
        _user.schoolId = parseInt(user.schoolId);
        _user.schoolName = user.schoolName;
    }
    let schoolInfo;
    let bossData;
    // if (user.schoolId && user.role === '教师') schoolInfo = await db.collection('school_info').findOne({ _id: user.schoolId });
    // if (user.vipType !== enums.MemberType.TIKU_PERSONAL && user.schoolId && user.role === '教师') {
    //     if (schoolInfo && schoolInfo.teachers && schoolInfo.teachers.includes(_user.id) && Object.values(enums.MemberType).includes(schoolInfo.vip_type) && schoolInfo.expired_time > new Date()) {
    //         _user.isVip = true;
    //         _user.vipType = schoolInfo.vip_type;
    //         _user.startTime = schoolInfo.start_time && schoolInfo.start_time.getTime();
    //         _user.expiredTime = schoolInfo.expired_time && schoolInfo.expired_time.getTime();
    //         _user.expireMonth = schoolInfo.expire_month || DEFAULT_EXPIRE_MONTH;
    //     } else if (schoolInfo && Object.values(enums.YjSchoolVersionTypeToMemberType).includes(schoolInfo.vip_type) && schoolInfo.expired_time > new Date()) {
    //         _user.isVip = true;
    //         _user.vipType = schoolInfo.vip_type;
    //         _user.startTime = schoolInfo.start_time && schoolInfo.start_time.getTime();
    //         _user.expiredTime = schoolInfo.expired_time && schoolInfo.expired_time.getTime();
    //         _user.expireMonth = schoolInfo.expire_month || DEFAULT_EXPIRE_MONTH;
    //     }
    // }
    if (user.schoolId && user.role === '教师') {
        schoolInfo = await db.collection('school_info').findOne({ _id: user.schoolId });
        let str = await rediser.redis.get(`tiku:boss:school:${user.schoolId}`);
        bossData = JSON.parse(str);
        if (!bossData) {
            let BOSSSERV = config.get('BOSS_SERVER');
            let bossUrl = URL.format({
                protocol: BOSSSERV.protocol,
                hostname: BOSSSERV.hostname,
                pathname: '/external/api/customer/app_usage/get_by_school_id',
                port: BOSSSERV.port,
                query: {
                    schoolId: user.schoolId,
                    productCategory: 'tiku',
                }
            });
            let opstions = { headers: { 'apikey': BOSSSERV.apikey }, timeout: 50000 };
            bossData = await axios.get(bossUrl, opstions).then(utils.bossHandler);
            rediser.redis.setex(`tiku:boss:school:${user.schoolId}`, 200, JSON.stringify(bossData));
        }
    };
    if (user.vipType !== enums.MemberType.TIKU_PERSONAL && user.schoolId && user.role === '教师') {
        if (schoolInfo && schoolInfo.teachers && schoolInfo.teachers.includes(_user.id) && bossData.appUsages && bossData.appUsages[0] && bossData.appUsages[0].status && new Date(bossData.appUsages[0].endDate) > new Date()) {
            _user.isVip = true;
            _user.vipType = enums.CrmVersionToMemberType[bossData.appUsages[0].name];
            _user.startTime = new Date(bossData.appUsages[0].beginDate).getTime();
            _user.expiredTime = new Date(bossData.appUsages[0].endDate).getTime();
            _user.expireMonth = schoolInfo.expire_month || DEFAULT_EXPIRE_MONTH;
        } else if (schoolInfo && Object.values(enums.YjSchoolVersionTypeToMemberType).includes(schoolInfo.vip_type) && schoolInfo.expired_time > new Date()) {
            _user.isVip = true;
            _user.vipType = schoolInfo.vip_type;
            _user.startTime = schoolInfo.start_time && schoolInfo.start_time.getTime();
            _user.expiredTime = schoolInfo.expired_time && schoolInfo.expired_time.getTime();
            _user.expireMonth = schoolInfo.expire_month || DEFAULT_EXPIRE_MONTH;
        }
    }
    if (user.logo) _user.logo = user.logo;
    if (user.type) _user.type = user.type;
    if (user.nav_bg_color) _user.nav_bg_color = user.nav_bg_color;

    let newExp = Date.now() + cookie.options.maxAge;
    let preExp = user.exp;
    if (!preExp || newExp - new Date(preExp) > cookie.interval) {
        _user.exp = new Date(newExp);
        let value = aes.encript(jwt.encode(_user));
        res.cookie(cookie.name, value, cookie.options);
        res.cookie(config.get('TIKU_SERVER').userInfo.name, JSON.stringify(_user), cookie.options);
    }

    req.user = _user;

    // 接口权限检查
    if (utils.isMobileByUa(req.headers['user-agent'])) {
        req.query.device = 'mobile';
    }
    if (!utils.isMobileByUa(req.headers['user-agent'])) {
        let apiName = getApiName(req);
        if (!apiName) {
            return next();
        }

        let userId = user.id;
        let userInfo = await db.collection('user').findOne({ _id: userId });

        if (!userInfo) {
            let userPeriod = '高中';
            let userExactSubject = '数学';
            let userData = {
                _id: userId,
                name: user.name,
                sch_id: user.schoolId,
                sch_name: user.schoolName,
                trace: {
                    period: userPeriod,
                    subject: userExactSubject,
                },
                curr: {
                    period: userPeriod,
                    subject: userExactSubject,
                },
                finished: 1,
                utime: new Date(),
                ctime: new Date(),
            };
            await db.collection('user').insertOne(userData);
            userInfo = userData;
        }
        let useNum = {
            que_download_num: 0,
            exampaper_download_num: 0,
            assemble_download_num: 0,
            que_details_num: 0,
        };
        if (userInfo.use_time && userInfo.use_time > moment().startOf('month').toDate()) {
            useNum.que_download_num = userInfo.que_download_num;
            useNum.exampaper_download_num = userInfo.exampaper_download_num;
            useNum.assemble_download_num = userInfo.assemble_download_num;
            useNum.que_details_num = userInfo.que_details_num;
        } else {
            await db.collection('user').updateOne({ _id: userId }, { $set: useNum });
        }
        useNum[apiName] += 1;
        if (apiName === 'que_details_num') {
            return next();
        }

        if (!Object.values(enums.MemberType).includes(_user.vipType)) {
            let memberInfo = VISIT_TRIAL_DEFAULT_LIMIT;
            if (memberInfo[apiName] < useNum[apiName]) {
                return responseWrapper.error('EXCEED_FRQ_ERROR', '您的账号已达本月数量上限');
            }
        } else if (_user.vipType === enums.MemberType.TIKU_PERSONAL || _user.vipType === enums.MemberType.HFS_360 || Object.values(enums.SyMemberTypeToMemberType).includes(_user.vipType)) {
            let memberInfo = VISIT_DEFAULT_LIMIT;
            if (memberInfo[apiName] < useNum[apiName]) {
                return responseWrapper.error('EXCEED_FRQ_ERROR', '您的账号已达本月数量上限');
            }
        }
    }
    return next();
}

function getApiName(data) {
    let method = data.method.toUpperCase();
    let path = data.path;
    let pathregs = METHOD_PATHREG[method];

    for (let i in pathregs) {
        if (pathregs.hasOwnProperty(i)) {
            let desc = pathregs[i];
            let m = desc.reg.exec(path);
            if (m) {
                if (desc.api_name === 'exampaper_download_num' || desc.api_name === 'que_download_num' || desc.api_name === 'assemble_download_num') {
                    if (data.body.resource_type === 'exampaper') {
                        return 'exampaper_download_num';
                    } else if (data.body.resource_type === 'question') {
                        return 'que_download_num';
                    } else {
                        return 'assemble_download_num'
                    }
                }
                return desc.api_name;
            }
        }
    }
    return null;
};

module.exports = verify;
