const request = require('request');
const ResponseWrapper = require('../../middlewares/response_wrapper');
const Readable = require('stream').Readable;
const fs = require('fs');
const path = require('path');
const AcceptWordAPI = require('config').get('EXTERNAL_API.AcceptWord');
const crypto = require('crypto');
const Config = require('config');
const qs = require('querystring');
const URL = require('url');
const _  = require('underscore');
const md5 = crypto.createHash('md5');
const sha = require('sha-1');
const Logger = require('../../utils/logger');
const mongodber = require('../../utils/mongodber');
const WebSocketClient = require('websocket').client;
const appKey = '1481275433000006';
const secretKey = '476e4692be57bd5e83bcb81e4ebcca93';
const db = mongodber.use('tiku');
const mapExampaperGrade = require('./mapBookGrade.json');

var defaultBook = {
    '高中': {},
    '初中': {},
    '小学': {},
};

var defaultBook3D = {};

function _getPeriodByGrade(grade){

	if(!grade){
		return '初中';
	}

	if(grade.indexOf('高') === 0)
		return '高中';

	var middle = ['初', '七', '八', '九'];
	for(var ix in middle) {
		if(grade.indexOf(middle[ix]) >= 0)
			return '初中';
	}
	return '小学';
}

function _setAllDefaultBook(appKey){
    var KB_API_SERVER = Config.get('KB_API_SERVER');
    var bookApiUrl = URL.format({
        protocol: KB_API_SERVER.protocol,
        hostname: KB_API_SERVER.hostname,
        port: KB_API_SERVER.port,
        pathname: '/kb_api/v2/books',
        search: qs.stringify({
            api_key: appKey
        })
    });

    request({
        url: bookApiUrl,
    }, function(err, response, body){
        if(err){
            Logger.error(`request url: ${bookApiUrl}, error: ${err.message}`);
			process.exit(-1);
        }
        try{
            var _body = JSON.parse(body);
            if(response.statusCode === 200){
                _.each(_body.book.children, function(period){

                    if(!defaultBook3D[period.name])
                        defaultBook3D[period.name] = {};

                    _.each(period.children, function(subject){
                        if(!defaultBook3D[period.name][subject.name]){
                            defaultBook3D[period.name][subject.name] = {};
                        }

                        var pressVersion = subject.children[0].name;
                        _.each(subject.children[0].children, function(grade){
                            var gradeName = grade.name;
                            defaultBook3D[period.name][subject.name][gradeName] = {
                                type: 'book',
                                period: period.name,
                                subject: subject.name,
                                press_version: pressVersion,
                                grade: gradeName,
                                id: grade.id
                            };
                        });
                    });
                });
            }
        }catch(err){
            process.exit(-1);
        }
    });
}

(function(){
 	var appKey = Config.get('KB_API_SERVER').appKey;
    _setAllDefaultBook(appKey);
})();

/*
 * 定义支持的文件
 */
const supportAudioType = ['mp3', 'amr'];
const supportType = {
    eng_word: 'word.eval',
    eng_sentence: 'sent.eval'
};

/**
 * 随机生成UUID的函数
 */
var _createUUID = (function (uuidRegEx, uuidReplacer) {
    return function () {
        return 'xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx'.replace(uuidRegEx, uuidReplacer).toUpperCase();
    };
})(/[xy]/g, function (c) {
    var r = Math.random() * 16 | 0,
        v = c == 'x' ? r : (r & 3 | 8);
    return v.toString(16);
});

function addScores(req, res){

    var responseWrapper = null,
        body = null,
        buffer = null,
        readStream = null,
        client = null;

    client = new WebSocketClient();

    try{
        responseWrapper = new ResponseWrapper(req, res);
        readStream = new Readable();
        readStream._read = function () {};

        body = req.body;
        var recoType = body.type;
        if(typeof body.id)
            body.id = String(body.id);

        if(!body)
            throw new TypeError('必须有数据体');

        if(!body.text || typeof body.text !== 'string')
            throw new TypeError('text 必须是字符串且不能为空');

        if(!body.audio_type || typeof body.audio_type !== 'string'){
            throw new TypeError('audio_type必须是字符串且不能为空');
        }

        if(supportAudioType.indexOf(body.audio_type) < 0){
            throw new TypeError('audio_type 必须取值'+supportAudioType.join(' '));
        }

        if(body.type === 'eng_sentence')
            body.type = supportType['eng_sentence'];
        else
            body.type = supportType['eng_word'];

		if(body.text.trim().split(/\s+/g).length > 1)
			body.type = supportType['eng_sentence'];

        body.sample_rate = Number(body.sample_rate);
        if(!body.sample_rate || typeof body.sample_rate !== 'number')
            throw new TypeError('sample_rate必须是数字且不能为0');

        if(!body.audio || typeof body.audio !== 'string')
            throw new TypeError('audio 必须为base64数据');

        buffer = new Buffer(body.audio, 'base64');
        var unique_userId = req.user.id;

        if(typeof unique_userId === 'number')
            unique_userId = 1000000 % unique_userId;

        var start_timestamp = new Date().getTime().toString();
        var init_timestamp = start_timestamp;
        var start_sig = sha(appKey + start_timestamp + unique_userId + secretKey);
        var init_sig = sha(appKey + init_timestamp + secretKey);
        var t_tokenId = _createUUID();

        var connectCmd = {
            'cmd': 'connect',
            'param': {
                'sdk': {
                    'version': 16777472,
                    'source': 4,
                    'protocol': 1
                },
                'app': {
                    'applicationId': appKey,
                    'sig': init_sig,
                    'timestamp': init_timestamp
                }
            }
        };

        var startCmd = {
            'cmd': 'start',
            'param': {
                'app': {
                    'applicationId': appKey,
                    'sig': start_sig ,
                    'userId': unique_userId ,
                    'timestamp': start_timestamp
                },
                'audio': {
                    'audioType': body.audio_type,
                    'sampleRate': body.sample_rate,
                    'channel': 1,
                    'sampleBytes': 2
                },
                'request': {
                    'getParam': 0,
                    'coreType': body.type,
                    'attachAudioUrl': 0,
                    'refText': body.text,
                    'dict_type': 'KK',
                    'phoneme_output': 0,
                    'tokenId': t_tokenId
                }
            }
        };

        client.on('connect', function(connection) {
            connection.on('message', function(message) {
                if (message.type === 'utf8') {
                    var retBody = null;
                    try{
                        var body = JSON.parse(message.utf8Data);
                        if(body.error)
                            throw new Error(body.error);
                        if(!body.result)
                            throw new Error('without result');
                        retBody = {
                            overall: body.result.overall,
                            pronunciation: body.result.pronunciation
                        };
                    }catch(err){
                        process.nextTick(function(){
                            connection.close();
                        });
                        return responseWrapper.error('HANDLE_ERROR', err.message);
                    }

                    var KB_API_SERVER = Config.get('KB_API_SERVER');

                    var bookApiUrl = URL.format({
                        protocol: KB_API_SERVER.protocol,
                        hostname: KB_API_SERVER.hostname,
                        port: KB_API_SERVER.port,
                        pathname: '/kb_api/v2/'+recoType+'s/'+req.body.id+'/score/',
                    });


                    var query = qs.stringify({
                        api_key: req.apiKey,
                        score: retBody.overall
                    });

                    var putBody = {
                        score: retBody.overall,
                        id: req.user.id,
                        name: req.user.name
                    };

                    request.put({
                        url: [bookApiUrl, query].join('?'),
                        headers: {
                            'content-type': 'application/json'
                        },
                        body: JSON.stringify(putBody)
                    }, function(err, response, body){

                        if(err){
                            return responseWrapper.error('HANDLE_ERROR');
                        }

                        try{
                            var _body = JSON.parse(body);
                        }catch(err){
                            process.nextTick(function(){
                                connection.close();
                            });
                            return responseWrapper.error('HANDLE_ERROR', err.message);
                        }

                        process.nextTick(function(){
                            connection.close();
                        });

                        retBody.win_rate = _body.win_rate;
                        return responseWrapper.succ(retBody);
                    });
                }
            });

            var jsc = JSON.stringify(connectCmd, '\t', 3);
            connection.send(jsc);
            startCmd = JSON.stringify(startCmd);
            connection.send(startCmd);
            connection.send(buffer);
            connection.send(new Buffer(0));
        });

        client.connect([AcceptWordAPI, body.type].join('/'));

    }catch(err){
        if(err.name === 'TypeError'){
            return responseWrapper.error('PARAMETERS_ERROR', err.message);
        }
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
}

/**
 * 通过ID获取书的回调函数！将返回值写入到retobj的book里
 */
function _requestBookByIdCb(req, res, retobj){
    var responseWrapper = new ResponseWrapper(req, res);
    return function(err, body){
        if(typeof err === 'string'){
            return responseWrapper.error(err);
        }

        if(err instanceof Error){
            return responseWrapper.error(err, err.message || undefined);
        }

        retobj.book = body;
        return responseWrapper.succ(retobj);
    };
}

/**
 * 根据book id 获取book内容
 */
function _requestBookById(id, api_key, done){

    var KB_API_SERVER = Config.get('KB_API_SERVER');

    var bookApiUrl = URL.format({
        protocol: KB_API_SERVER.protocol,
        hostname: KB_API_SERVER.hostname,
        port: KB_API_SERVER.port,
        pathname: '/kb_api/v2/books/'+id+'/',
        search: qs.stringify({
            api_key,
            fields_type: 'eng_book'
        })
    });
    request({
        url: bookApiUrl,
    }, function(err, response, body){

        if(err){
			return done(err);
        }

		try{
			var _body = JSON.parse(body);
			if(response.statusCode !== 200){
				if(_body.code == 3)
					return done('PARAMETERS_ERROR');
				if(_body.code == 5)
					return done('NULL_ERROR');
			}
			var book = _body.book;
			if(!book){
				return done('HANDLE_ERROR');
			}
		}catch(err){
			Logger.error(err.message);
			return done(err, null);
		}

        return done(null, book);
    });
}

/**
 * Method:  GET
 * URI：/english_api/v1/speaking/book/
 * Wiki: http://wiki.iyunxiao.com/pages/viewpage.action?pageId=26128382#id-英语跟读API-获取当期英语跟读教材
 */
function getSpeakBook(req, res){

    if(req.headers['if-none-match'])
        delete req.headers['if-none-match'];

    var responseWrapper = new ResponseWrapper(req, res);
    var retobj = {};
	var _grade = req.query.grade || req.user.grade;
	var period = _getPeriodByGrade(_grade);
    var subject = req.query.subject ? req.query.subject: '英语';
    var apiKey = req.apiKey;
    var grade = mapExampaperGrade[period][_grade];

    db.collection('user').findOne({_id: req.user.id}, function(err, docUser){
        if(err){
			Logger.error(err.message);
            return responseWrapper.error('HANDLE_ERROR', err.message);
        }
        if(docUser == null){
            docUser = {};
        }

		try{
			if(period === '高中')
				docUser.chieng_book_profile = null;

			if(!docUser.chieng_book_profile){
				docUser.chieng_book_profile = grade ?
					[defaultBook3D[period][subject][grade]]:
					[defaultBook[period][subject]];
			}
			if(!docUser.chieng_book_profile[0]){
				throw new Error();
			}
		}catch(err){
			return responseWrapper.error('NULL_ERROR', '此学段尚未开通本功能');
		}
        var id = docUser.chieng_book_profile[0].id;
        return _requestBookById(id, apiKey, function(err, book){
            if(typeof err === 'string'){
                return responseWrapper.error(err);
            }

            if(err instanceof Error){
                return responseWrapper.error(err, err.message || undefined);
            }

            docUser.chieng_book_profile[0].book = book;
            if(!docUser.chieng_book_profile[0].trace || docUser.chieng_book_profile[0].trace.length === 0)
                docUser.chieng_book_profile[0].trace = _getDefaultTraceFromBook(book);

            return responseWrapper.succ(docUser.chieng_book_profile[0]);
        });
    });
}

/**
 * Method: PUT
 * URI：/english_api/v1/speaking/book/
 * Wiki: http://wiki.iyunxiao.com/pages/viewpage.action?pageId=26128382#id-英语跟读API-修改当前英语同步教材
 */
function updateSpeakBookProfile(req, res){
    var body = req.body;
    var userId = req.user.id;
    var api_key = req.apiKey;
    var responseWrapper = new ResponseWrapper(req, res);
    try{
        if(!body.id ||
		   !body.type ||
		   !body.press_version ||
		   !body.grade)
            throw new TypeError('body 缺少必要参数!');

        var putBody = {};
        putBody.id = Number(body.id);
		if(isNaN(putBody.id))
			throw new TypeError('id必须是整数');

        putBody.type = String(body.type);
        putBody.period = String(body.period);
        putBody.subject = String(body.subject);
        putBody.press_version = String(body.press_version);
        putBody.grade = String(body.grade);

        return _requestBookById(putBody.id, api_key, function(err, book){

            if(typeof err === 'string'){
                return responseWrapper.error(err);
            }

			if(err){
				return responseWrapper.error('HANDLE_ERROR', err.message);
			}

			if(!book){
				db.collection('user').updateOne({
					_id: userId
				},{
					$set: {
						chieng_book_profile: null
					}
				});
				return responseWrapper.error('NULL_ERROR', '新设置的教材ID其实不存在');
			}

			db.collection('user').updateOne({
				_id: userId
			}, {
				$set: {
					chieng_book_profile: [putBody]
				}
			}, {
				upsert: true
			}, function(err, writeResult){

				if(err){
					return responseWrapper.error('HANDLE_ERROR', err.message);
				}

				putBody.trace = _getDefaultTraceFromBook(book);
				var chapter = (function(trace, book){
					try{
						var chapter = _.find(book, function(b){
							return b.name === trace[0];
						});

						if(trace.length === 1 || !chapter.children){
							return chapter;
						}
						return arguments.callee(trace.slice(1), chapter.children);
					}catch(err){
						Logger.error(err.message);
						return null;
					}
				})(putBody.trace, book.children);

				if(!chapter){
					return responseWrapper.error('HANDLE_ERROR');
				}

				return _getRecoFromChapter(chapter.children, api_key, function(err, data){
					if(err){
						Logger.error(err.message);
						return responseWrapper.error('HANDLE_ERROR', err.message);
					}
					if(data == null){
						return responseWrapper.error('NULL_ERROR');
					}
					return _returnReco(responseWrapper, putBody, data);
				});
			});
		});
    }catch(err){
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
}

/**
 * 获取模本书的默认第一节的路径
 */
function _getDefaultTraceFromBook(book){

    var node = book,
        trace = [];

    while(node){
        if(node.name && node.key === 'chapter')
            trace.push(node.name);
        else if(node.label && node.key === 'chapter')
            trace.push(node.label);

		if (node.children) {
        	node = node.children[0];
		} else {
			break;
		}
    }

    return trace;
}

/**
 * Method: PUT
 * URI：/english_api/v1/speaking/book/trace/
 * Wiki: http://wiki.iyunxiao.com/pages/viewpage.action?pageId=26128382#id-英语跟读API-修改英语同步资源下章节
 */
function updateBookTrace(req, res){

    var body = req.body;
    var userId = req.user.id;
	var apiKey = req.apiKey;
    var responseWrapper = new ResponseWrapper(req, res);
	var _grade = req.query.grade || body.grade || req.user.grade;
	var period = _getPeriodByGrade(_grade);
    var grade = mapExampaperGrade[period][_grade];

    try{
        if(!body.id ||
		   !body.type ||
		   !body.trace)
            throw new TypeError('body 缺少必要参数!');

        var putBody = {};
        putBody.type = body.type;
        putBody.trace = body.trace,
        putBody.id = body.id;

        /**
		 * 这里还缺一次椒盐
		 */
        db.collection('user').findOne({_id: userId}, {chieng_book_profile: 1}, function(err, docUser){
            if(err){
                return responseWrapper.error('HANDLE_ERROR', err.message);
            }

			try{
				if(!docUser)
					docUser = {
						_id: userId
					};

				if(period === '高中')
					docUser.chieng_book_profile = null;

				if(!docUser.chieng_book_profile){
					docUser.chieng_book_profile = grade ?
						[defaultBook3D[period]['英语'][grade]] :
						[defaultBook[period]['英语']];
				}
			}catch(err){
				return responseWrapper.error('HANDLE_ERROR');
			}

			return _requestBookById(putBody.id, apiKey, function(err, book){

				if(typeof err === 'string'){
					return responseWrapper.error(err);
				}

				if(err){
					Logger.error(err.message);
					return responseWrapper.error('HANDLE_ERROR', err.message);
				}

				if(!book){
					return responseWrapper.error('HANDLE_ERROR');
				}
                if (!docUser.chieng_book_profile[0]) {
                    return responseWrapper.error('HANDLE_ERROR');
                }

				docUser.chieng_book_profile[0].trace = body.trace;

				var chapter = (function(trace, book){
					try{
						var chapter = _.find(book, function(b){
							return b.name === trace[0];
						});

						if(trace.length === 1 || !chapter.children){
							return chapter;
						}
						return arguments.callee(trace.slice(1), chapter.children);
					}catch(err){
						return null;
					}
				})(putBody.trace, book.children);

				if(!chapter){
					return responseWrapper.error('PARAMETERS_ERROR', 'trace与教材并不对应');
				}

				db.collection('user').updateOne({
					_id: userId
				}, {
					$set: {
						chieng_book_profile: docUser.chieng_book_profile
					},
				}, true, function(err, writeResult){
					if(err){
						return responseWrapper.error('HANDLE_ERROR', err.message);
					}
					return responseWrapper.succ(null);
				});
			});
        });
    }catch(err){
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
}

/**
 * 获取资源中的第一个句子
 */
function getEnOneS(recos, api_key, done) {

    var KB_API_SERVER = Config.get('KB_API_SERVER');
    var eng_sentence_id = recos.id;

    var url = URL.format({
        protocol: KB_API_SERVER.protocol,
        hostname: KB_API_SERVER.hostname,
        port: KB_API_SERVER.port,
        pathname: '/kb_api/v2/eng_sentences/'+eng_sentence_id+'/',
        search: qs.stringify({
            api_key
        })
    });

    request({ url: url, }, function(err, response, body){
        if(err){
            Logger.error(err);
            return done('HANDLE_ERROR');
        }

        var _body = JSON.parse(body);
        if(response.statusCode !== 200){
            if(_body.code == 3)
                return done('PARAMETERS_ERROR');
            if(_body.code == 5)
                return done('NULL_ERROR');
        }
        return done(null, _body[0]);
    });
}

/**
 * 获取对话中的第一个句子
 */
function getEnOneDS(recos, api_key, done) {

    var KB_API_SERVER = Config.get('KB_API_SERVER');
    var eng_dialog_id = recos.id;

    var url = URL.format({
        protocol: KB_API_SERVER.protocol,
        hostname: KB_API_SERVER.hostname,
        port: KB_API_SERVER.port,
        pathname: '/kb_api/v2/eng_dialogs/'+eng_dialog_id+'/',
        search: qs.stringify({
            api_key
        })
    });

    request({url: url, }, function(err, response, body){
        if(err){
            Logger.error(err);
            return done('HANDLE_ERROR');
        }

        var _body = JSON.parse(body);
        if(response.statusCode !== 200){
            if(_body.code == 3)
                return done('PARAMETERS_ERROR');
            if(_body.code == 5)
                return done('NULL_ERROR');
        }
        var sentence = _body[0].dialog[0].sentences[0];
        return done(null, sentence);
    });
}

/**
 * 获取资源中的第一个单词
 */
function getEnOneWord(recos, api_key, done) {

    var KB_API_SERVER = Config.get('KB_API_SERVER');
    var eng_word_id = recos.id;

    var url = URL.format({
        protocol: KB_API_SERVER.protocol,
        hostname: KB_API_SERVER.hostname,
        port: KB_API_SERVER.port,
        pathname: '/kb_api/v2/eng_words/'+eng_word_id+'/',
        search: qs.stringify({
            api_key
        })
    });

    request({url: url, }, function(err, response, body) {
        if(err){
            Logger.error(err);
            return done('HANDLE_ERROR');
        }

        var _body = JSON.parse(body);
        if(response.statusCode !== 200){
            if(_body.code == 3)
                return done('PARAMETERS_ERROR');
            if(_body.code == 5)
                return done('NULL_ERROR');
        }
        return done(null, _body[0]);
    });
}

function _returnReco(responseWrapper, body, reco){

    var retobj = {
        id: body.id,
        type: body.type,
        period: body.period,
        subject: body.subject,
        press_version: body.press_version,
        grade: body.grade,
        trace: body.trace,
        reco: reco
    };

    return responseWrapper.succ(retobj);
}

function _getRecoFromChapter(chapter, api_key, done){

    var reco = _.find(chapter, function(ch){
        return ch.key === 'eng_sentence';
    });

    if(reco) {
        return getEnOneS(reco, api_key, function(err, data){
            return done(err, data);
        });
    }

    reco = _.find(chapter, function(ch){
        return ch.key === 'eng_dialog';
    });

    if(reco) {
        return getEnOneDS(reco, api_key, function(err, data){
            return done(err, data);
        });
    }

    reco = _.find(chapter, function(ch){
        return ch.key === 'eng_word';
    });

    if(reco) {
        return getEnOneWord(reco, api_key, function(err, data){
            return done(err, data);
        });
    }

    return done(null, null);
}

/**
 * Method: GET
 * URI: /english_api/v1/speaking/reco/
 * Wiki:
 */
function getSpeakingReco(req, res) {
    var responseWrapper = new ResponseWrapper(req, res);
    var userId = req.user.id;
    var apiKey = req.apiKey;
    var api_key = apiKey;
	var _grade = req.query.grade || req.user.grade;
	var period = _getPeriodByGrade(_grade);
    var subject = req.query.subject || '英语';
    var grade = mapExampaperGrade[period][_grade];

    db.collection('user').findOne({
        _id: userId
    }, {
        chieng_book_profile: 1
    }, function(err, docUser){
        if(err){
            return responseWrapper.error('HANDLE_ERROR', err.message);
        }
		try {
			if(period === '高中'){
				docUser = null;
			}

			if(!docUser || !docUser.chieng_book_profile){
				docUser = {
					_id: userId,
					chieng_book_profile : grade ?
						[defaultBook3D[period][subject][grade]]:
						[defaultBook[period][subject]]
				};
			}

			if(!docUser.chieng_book_profile[0]){
				return responseWrapper.error('NULL_ERROR', '对不起，此学段尚未开通本功能');
			}
		}catch(err){
			return responseWrapper.error('NULL_ERROR', '对不起，此学段尚未开通本功能');
		}

        var id = docUser.chieng_book_profile[0].id;
        return _requestBookById(id, apiKey, function(err, body){
            if(typeof err === 'string'){
                return responseWrapper.error(err);
            }

            if(err instanceof Error){
                return responseWrapper.error(err, err.message || undefined);
            }

            if(!Array.isArray(docUser.chieng_book_profile[0].trace) ||
				docUser.chieng_book_profile[0].trace.length === 0)
                docUser.chieng_book_profile[0].trace = _getDefaultTraceFromBook(body);

            var chapter = (function(trace, _body){
                try{
                    var chapter = _.find(_body, function(b){
                        return b.name === trace[0];
                    });

                    if(trace.length === 1 || !chapter.children){
                        return chapter;
                    }
                    return arguments.callee(trace.slice(1), chapter.children);
                }catch(err){
                    return null;
                }
            })(docUser.chieng_book_profile[0].trace, body.children);

            if(!chapter){
                docUser.chieng_book_profile[0].trace = _getDefaultTraceFromBook(body);
                chapter = (function(trace, _body){
                    try{
                        var chapter = _.find(_body, function(b){
                            return b.name === trace[0];
                        });

                        if(trace.length === 1 || !chapter.children){
                            return chapter;
                        }
                        return arguments.callee(trace.slice(1), chapter.children);
                    }catch(err){
                        return null;
                    }
                })(docUser.chieng_book_profile[0].trace, body.children);
            }

            if(!chapter){
                return responseWrapper.error('HANDLE_ERROR');
            }

            return _getRecoFromChapter(chapter.children, api_key, function(err, data){
                if(err){
                    return responseWrapper.error('HANDLE_ERROR', err.message);
                }
                if(data == null){
                    return responseWrapper.error('NULL_ERROR');
                }
                return _returnReco(responseWrapper, docUser.chieng_book_profile[0], data);
            });
        });
    });
}

module.exports = {
    getSpeakingReco: getSpeakingReco,
    getSpeakBook: getSpeakBook,
    updateSpeakBookProfile: updateSpeakBookProfile,
    updateBookTrace: updateBookTrace,
    addScores: addScores,
};
