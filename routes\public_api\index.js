const express = require('express');
const router = express.Router();
const Config = require('config');
const statistic_api = require('./statistic');
// const payments_api = require('../payments_api/v1');
const ResponseWrapper = require('../../modules/middlewares/response_wrapper');
const updateSchoolInfo = require('../../modules/timed_task/updateSchoolInfo');

function route(app) {
    app.use(function (req, res, next) {
        if (req.headers['if-none-match'])
            delete req.headers['if-none-match'];
        next();
    });
    // app keys filter
    router.use(function (req, res, next) {
        const responseWrapper = new ResponseWrapper(req, res);
        const appKeys = Config.get('app_keys');
        if (appKeys.indexOf(req.query['api_key']) >= 0) {
            next();
        } else {
            responseWrapper.error('AUTH_ERROR');
        }
    });
    // router.use('/statistic', statistic_api);
    // router.use('/payments', payments_api);
    router.use('/update_school_info', updateSchoolInfo.updateSchoolInfo);

    // Use Public APIs
    app.use('/tiku_api/v1',router);

}

module.exports = route;
