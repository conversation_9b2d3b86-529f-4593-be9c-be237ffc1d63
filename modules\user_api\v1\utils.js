
function getSemester(time){
    if (!time){
        time = new Date();
    }
    m = time.getMonth();
    return (m>1 && m<6) ? '下' : '上';
}

function normPeriod(grade){
    if (grade.search(/初|七|八|九/) >= 0){
        return '初中';
    } else if (grade.search(/高/) >= 0){
        return '高中';
    }
    return '小学';
}

function normJuniorGrade(grade){
    if (grade.search(/初|七|八|九/) >= 0){
        grade_m = {
            '初一': '七年级',
            '初二': '八年级',
            '初三': '九年级',
            '七年级': '七年级',
            '八年级': '八年级',
            '九年级': '九年级',
        };
        var gr = grade_m[grade];
        if (gr){
            return gr + getSemester();
        } else {
            return '七年级上';
        }
    }
}

function checkGrade(grade){
    var sn = /^(直升){0,1}(高|初)(一|二|三|四)(上|下){0,1}$/;
    var pri = /^(一|二|三|四|五|六)年级(上|下){0,1}$/;
    if (grade.search(sn) > -1){
        return true;
    } else if (grade.search(pri) > -1){
        return true;
    } else {
        return false;
    }
}

module.exports = {
    getSemester: getSemester,
    normPeriod: normPeriod,
    normJuniorGrade: normJuniorGrade,
    checkGrade: checkGrade,
}
