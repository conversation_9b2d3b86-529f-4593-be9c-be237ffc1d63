/**
 * 三方服务接入
 */


const algo = require('./algoClient');
const hfsTeacherV3 = require('./hfsTeacherV3');
const yuanpei = require('./yuanpeiClient');
const kb = require('./kbClient');
const yj = require('./yjClient');
const kbApp = require('./kbAppClient');
const sy = require('./syClient');
const wly = require('./wly');
const cas = require('./casClient');
const wechat = require('./wechat');
const hfs = require('./hfs');
const utilBox = require('./utilboxClient');
const rank = require('./rank');


module.exports = {
    algo,
    hfsTeacherV3,
    yuanpei,
    kb,
    yj,
    kbApp,
    sy,
    wly,
    cas,
    wechat,
    hfs,
    utilBox,
    rank
}
