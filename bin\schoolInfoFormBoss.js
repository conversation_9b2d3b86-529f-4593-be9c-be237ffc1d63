const axios = require('axios');
const URL = require('url');
const config = require('config');
const MongoClient = require('mongodb').MongoClient;
const KBSERV = config.get('KB_API_SERVER');

const limit = 30; // 每页条数
const schType = {
    'Tryout': '体验校',
    'Signed': '付费校',
    'Stopped': '终止合作校'
};

async function getSchoolFromKb(offset, limit) {
    let kbUrl = URL.format({
        protocol: KBSERV.protocol,
        hostname: 'dnr-kb-api-lan.yunxiao.com',
        pathname: '/kb_api/v2/schools/list',
        port: KBSERV.port,
        query: {
            api_key: KBSERV.appKey,
            limit: limit,
            offset: offset
        }
    });
    let kbSchoolList = await axios.get(kbUrl);
    if (typeof kbSchoolList.data.total_num !== 'number') {
        console.log('获取kb学校数据出错了');
    }
    return kbSchoolList;
}

async function getBossData(body) {
    let BOSSSERV = config.get('BOSS_SERVER');
    let bossUrl = URL.format({
        protocol: BOSSSERV.protocol,
        hostname: BOSSSERV.hostname,
        pathname: '/external/api/customer/get_status_by_school_ids',
        port: BOSSSERV.port,
    });
    let opstions = { headers: { 'apikey': BOSSSERV.apikey }, timeout: 50000 };
    let bossData = await axios.post(bossUrl, body, opstions);
    return bossData;
}

const run = async () => {
    const tikuclient = await MongoClient.connect(config.get('MONGODBS').tiku);
    const tikuDB = tikuclient.db('tiku');
    const schoolColl = tikuDB.collection('school_info');
    const memeberColl = tikuDB.collection('member');

    // 获取总条数，分页数
    let kbSchoolList = await getSchoolFromKb(0, 1);
    let allSchoolNum = kbSchoolList.data.total_num;
    let allPage = Math.ceil(allSchoolNum / limit);
    // 获取效益表
    let cond = { level_name: { $in: ['体验V1', '付费V1', '终止合作校V1'] } };
    let memberResult = await memeberColl.find(cond, { _id: 1, level_name: 1 }).toArray();
    let offset = 0;
    for (let page = 0; page < allPage; page++) {
        // 获取学校列表,来自kb
        let result = await getSchoolFromKb(offset, limit);
        offset = (page + 1) * limit;
        let ids = [];
        let schIdNoExists = [];
        if (result && Array.isArray(result.data.list)) {
            for (let sch of result.data.list) {
                let schoolData = await schoolColl.findOne({ _id: Number(sch.id) }, { fields: { is_edit_type: 1 } });
                // 还不存在的学校
                if (!schoolData) {
                    ids.push(sch.id);
                    schIdNoExists.push(sch.id);
                }
                // 题库没有修改过的学校类型
                if (schoolData && !schoolData.is_edit_type) {  // false
                    ids.push(sch.id);
                }
            }
        }
        console.log('ids', ids)
        console.log('schIdNoExists', schIdNoExists)
        if (ids.length === 0) {
            continue;
        }
        // 获取学校状态，来自boss
        let bossData = await getBossData({ schoolIds: ids });
        console.log(bossData.data) // boss没有查到数据，没有这些学校，则直接入库
        if (Array.isArray(bossData.data) && bossData.data.length === 0) {
            let insertSchools = result.data.list.map(function (sch) {
                return {
                    _id: sch.id,
                    name: sch.name,
                    type: '',
                    member_id: '',
                    is_edit_type: true,
                    valid: true,
                    ctime: new Date(),
                    utime: new Date(),
                }
            });
            await schoolColl.insertMany(insertSchools);
            continue;
        }

        if (bossData.data && bossData.data.code && bossData.data.code === 1 && Array.isArray(bossData.data.data)) {
            for (const sch of result.data.list) {
                let existBossSch = false;
                let data = {};
                for (const bossDa of bossData.data.data) {
                    if (sch.id === bossDa.schoolId) {
                        existBossSch = true;
                        let type = schType[bossDa.status] || '';
                        data._id = sch.id;
                        data.name = sch.name;
                        data.type = type;
                        data.is_edit_type = false;
                        data.valid = true;
                        data.ctime = new Date();
                        data.utime = new Date();
                        data.member_id = getMemberId(memberResult, type);
                        break;
                    }
                }
                if (existBossSch) {
                    if (schIdNoExists.includes(sch.id)) {
                        await schoolColl.insertOne(data);
                    } else {
                        let updata = { type: data.type, member_id: data.member_id, utime: data.utime };
                        await schoolColl.updateOne({ _id: data._id }, { $set: updata });
                    }
                } else { // boss不存在这个学校id, 直接插入后不需要在更新了
                    if (schIdNoExists.includes(sch.id)) {
                        let data = {
                            _id: sch.id,
                            name: sch.name,
                            type: '',
                            member_id: '',
                            is_edit_type: true,
                            valid: true,
                            ctime: new Date(),
                            utime: new Date(),
                        };
                        await schoolColl.insertOne(data);
                    }
                }
            }
        }
    }
    tikuDB.close();
};

function getMemberId(memberResult, type) {
    let result = '';
    let map = {
        '体验校': '体验V1',
        '付费校': '付费V1',
        '终止合作校': '终止合作校V1'
    }
    for (let member of memberResult) {
        if (map[type] === member.level_name) {
            result = member._id.toString();
            break;
        }
    }
    return result;
}

run().then(() => {
    console.log('程序运行完成，结束。');
}).catch(e => {
    console.log(e);
});