const config = require('config');
const _ = require('lodash');
let logger = require('./logger');
const axios = require('axios');
const utils = require('./utils');


module.exports = {
    sendMsg,
};

/**
 * 发送短信
 * @param {Array}
 */
async function sendMsg(content, phones, contentVar) {
    phones = filterShortMsgWhiteList(phones);
    if (_.isEmpty(phones)) return false;

    const appId = config.get('msgCentre.app.shortMsg.bdy');
    try {
        let result = await axios.post(`${config.get('msgCentre.host')}/v1/sms/phone`, {
            content, phone: phones, appId, contentVar,
        }, { headers: { 'apikey': config.get('msgCentre.apiKey') }, timeout: 50000 }).then(utils.handler);

        // if (result.code !== 0) {
        //     logger.error('发送端信通知失败：', JSON.stringify(result), contentVar, JSON.stringify(phones));
        //     return false;
        // }
        return result;
    } catch (err) {
        logger.error('发送短信通知失败：', err);
        return false;
    }
}

function filterShortMsgWhiteList(phones) {
    // 不是生产环境，测试开发
    const shortMsgWhiteList = config.get('shortMsgWhiteList');
    if (process.env.NODE_ENV !== 'production') {
        if (!shortMsgWhiteList) {
            return phones;
        }
        const inList = _.intersection(phones, shortMsgWhiteList);
        const outOfList = _.difference(phones, shortMsgWhiteList);
        if (!_.isEmpty(outOfList)) logger.error(`not in white list : ${outOfList.join(',')}`);
        return inList;
    }

    return phones;
}
