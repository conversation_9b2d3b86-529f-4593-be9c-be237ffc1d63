const URL = require('url');
const config = require('config');
const axios = require('axios');
const qs = require('querystring');
const server = config.get('wechat-teacher');
const logger = require('../utils/logger');
const rediser = require('../utils/rediser');
const _ = require('lodash');

module.exports = {
    get_access_token,
    get_jsapi_ticket
}

const cache_key_access_token = `tiku:wechat:teacher:${server.appid}:access_token`;
const cache_key_jsapi_ticket = `tiku:wechat:teacher:${server.appid}:jsapi:ticket`;

async function get_access_token() {
    let access_token = await rediser.redis.get(cache_key_access_token);
    if (access_token) return access_token;
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        pathname: '/cgi-bin/token',
        search: qs.stringify({
            grant_type: 'client_credential',
            appid: server.appid,
            secret: server.app_secret,
        })
    });
    const result = await axios.get(url);
    if (!result || result.status !== 200 || _.get(result, 'data.errcode', 0) !== 0) {
        logger.error(`微信获取access_token失败`);
        logger.error(result.data);
        return null;
    }
    access_token = result.data.access_token;
    if (access_token) {
        await rediser.redis.setex(cache_key_jsapi_ticket, 7000, access_token);
    }
    return access_token;
}

async function get_jsapi_ticket() {
    let ticket = await rediser.redis.get(cache_key_jsapi_ticket);
    if (ticket) return ticket;
    const access_token = await get_access_token();
    if (!access_token) return null;
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        pathname: '/cgi-bin/ticket/getticket',
        search: qs.stringify({
            type: 'jsapi',
            access_token: access_token,
        })
    });
    const result = await axios.get(url);
    if (!result || result.status !== 200 || result.data.errcode !== 0) {
        logger.error(`微信获取jsapi_ticket失败`);
        logger.error(result.data);
        return null;
    }
    ticket = result.data.ticket;
    if (ticket) {
        await rediser.redis.setex(cache_key_jsapi_ticket, 7000, ticket);
    }
    return ticket;
}
