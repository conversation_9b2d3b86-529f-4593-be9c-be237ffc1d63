const URL = require('url');
const _ = require('lodash');
const axios = require('axios');
const config = require('config');
const ResponseWrapper = require('../../middlewares/response_wrapper');

const getQuesInfo = async (question_ids, refer_category) => {
    let kb_server = config.get('KB_API_SERVER');
    let kbUrl = URL.format({
        protocol: kb_server.protocol,
        hostname: kb_server.hostname,
        pathname: `/kb_api/v2/questions`,
        port: kb_server.port,
    });
    let response = await axios.get(kbUrl, {params: {'question_ids': question_ids, api_key: kb_server.appKey} });

    let questions = []
    if (response.status === 200) {
        questions = response.data;
    }
    let quesInfo = []
    let quesDict = {}
    questions.forEach(element => {
        let new_ques = {};
        new_ques['id'] = element.id
        new_ques['description'] = element.description || ''
        new_ques['difficulty'] = element.difficulty || ''
        new_ques['type'] = element.type || ''
        new_ques['comment'] = element.comment || ''
        new_ques['period'] = element.period || ''
        new_ques['subject'] = element.subject || ''
        new_ques['year'] = element.year || ''
        new_ques['refer_times'] = element.refer_times || 0

        // 引用试卷
        let newRefer = [];
        let refer_exampapers = element.refer_exampapers || [];
        refer_exampapers.forEach(refer => {
            let paper_category = refer.category || ''
            if (refer_category.includes(paper_category) | refer_category == '') {
                if (refer.hasOwnProperty('from')){
                    delete refer['from']
                }
                refer['name'] = refer['vague_name']
                newRefer.push(refer)
            }
        });
        new_ques['refer_exampapers'] = newRefer

        // 试题结构
        let newBlocks = {};
        let blocks = element.blocks || [];
        newBlocks['types'] = blocks.types || []
        newBlocks['stems'] = blocks.stems || []
        newBlocks['knowledges'] = blocks.knowledges || []
        new_ques['blocks'] = newBlocks

        quesInfo.push(new_ques);
        quesDict[element.id] = new_ques
    });
    quesInfo = _.reverse(_.sortBy(quesInfo, function(it) {
        return it.type
    }))
    return {'quesInfo': quesInfo, 'quesDict': quesDict}
}

const getQuestionInfo = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    let question_ids = req.query.ids || '';
    let refer_category = req.query.refer_category || '';
    let quesDetail = await getQuesInfo(question_ids, refer_category)
    // 返回试题信息
    return responseWrapper.succ(quesDetail.quesInfo);
}

module.exports = {
    getQuesInfo: getQuesInfo,
    getQuestionInfo: getQuestionInfo,
}