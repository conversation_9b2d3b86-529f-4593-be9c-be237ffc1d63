/**
 * 考后巩固
 */

const express = require('express');
const router = express.Router();
const exam = require('../../modules/exam_api/v2/index');

// // 获取教师考试列表
router.get('/list', exam.getList);
// 获取考试班级列表
router.get('/:exam_id/paper/:paper_id/classes', exam.getExamPaperClasses);
// 获取试题详细
router.get('/:exam_id/paper/:paper_id/question/detail', exam.getExamPaperQuestionDetail);
// 获取相似题
router.get('/:exam_id/paper/:paper_id/question/:question_id/same', exam.getExamPaperQuestionSame);
// 相似题操作
router.put('/:exam_id/paper/:paper_id/question/:question_id/same', exam.putExamPaperQuestionSame);
// 试题搜索/换一题
router.post('/:exam_id/paper/:paper_id/question/search', exam.questionSearch);
// 试卷详细
router.get('/:exam_id/paper/:paper_id/exampaper/category/:category/info', exam.getExamPaperInfoByCategory);
// 试卷试题操作
router.put('/:exam_id/paper/:paper_id/exampaper/category/:category/question', exam.putExamPaperQuestionByCategory);
// 获取组卷试题ID
router.get('/:exam_id/paper/:paper_id/exampaper/question/ids', exam.getExamPaperQuestionIds);
// 教师讲义数据
router.get('/:exam_id/paper/:paper_id/handout', exam.getExamPaperTeacherHandout);
// 教师讲评
router.get('/:exam_id/paper/:paper_id/class/:class_id/comment', exam.getExamPaperTeacherComment);
// 获取学生答题情况
router.get('/:exam_id/paper/:paper_id/class/:class_id/student/:student_id/question/:question_id/brief', exam.getExamPaperStudentQuestionAnswer);
// 获取试题答题卡信息
router.get('/:exam_id/paper/:paper_id/question/:question_id/answer/pictures', exam.getExamPaperQuestionAnswerImage);
// 试题答题卡标记或取消标记
router.put('/:exam_id/paper/:paper_id/question/:question_id/answer/tag', exam.putExamPaperQuestionAnswerTag);
// 下载概览
router.get('/:exam_id/paper/:paper_id/download/info', exam.getExamPaperDownloadInfo);
// 文本转换
router.post('/text2ttf', exam.text2ttf);
// 获取考试资源下载信息
router.get('/:exam_id/paper/:paper_id/resource_download/info', exam.getResourceDownloadInfo);
// 获取试卷状态
router.get('/:exam_id/paper/:paper_id/status', exam.getExamPaperStatus);
// 获取成绩单
router.get('/:exam_id/paper/:paper_id/comparison', exam.getExamPaperComparison)

module.exports = router;
