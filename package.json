{"name": "tiku_serv", "version": "1.0.0", "description": "tiku server", "main": "app.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "nodemon app.js"}, "repository": {"type": "git", "url": "********************:dnr-platform/tiku_serv.git"}, "author": "<PERSON>uo<PERSON><PERSON>", "license": "ISC", "dependencies": {"@hapi/joi": "15.1.0", "@peculiar/x509": "^1.6.0", "ajv": "^6.5.2", "axios": "^0.18.0", "body-parser": "^1.17.2", "chai": "^4.1.2", "chinese-workday": "^1.10.0", "compression": "^1.7.3", "config": "^1.26.1", "cookie-parser": "^1.4.3", "cron": "^1.8.2", "decompress": "^4.2.0", "decompress-unzip": "^4.0.1", "enfscopy": "^1.0.1", "express": "^4.15.3", "express-rate-limit": "^3.5.3", "fontmin": "^1.1.0", "http-proxy-middleware": "^0.19.1", "ioredis": "^3.0.0", "jquery": "^3.3.1", "jsonwebtoken": "^7.4.3", "jsrsasign": "^10.5.1", "lodash": "^4.17.4", "log4js": "^1.1.1", "mathjax": "2.7.2", "mathjs": "^11.0.1", "mocha": "^4.0.1", "moment": "^2.30.1", "mongodb": "^2.2.27", "multer": "^1.3.0", "node-xlsx": "^0.12.1", "qs": "^6.5.0", "request": "^2.81.0", "rimraf": "^2.6.1", "sha-1": "^0.1.1", "superagent": "^3.6.3", "thenjs": "^2.0.5", "underscore": "^1.8.3", "url": "^0.11.0", "urllib": "^2.38.0", "uuid": "7.0.1", "websocket": "^1.0.24", "xml2js": "^0.4.19", "zip-dir": "^1.0.2", "zipkin-middleware": "git+ssh://********************:tiku/zipkin-middleware.git"}, "devDependencies": {"chai": "^4.0.2", "chalk": "^1.1.3", "commander": "^2.9.0", "nodemon": "^3.1.0", "superagent": "^3.5.2"}}