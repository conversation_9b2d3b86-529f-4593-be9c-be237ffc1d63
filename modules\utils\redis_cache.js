const axios = require('axios');
const config = require('config');
const URL = require('url');

const rediser = require('./rediser');
const utils = require('./utils');
const SCHOOL_DATA_JSON = require('./school_data.json');

exports.getBossAppUsageSchoolCache = async function getBossAppUsageSchoolCache(schoolId) {
    let str = await rediser.redis.get(`tiku:boss:appUsage:school:${schoolId}`);
    let bossData = JSON.parse(str);
    if (!bossData) {
        let BOSSSERV = config.get('BOSS_SERVER');
        let bossUrl = URL.format({
            protocol: BOSSSERV.protocol,
            hostname: BOSSSERV.hostname,
            pathname: '/external/api/customer/app_usage/get_by_school_id',
            port: BOSSSERV.port,
            query: {
                schoolId: schoolId,
                productCategory: 'tiku',
            }
        });
        let opstions = { headers: { 'apikey': BOSSSERV.apikey }, timeout: 50000 };
        bossData = await axios.get(bossUrl, opstions).then(utils.bossHandler);
        rediser.redis.setex(`tiku:boss:appUsage:school:${schoolId}`, 60 * 10, JSON.stringify(bossData));
    };

    return bossData;
};


exports.getBossSchoolInfoCache = async function getBossSchoolInfoCache(schoolId) {
    if (!schoolId) return null;
    let str = await rediser.redis.get(`tiku:boss:schoolinfo:${schoolId}`);
    if (str) {
        return JSON.parse(str);
    }
    let BOSSSERV = config.get('BOSS_SERVER');
    let bossUrl = URL.format({
        protocol: BOSSSERV.protocol,
        hostname: BOSSSERV.hostname,
        pathname: '/external/api/customer/get_by_school_ids',
        port: BOSSSERV.port,
        query: {
            schoolId: schoolId,
            productCategory: 'tiku',
        }
    });
    const schoolInfoMap = await axios.post(bossUrl, {
        schoolIds: [schoolId],
        fields: ['location', 'system', 'eduSystem',],
    }, {
        headers: {
            apikey: BOSSSERV.apikey,
        }
    }).then(utils.bossHandler);
    const bossData = schoolInfoMap[schoolId];
    rediser.redis.setex(`tiku:boss:schoolinfo:${schoolId}`, 60 * 60 * 24 * 7, JSON.stringify(bossData));

    return bossData;
};

exports.getYunXiaoIoSchoolMapCache = async function getYunXiaoIoSchoolMapCache() {
    let str = await rediser.redis.get(`tiku:yunxiao:schools`);
    let yunxiaoSchoolsMap = JSON.parse(str);
    if (!yunxiaoSchoolsMap) {
        yunxiaoSchoolsMap = {};
        let schoolData = null;
        if (!process.env.NODE_ENV || process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test') {
            schoolData = {
                data: SCHOOL_DATA_JSON
            };
        } else {
            schoolData = await axios.get(config.get('yunxiaoIoSchool'));
        }
        for (let newId of Object.keys(schoolData.data)) {
            yunxiaoSchoolsMap[schoolData.data[newId]] = newId;
        }
        rediser.redis.setex(`tiku:yunxiao:schools`, 60 * 60, JSON.stringify(yunxiaoSchoolsMap));
    }
    return yunxiaoSchoolsMap;
};

