/* 
 * Desc: video api
 * 
 * Author: g<PERSON><PERSON><PERSON>@iyunxiao.com
 */

'use strict';
const _ = require('underscore');
const qs = require('querystring');
const config = require('config');
const thenjs = require('thenjs');

const logger = require('../../utils/logger');
const ResponseWrapper = require('../../middlewares/response_wrapper');
const HttpWrapper = require('../../utils/http_wrapper');
const utils = require('./utils');

const TIMEOUT = 1000;
const RETRY_TIMES = 1;

function getExpCats(cats, path){
    if (!cats.children){
        if (cats.key == 'type' && cats.name == '实验天地'){
            path.push([cats.key, cats.name]);
            for (var z in path){
                cats[path[z][0]] = path[z][1];
            }
            path.pop();
            return [cats];
        } else {
            return [];
        }
    } else {
        var ecats = [];
        for (var i in cats.children){
            var cat = cats.children[i];
            path.push([cat.key, cat.name]);
            var secats = getExpCats(cat, path);
            for (var j in secats){
                ecats.push(secats[j]);
            }
            path.pop();
        }
        return ecats;
    }
}

function filterCats(cats, period, grade){
    var scats = [];
    if (period == '高中'){
        scats = _.filter(cats, x => x.grade == '高考专题');
    } else if (period == '初中'){
        var grade_sub_map = {
            '七年级上': {'生物': 1},
            '七年级下': {'生物': 1},
            '八年级上': {'物理': 1, '生物': 1},
            '八年级下': {'物理': 1, '生物': 1},
            '九年级上': {'物理': 1, '化学': 1},
            '九年级下': {'物理': 1, '化学': 1},
        };
        var _grade = utils.normJuniorGrade(grade);
        var subjects = grade_sub_map[_grade];
        scats = _.filter(cats, x => x.grade == _grade && x.subject in subjects);
    }
    return scats;
}

function getVideoCategory(period, callback){
    var server = config.get('KB_API_SERVER');
    var query = {
        period: period,
        api_key: server.appKey,
    };
    var _path = '/kb_api/v2/videos/categorys/?';
    _path += qs.stringify(query);

    var options = {
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        method: 'GET',
        path: _path,
        timeout: TIMEOUT,
        retryTimes: RETRY_TIMES,
    }
    var httpWrapper = new HttpWrapper();
    httpWrapper.request(options, function(err, ret){
        if (err){
            return callback(err);
        }
        if (ret.statusCode == 200){
            try {
                var cates = JSON.parse(ret.data);
                var expCats = getExpCats(cates.category, []);
                return callback(null, expCats);
            } catch(err) {
                logger.error(err);
                return callback(new Error('get exp video error'));
            }
        } else {
            return callback(new Error('get video category error'));
        }
    });
}

/*
 * Desc: 通过专辑id获取其所有视频资源
 */
function getCategory(catId, callback){
    var server = config.get('KB_API_SERVER');
    var query = {
        offset: 0, 
        limit: 50,
        api_key: server.appKey,
    };

    var _path = `/kb_api/v2/videos/categorys/${catId}/?`;
    _path += qs.stringify(query);

    var options = {
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        method: 'GET',
        path: _path,
        timeout: TIMEOUT,
        retryTimes: RETRY_TIMES,
    };

    var httpWrapper = new HttpWrapper();
    httpWrapper.request(options, function(err, ret){
        if (err){
            return callback(err);
        }
        if (ret.statusCode == 200){
            try {
                var cates = JSON.parse(ret.data);
                return callback(null, cates.videos);
            } catch(err) {
                logger.error(err);
                return callback(new Error('get videos error'));
            }
        } else {
            return callback(new Error('get videos error'));
        }
    });
}

/*
 * Desc: 通过id，一次取多个视频
 */
function getVideos(ids, callback){
    var server = config.get('KB_API_SERVER');
    var query = {
        ids: ids.join(','),
        api_key: server.appKey,
    };

    var _path = `/kb_api/v2/videos/?`;
    _path += qs.stringify(query);

    var options = {
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        method: 'GET',
        path: _path,
        timeout: TIMEOUT,
        retryTimes: RETRY_TIMES,
    };

    var httpWrapper = new HttpWrapper();
    httpWrapper.request(options, function(err, ret){
        if (err){
            return callback(err);
        }
        if (ret.statusCode == 200){
            try {
                var video = JSON.parse(ret.data);
                return callback(null, video);
            } catch(err) {
                logger.error(err);
                return callback(new Error('get video error'));
            }
        } else {
            return callback(new Error('get video error'));
        }
    });
}

function recoExpVideos(req, res){
    var resWrapper = new ResponseWrapper(req, res);
    try {
        var userId = req.user.id;
        var limit = parseInt(req.query.limit || 3);
        var grade = req.query.grade || req.user.grade;
    } catch(err) {
        return resWrapper.error('PARAMETERS_ERROR', err);
    }
    var period = utils.normPeriod(grade);

    thenjs(function(cont){
        getVideoCategory(period, function(err, cats){
            if (err){
                return cont(err);
            }
            if (!cats){
                return resWrapper.error('NULL_ERROR');
            }
            return cont(null, cats);
        });
    }).then(function(cont, cats){
        var scats = filterCats(cats, period, grade);
        if (scats.length <= 0){
            return resWrapper.error('NULL_ERROR');
        }
        var scat = _.sample(scats);
        getCategory(scat.id, function(err, videos){
            if (err){
                return cont(err);
            }
            return cont(null, videos);
        });
    }).then(function(cont, videos){
        // 取特定video详细信息
        var vids = _.map(_.sample(videos, limit), x => x.id);
        getVideos(vids, function(err, videos){
            if (err){
                return cont(err);
            }
            return resWrapper.succ(videos);
        });
    }).fail(function(cont, err){
        logger.error(err);
        return resWrapper.error('HANDLE_ERROR', err);
    });
}

module.exports = {
    recoExpVideos: recoExpVideos,
}
