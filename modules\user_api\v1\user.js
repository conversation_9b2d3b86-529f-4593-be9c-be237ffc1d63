/*
 * Desc: user api
 *
 */
'use strict';
const crypto = require('crypto');
const config = require('config');
const qs = require('querystring');
const _ = require('underscore');
const axios = require('axios');
const moment = require('moment');
const Joi = require('@hapi/joi');
const request = require('request');
const URL = require('url');
const ObjectId = require('mongodb').ObjectId;
const lodash = require('lodash');
const fs = require('fs');

const isMobileByUa = require('../../utils/utils').isMobileByUa;
const ResponseWrapper = require('../../middlewares/response_wrapper');
const mongodber = require('../../utils/mongodber');
const redisCache = require('../../utils/redis_cache');
const rediser = require('../../utils/rediser');
const mapBookGrade = require('./mapBookGrade.json');
const mapExampaperGrade = require('./mapExampaperGrade.json');
const logger = require('../../utils/logger');
const aes = require('../../utils/aes');
const jwt = require('../../utils/jwt');
const utils = require('../../utils/utils');
const enums = require('../../../bin/enum');
const shortMsg = require('../../utils/msg');
const subjectUtils = require('../../utils/subject_utils');
const { searchRegionByIp } = require('../../utils/ip2region');
const { getClientIp } = require('../../utils/utils');
const client = require('../../client');
const user_right_service = require('../v2/user_right_service');

const TIKUSERVER = config.get('TIKU_SERVER');
const SSOERV = config.get('SSO_SERVER');
const CASSERHOST = config.get('casServer');
const YJSERVER = config.get('YJ_API_SERVER');
const YJYZSERVER = config.get('YJ_YZ_API_SERVER');
const CAPTCHASERVERHOST = config.get('captchaServer');


const db = mongodber.use('tiku');
const db_open = mongodber.use('tiku_open');
const TIMEOUT = 1000;
const DEFAULT_EXPIRE_MONTH = 3;
const defaultPeriod = '高中';
const defaultSubject = '数学';
const virtualSchoolId = 101382;
const virtualSchoolName = '好分数题库虚拟学校';
const vCodeLength = 4;
const registContent = '您的短信验证码为:${vCode},请使用该验证码完成题库注册，该验证码15分钟内有效。';
const TIKUTEACHERHOMEPAGE = config.get('tikuTeacherHomePage');
const TIKUSTUDENTHOMEPAGE = config.get('tikuStudentHomePage');

const yjClient = require('../../../bin/yjClient');
const user_log_service = require('./user_log_service');
const user_vip_service = require('./user_vip_service');

// let teacher = null;
// let student = null;

// try {
//     teacher = fs.readFileSync('./dist/teacher.html').toString();
//     student = fs.readFileSync('./dist/student.html').toString();
// } catch (err) {
//     console.error(err);
// }


function _add_user_vip(user, userInfo) {
    user.is_vip = false;
    if (_.isEmpty(userInfo)) return;
    user.is_vip = moment().isBetween(userInfo.start_time, userInfo.expired_time);
    if (!user.is_vip) return;
    if (Object.values(enums.MemberType).includes(userInfo.vip_type)) {
        user.vip_type = userInfo.vip_type;
    } else {
        user.vip_type = enums.MemberType.TIKU_PERSONAL;
    }
    user.start_time = userInfo.start_time && userInfo.start_time.getTime();
    user.expired_time = userInfo.expired_time && userInfo.expired_time.getTime();
}

const basic_types = [
    enums.MemberType.YJ_PROFESSION,
    enums.MemberType.YJ_ULTIMATE
]

function _add_school_vip(user, school) {
    if (user.is_vip && user.is_vip === enums.MemberType.TIKU_PERSONAL) return;
    if (_.isEmpty(school)) return;
    const isVip = moment().isBetween(school.start_time, school.expired_time);
    if (!isVip) return;
    if (!basic_types.includes(school.vip_type)) { // 非基础版
        const teachers = _.get(school, 'teachers',  []);
        if (!teachers.includes(user.id)) {
            return;
        }
    }
    user.is_vip = true;
    user.vip_type = school.vip_type;
    user.start_time = school.start_time && school.start_time.getTime();
    user.expired_time = school.expired_time && school.expired_time.getTime();
}

/**
 * 用户权益信息
 */
const getAssets = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const user = req.user || {};
        const userId = user.userId;
        // const ucId = user.ucId;
        const userInfo = await db.collection('user').findOne({ _id: userId });
        if (_.isEmpty(userInfo)) {
            return responseWrapper.error('PARAMETERS_ERROR', '用户信息不存在');
        }
        const result = {
            user_id: user.id, // 用户id
            uc_id: user.ucId, // uc_id
            period: lodash.get(userInfo, 'curr.period', ''),
            subject: lodash.get(userInfo, 'curr.subject', ''),
            vip_info: [],

        }
        const vip_info = await user_right_service.get_vips(userId);
        if (lodash.size(vip_info)) {
            result.vip_info = vip_info;
        }
        return responseWrapper.succ(result);
        // let schoolInfo;
        // if (user.schoolId) schoolInfo = await db.collection('school_info').findOne({ _id: user.schoolId });
        //
        // let resObj = {
        //     user_id: userId,
        //     uc_id: ucId,
        //     is_vip: userInfo && userInfo.is_vip || false,
        //     available_count: 0,
        //     used_count: 0,
        //     expire_count: 0,
        // };
        // if (resObj)
        // if (!userInfo) {
        //     return responseWrapper.succ(resObj);
        // }
        // let useNum = {
        //     que_download_num: 0,
        //     exampaper_download_num: 0,
        //     assemble_download_num: 0,
        //     que_details_num: 0,
        // };
        // if (userInfo.use_time && userInfo.use_time > moment().startOf('month').toDate()) {
        //     useNum.que_download_num = userInfo.que_download_num || 0;
        //     useNum.exampaper_download_num = userInfo.exampaper_download_num || 0;
        //     useNum.assemble_download_num = userInfo.assemble_download_num || 0;
        //     useNum.que_details_num = userInfo.que_details_num || 0;
        // } else {
        //     await db.collection('user').updateOne({ _id: userId }, { $set: useNum });
        // }
        // let edu_file_download_num = 0;
        // if (userInfo.use_time && !moment().isSame(userInfo.use_time, 'day')) {
        //     await db.collection('user').updateOne({ _id: userId }, { $set: {edu_file_download_num: 0} });
        // } else {
        //     edu_file_download_num = userInfo.edu_file_download_num || 0;
        // }
        // if (userInfo.is_vip) {
        //     resObj.edu_file_download_num = edu_file_download_num;
        //     resObj.edu_file_download_total_num = 5;
        // } else {
        //     resObj.edu_file_download_num = 0;
        //     resObj.edu_file_download_total_num = 0;
        // }
        // _add_user_vip(resObj, userInfo);
        // if (resObj.is_vip) {
        //     if (resObj.vip_type === enums.MemberType.TIKU_PERSONAL) {
        //         const itemInfos = await db.collection('@TicketItem').find({ user_id: userId }).toArray();
        //         resObj.available_count = (itemInfos || []).filter(item => item.status === enums.TicketItemStatus.AVAILABLE).length;
        //         resObj.used_count = (itemInfos || []).filter(item => item.status === enums.TicketItemStatus.USED).length;
        //         resObj.expire_count = (itemInfos || []).filter(item => item.status === enums.TicketItemStatus.EXPIRE).length;
        //
        //         const coupons = await db.collection('user_coupons').find({ user_id: userId }).toArray();
        //         const now = new Date().getTime();
        //         resObj.coupons = {
        //             normal_count: (coupons || []).filter(it => it.usage_status === 0 && it.valid_to.getTime() > now).length,
        //             used_count: (coupons || []).filter(it => it.usage_status === 1).length,
        //             out_count: (coupons || []).filter(it => it.usage_status === 0 && it.valid_to.getTime() < now).length
        //         };
        //     } else {
        //         resObj.user_num = {
        //             que_download_num: useNum.que_download_num,
        //             exampaper_download_num: useNum.exampaper_download_num,
        //             assemble_download_num: useNum.assemble_download_num,
        //             que_details_num: useNum.que_details_num,
        //         };
        //         resObj.total_num = {
        //             que_download_num: (schoolInfo && schoolInfo.que_download_num || 0) > useNum.que_download_num ? schoolInfo.que_download_num : useNum.que_download_num,
        //             exampaper_download_num: (schoolInfo && schoolInfo.exampaper_download_num || 0) > useNum.exampaper_download_num ? schoolInfo.exampaper_download_num : useNum.exampaper_download_num,
        //             assemble_download_num: (schoolInfo && schoolInfo.assemble_download_num || 0) > useNum.assemble_download_num ? schoolInfo.assemble_download_num : useNum.assemble_download_num,
        //             que_details_num: schoolInfo && schoolInfo.que_details_num || 0,
        //         };
        //     }
        // }
        // // _add_school_vip(resObj, schoolInfo);
        // return responseWrapper.succ(resObj);
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

/**
 * @desc: 提取用户教材配置信息
 * @source: http://wiki.iyunxiao.com/pages/viewpage.action?pageId=26118717#id-用户API-提取用户教材配置信息
 * @author: cuiyunfeng <<EMAIL>>
 * @method: GET
 * @api: /user_api/v1/books/profile
 * @input: cookie
 * @response  {Object}
 * @public
 */
function getUserBooksProfile(req, res) {

    var responseWrapper = new ResponseWrapper(req, res);

    /**
     * Get userId from SSID.
     */
    var KB_API_SERVER = config.get('KB_API_SERVER');
    var period = req.query.period;
    var queryGrade = req.query.grade;

    if (!period) {
        return responseWrapper.error('PARAMETERS_ERROR', 'need period query!');
    }

    if (!req.user) {
        return responseWrapper.error('HANDLE_ERROR');
    }

    var userId = req.user.id,
        apiKey = req.apiKey;

    if (userId === null) {
        return responseWrapper.error('HANDLE_ERROR', 'Get userId fail');
    }

    db.collection('user').find({
        '_id': userId
    }).toArray(function (err, docs) {

        if (err || !(docs instanceof Array))
            return responseWrapper.error('HANDLE_ERROR');

        var retobj = [];
        if (docs[0] && docs[0].book_profile)
            retobj = docs[0].book_profile;

        // 过滤掉英语语文配置教材的记录
        retobj = _.filter(retobj, function (subject) {
            return !(['语文', '英语'].indexOf(subject.name) >= 0 &&
                subject.type != 'knowledge_tree');
        });

        /**
         * period may not normal.
         */
        var url = URL.format({
            protocol: 'http',
            hostname: KB_API_SERVER.hostname,
            port: KB_API_SERVER.port,
            pathname: '/kb_api/v2/books/',
            search: qs.stringify({
                api_key: apiKey,
                period: period
            }),
        });

        request({
            url: url,
            timeout: TIMEOUT
        }, function (err, response, body) {

            try {
                body = JSON.parse(body);
                var data = body;
                if (response.statusCode === 200) {
                    var books = data.book;
                    var pdObj = books.children[0];

                    for (var sx in pdObj.children) {
                        var subject = pdObj.children[sx];
                        if (_.pluck(retobj, 'subject').indexOf(subject.name) < 0
                            && ['语文', '英语'].indexOf(subject.name) < 0) {
                            for (var pv in subject.children) {
                                var pvObj = subject.children[pv];
                                var grade = _.find(pvObj.children, function (x) {
                                    if (!mapBookGrade[period][subject.name])
                                        return undefined;
                                    return x.name == mapBookGrade[period][subject.name][queryGrade];
                                });
                                if (grade) {
                                    var obj = {
                                        type: 'book',
                                        period: period,
                                        subject: subject.name,
                                    };
                                    obj.press_version = pvObj.name;
                                    obj.grade = grade.name;
                                    obj.id = grade.id;
                                    retobj.push(obj);
                                    break;
                                }
                            }
                        }
                    }
                } else {
                    return responseWrapper.error('HANDLE_ERROR', data.msg);
                }
            } catch (err) {
                return responseWrapper.error('HANDLE_ERROR', err.message);
            }

            var url = URL.format({
                protocol: 'http',
                hostname: KB_API_SERVER.hostname,
                port: KB_API_SERVER.port,
                pathname: '/kb_api/v2/knowledge_trees/',
                search: qs.stringify({
                    api_key: apiKey,
                    period: period
                }),
            });

            request({
                url: url,
                timeout: TIMEOUT
            }, function (err, response, body) {

                try {
                    body = JSON.parse(body);
                    var data = body;

                    if (response.statusCode === 200) {
                        try {
                            /**
                             * HTTP is unreliable, maybe throw an exception.
                             */
                            var knowledge_trees = data.knowledge_tree;
                            var pdObj = knowledge_trees.children[0];

                            for (var sx in pdObj.children) {
                                var subject = pdObj.children[sx];
                                if (_.pluck(retobj, 'subject').indexOf(subject.name) < 0 &&
                                    ['语文', '英语'].indexOf(subject.name) >= 0) {
                                    var obj = {
                                        type: 'knowledge_tree',
                                        period: period,
                                        subject: subject.name,
                                        name: subject.children[0].name,
                                        id: subject.children[0].id
                                    };
                                    retobj.push(obj);
                                }
                            }
                        } catch (err) {
                            return responseWrapper.error('HANDLE_ERROR', [err.message, err.stack].join(': '));
                        }
                    } else {
                        return responseWrapper.error('PARAMETERS_ERROR', data.msg);
                    }
                } catch (err) {
                    return responseWrapper.error('HANDLE_ERROR', [err.message, err.stack].join(': '));
                }

                return responseWrapper.succ(retobj);
            });
        });

    });
}

/**
 * @desc: 提交或者修改教材配置信息
 * @source: http://wiki.iyunxiao.com/pages/viewpage.action?pageId=26118717#id-用户API-提交或者修改教材配置信息
 * @author: cuiyunfeng <<EMAIL>>
 * @method: PUT
 * @api: /user_api/v1/books/profile
 * @input {Object}  cookie
 * @input {Object}    body
 * @response  {Object}
 * @public
 */
function updateUserBooksProfile(req, res) {

    var responseWrapper = new ResponseWrapper(req, res);
    var body = req.body;
    if (!req.user)
        return responseWrapper.error('HANDLE_ERROR');

    var userId = req.user.id;
    var type = req.body.type + 's';
    if (!type) {
        return responseWrapper.error('PARAMETERS_ERROR');
    }

    var KB_API_SERVER = config.get('KB_API_SERVER');
    var url = URL.format({
        protocol: KB_API_SERVER.protocol,
        hostname: KB_API_SERVER.hostname,
        port: KB_API_SERVER.port,
        pathname: '/kb_api/v2/' + type + '/' + req.body.id,
        search: qs.stringify({
            api_key: KB_API_SERVER.appKey
        })
    });

    request({
        url: url,
        timeout: TIMEOUT
    }, function (err, response, _body) {
        var responseBody = null;
        try {
            responseBody = JSON.parse(_body);
            if (response.statusCode !== 200) {
                if (responseBody.code == 5) {
                    return responseWrapper.error('NULL_ERROR', '您设置的资源不在我们资源库当中');
                }
                var error = new Error('网络异常，获取资源失败！');
                error.code = 'HANDLE_ERROR';
                throw error;
            }
            if ((req.body.type === 'book' && responseBody.grade !== req.body.grade) ||
                (req.body.type === 'knowledge_tree' && responseBody.name !== req.body.name)) {
                var error = new Error('配置资源名称和ID不匹配！');
                error.code = 'PARAMETERS_ERROR';
                throw error;
            }
        } catch (err) {
            if (err.code) {
                return responseWrapper.error(err.code, err.message);
            }
            return responseWrapper.error('HANDLE_ERROR');
        }

        db.collection('user').findOne({ '_id': userId }, function (err, doc) {
            if (err) {
                return responseWrapper.error('HANDLE_ERROR');
            }

            if (!doc) {
                db.collection('user').
                    insert({
                        _id: userId,
                        book_profile: [body],
                        ctime: new Date(),
                        utime: new Date(),
                        exampaper_profile: []
                    });
                return responseWrapper.succ([]);
            }

            var bookProfile = doc.book_profile;
            if (!bookProfile)
                bookProfile = [];

            var destBP = _.find(bookProfile, function (x) {
                return x.subject == body.subject;
            });

            if (destBP === undefined) {
                bookProfile.push(body);
            } else {
                for (let key in destBP) {
                    delete destBP[key];
                }
                for (let key in body) {
                    destBP[key] = body[key];
                }
            }

            db.collection('user').updateOne({
                '_id': userId
            }, {
                $set: { book_profile: bookProfile }
            }, function (err, writeResult) {
                if (err) {
                    return responseWrapper.error('HANDLE_ERROR');
                }
                return responseWrapper.succ([]);
            })
        });
    });
}

/**
 * @desc: 获取用户试卷配置信息
 * @source: http://wiki.iyunxiao.com/pages/viewpage.action?pageId=26118717#id-用户API-获取用户试卷配置信息
 * @author: cuiyunfeng <<EMAIL>>
 * @method: GET
 * @api: /user_api/v1/exampapers/profile
 * @input: cookie
 * @response  {Object}
 * @public
 */
function getUserExampapersProfile(req, res) {

    var responseWrapper = new ResponseWrapper(req, res);

    /**
     * cookieParser middle and config module is ensure.
     */
    var KB_API_SERVER = config.get('KB_API_SERVER');

    var period = req.query.period,
        queryGrade = req.query.grade;

    /**
     * Need period & queryGrade.
     */
    if (period === undefined || queryGrade === undefined) {
        return responseWrapper.error('PARAMETERS_ERROR', 'Need period and grade!');
    }

    if (!req.user)
        return responseWrapper.error('HANDLE_ERROR');

    var userId = req.user.id,
        apiKey = req.apiKey;

    if (userId === null) {
        return responseWrapper.error('HANDLE_ERROR', 'Get userId fail');
    }

    db.collection('user')
        .find({ '_id': userId }).
        toArray(function (err, docs) {

            if (err || !(docs instanceof Array))
                return responseWrapper.error('HANDLE_ERROR');

            /**
             * Is return when not query.
             */
            var retobj = [];
            if (docs[0] && docs[0].exampaper_profile)
                retobj = docs[0].exampaper_profile;

            var url = URL.format({
                protocol: 'http',
                hostname: KB_API_SERVER.hostname,
                port: KB_API_SERVER.port,
                pathname: '/kb_api/v2/exampapers/categorys/',
                search: qs.stringify({
                    api_key: apiKey,
                    period: period
                }),
            });

            request({
                url: url,
                timeout: TIMEOUT
            }, function (err, response, body) {

                try {
                    body = JSON.parse(body);
                    var data = body;
                    if (response.statusCode === 200) {
                        var categorys = data.category;
                        var pdObj = categorys.children[0];
                        for (var sx in pdObj.children) {
                            var subject = pdObj.children[sx];
                            if (_.pluck(retobj, 'subject').indexOf(subject.name) < 0) {
                                for (var pv in subject.children) {
                                    var pvObj = subject.children[pv];

                                    var grade = _.find(pvObj.children, function (x) {
                                        return x.name == mapExampaperGrade[period][queryGrade];
                                    });

                                    if (grade) {
                                        var obj = {
                                            period: period,
                                            subject: subject.name,
                                        };

                                        obj.press_version = pvObj.name;
                                        obj.grade = grade.name;
                                        obj.category_id = grade.id;
                                        retobj.push(obj);
                                        break;
                                    }
                                }
                            }
                        }
                    } else {
                        return responseWrapper.error('HANDLE_ERROR', data.msg);
                    }
                } catch (err) {
                    return responseWrapper.error('HANDLE_ERROR', err.message);
                }

                return responseWrapper.succ(retobj);
            });
        });
}

/**
 * @desc: 提交或者修改试卷信息
 * @source: http://wiki.iyunxiao.com/pages/viewpage.action?pageId=26118717#id-用户API-提交或者修改试卷信息
 * @author: cuiyunfeng <<EMAIL>>
 * @method: PUT
 * @api: /user_api/v1/exampapers/profile
 * @input {Object}  cookie
 * @input {Object}    body
 * @response  {Object}
 * @public
 */
function updateUserExampapersProfile(req, res) {

    var responseWrapper = new ResponseWrapper(req, res);
    /**
     * Get userId from SSID.
     */

    var body = req.body;

    if (!req.user)
        return responseWrapper.error('HANDLE_ERROR');

    var userId = req.user.id;

    db.collection('user').find({ '_id': userId }).toArray(function (err, docs) {

        if (err || !(docs instanceof Array))
            return responseWrapper.error('HANDLE_ERROR');

        if (docs.length <= 0) {

            db.collection('user').
                insert({
                    _id: userId,
                    book_profile: [],
                    exampaper_profile: [body]
                });
            return responseWrapper.succ([]);
        }

        var exampaperProfile = docs[0].exampaper_profile;
        if (!exampaperProfile)
            exampaperProfile = [];

        var destEP = _.find(exampaperProfile, function (x) {
            return x.subject == body.subject;
        });

        if (destEP === undefined) {
            exampaperProfile.push(body);
        } else {
            for (let key in destEP) {
                delete destEP[key];
            }

            for (let key in body) {
                destEP[key] = body[key];
            }
        }

        db.collection('user')
            .update({ '_id': userId },
                { $set: { 'exampaper_profile': exampaperProfile, utime: new Date() } },
                function (err) {

                    return responseWrapper.succ([]);
                });
    });
}

const getUserInfo = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const user = req.user || {};
        const userId = user.userId;
        if (!userId) return responseWrapper.error('HANDLE_ERROR', 'getUserInfo req.user fail');

        const userInfo = await db.collection('user').findOne({ _id: userId });
        let resObj = {
            id: userId,
            name: userInfo.name,
            role: userInfo.role,

            period: userInfo.period,
            subjects: userInfo.subjects,

            province: userInfo.province,
            city: userInfo.city,

            sch_id: userInfo.sch_id,
            sch_name: userInfo.sch_name,
        };

        return responseWrapper.succ(resObj);
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

const putUserInfoSchema = Joi.object({
    period: Joi.string().optional(),
    subjects: Joi.array().items(Joi.object({
        subject: Joi.string().optional(),
        book_id: Joi.number().optional(),
        press_version: Joi.string().optional(),
        teaching_materials: Joi.string().optional(),
    })).optional(),
    province: Joi.string().optional(),
    city: Joi.string().optional(),
});

const putUserInfo = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let { period, subjects, province, city } = await Joi.validate(req.body, putUserInfoSchema);

        const userId = req.user.userId;
        const userInfo = await db.collection('user').findOne({ _id: userId });

        const now = new Date();
        let insertData = {
            utime: now,
        };

        if (period && userInfo.period !== period) insertData.period = period;
        if (subjects && userInfo.subjects !== subjects) insertData.subjects = subjects;
        if (province && userInfo.province !== province) insertData.province = province;
        if (city && userInfo.city !== city) insertData.city = city;

        if (Object.keys(insertData).length > 1) {
            await db.collection('user').updateOne({
                _id: userId,
            }, {
                $set: insertData,
            });
        }
        return responseWrapper.succ({});
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

function addTemporaryKnowItem(req, res) {

    var responseWrapper = new ResponseWrapper(req, res);
    var body = req.body;
    function MD5(body) {
        if (body instanceof Object)
            body = JSON.stringify(body);

        var md5 = crypto.createHash('md5');
        md5.update(body, 'utf8', 'hex');
        var d = md5.digest('hex');
        return d;
    }

    var md5 = MD5(body);

    return rediser.set('kb:hfs:' + md5,
        JSON.stringify(body),
        60,
        function (err) {
            if (err) {
                // TODO
            }
            return responseWrapper.succ({
                jade: md5,
                expires: new Date(Date.now() + 60 * 1000)
            })
        });
}

function getTemporaryKnowItem(req, res) {
    var md5 = req.params.jade;
    var responseWrapper = new ResponseWrapper(req, res);
    return rediser.get('kb:hfs:' + md5, function (err, value) {
        return responseWrapper.succ(JSON.parse(value));
    });
}

function updateUserInfoTrace(req, res) {
    var responseWrapper = new ResponseWrapper(req, res);
    var user = req.user;
    var body = req.body;
    if (!body || typeof body != 'object') {
        return responseWrapper.error('PARAMETERS_ERROR');
    }
    _.pick.apply(body, ['period', 'subject', 'press_version', 'grade']);
    var cond = {
        _id: user.id
    };
    delete body.finished;
    var proj = {
        $set: { trace: body, finished: 1 }
    };
    db.collection('user').update(cond, proj, {
        upsert: true
    }, function (err, writeResult) {
        if (err) {
            return responseWrapper.error('HANDLE_ERROR');
        }

        if (writeResult.result.ok != 1) {
            return responseWrapper.error('PARAMETERS_ERROR');
        }

        return responseWrapper.succ({});
    });
}

function getUserInfoTrace(req, res) {
    var responseWrapper = new ResponseWrapper(req, res);
    var user = req.user;
    var cond = {
        _id: user.id
    };

    var proj = {
        trace: 1
    };

    db.collection('user').findOne(cond, proj, function (err, doc) {
        if (err) {
            return responseWrapper('HANDLE_ERROR');
        }
        var retval = null;
        if (doc && doc.trace) {
            retval = doc.trace;
        } else {
            retval = {};
            if (isMobileByUa(req.headers['user-agent'])) {
                retval = {
                    period: '高中',
                    subject: '数学',
                    press_version: '人教A版',
                    grade: '必修1',
                };
            }
        }
        return responseWrapper.succ(retval);
    })
}

const getUserInfoProfile = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let user = req.user;
        let cond = {
            _id: user.id
        };

        let proj = { trace: 1, finished: 1, hfs: 1, curr: 1, sch_id: 1, phone: 1 };
        let doc = await db.collection('user').findOne(cond, proj);
        if (!doc) {
            doc = { finished: 0, hfs: 0 };
        }
        if (doc.finished === undefined) {
            doc.finished = 0;
        }
        if (!doc.trace) {
            doc.trace = {};
        }
        if (!doc.hfs) {
            doc.hfs = 0;
        }
        let currPeriod = '高中';
        let currSubject = '数学';
        let currGrade = '';
        if (doc.curr) {
            currPeriod = lodash.get(doc.curr, 'period', currPeriod);
            currSubject = lodash.get(doc.curr, 'subject', currSubject);
            currGrade = lodash.get(doc.curr, 'grade', currGrade);
        }
        let retval = Object.assign(doc.trace, { finished: doc.finished, hfs: doc.hfs, curr_period: currPeriod, curr_subject: currSubject, curr_grade: currGrade });
        if (doc.sch_id) {
            // 查询虚拟学校可以分享组卷
            let result = await db.collection('school_info').findOne({ _id: doc.sch_id, valid: true });
            // if (result && result.type === '虚拟校') {
            //     retval.can_share = true;
            // } else {
            //     retval.can_share = false;
            // }
            // 暂时关闭虚拟学校逻辑 2023/05/26
            retval.can_share = false;
            // 用户所在学校的会员权益
            if (result && result.member_id) {
                retval.member = await db.collection('member').findOne({ _id: ObjectId(result.member_id) });
            }
        }

        retval.province = doc.province || '';
        retval.city = doc.city || '';
        retval.vip = doc.is_vip;
        retval.phone = doc.phone ? utils.maskPhoneNumber(doc.phone) : '';
        const yjUser = await client.yj.getUserInfo(req.user.id);
        retval.yjUser = yjUser;
        return responseWrapper.succ(retval);
    } catch (err) {
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

const logOut = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);

    try {
        // let sessionId = TIKUSERVER.sessionIdCookie.name;
        // let sessionIdValue = req.cookies[sessionId];
        // let sessionOption = {};
        // sessionOption.expires = new Date();
        // sessionOption.domain = TIKUSERVER.sessionIdCookie.options.domain;
        // sessionOption.path = TIKUSERVER.sessionIdCookie.options.path;
        // res.cookie(sessionId, sessionIdValue, sessionOption);

        // let userInfo = TIKUSERVER.userInfo.name;
        // let userInfoValue = req.cookies[userInfo];
        // let userOption = {};
        // userOption.expires = new Date();
        // userOption.domain = TIKUSERVER.userInfo.options.domain;
        // userOption.path = TIKUSERVER.userInfo.options.path;
        // res.cookie(userInfo, userInfoValue, userOption);

        // for (let tiku in TIKUSERVER) {
        //     let obj = TIKUSERVER[tiku];
        //     if (typeof obj === 'object'
        //     && obj.name
        //     && obj.options
        //     && (typeof obj.options === 'object')) {
        //         let sessionName = obj.name;
        //         let sessionVal = req.cookies[sessionName];
        //         let sessionOpt = {};
        //         sessionOpt.expires = new Date();
        //         sessionOpt.domain = obj.options.domain;
        //         sessionOpt.path = obj.options.path;
        //         res.cookie(sessionName, sessionVal, sessionOpt);
        //     }
        // }

        return res.status(200).send({});
    } catch (error) {
        return responseWrapper.error('HANDLE_ERROR')
    }
};

const updateHfsInfo = async (req, res) => {
    let resWrap = new ResponseWrapper(req, res);
    try {
        await db.collection('user').update({ _id: req.user.id }, { $set: { hfs: 0 } });
        return resWrap.succ({});
    } catch (err) {
        logger.error(err);
        return resWrap.error('HANDLE_ERROR');
    }
};

const updateUserCurrInfo = async (req, res) => {
    let resWrap = new ResponseWrapper(req, res);
    try {
        let periodRange = ['高中', '初中', '小学'];
        let subjectRange = ['数学', '语文', '英语', '物理', '化学', '生物', '政治', '历史', '地理', '科学'];
        let userPeriod = req.query.period ? req.query.period : '高中';
        userPeriod = periodRange.indexOf(userPeriod) !== -1 ? userPeriod : '高中';
        let userSubject = req.query.subject ? req.query.subject : '数学';
        userSubject = subjectRange.indexOf(userSubject) !== -1 ? userSubject : '数学';
        await db.collection('user').update({ _id: req.user.id }, { $set: { 'curr.period': userPeriod, 'curr.subject': userSubject } });
        return resWrap.succ({});
    } catch (err) {
        logger.error(err);
        return resWrap.error('HANDLE_ERROR');
    }
};

// 重置cookie
const resetCookie = async (req, res, userId) => {
    let _user = req.user;
    _user.id = userId;
    _user.userId = userId
    _user.need_bind = false;

    let value = aes.encript(jwt.encode(_user));
    let cookie = TIKUSERVER.sessionIdCookie;
    res.cookie(cookie.name, value, cookie.options);
    res.cookie(TIKUSERVER.userInfo.name, JSON.stringify(_user), cookie.options);
};

const setQiXinUnbind = async (req, res) => {
    let resWrap = new ResponseWrapper(req, res);
    let { qixin_id } = req.body;
    if (!qixin_id) {
        return resWrap.error('PARAMETERS_ERROR', '缺少参数');
    }
    const id = parseInt('111' + new Date().getTime());
    let data = {
        _id: id,
        curr: {},
        trace: {},
        finished: 0,
        qixin_id: qixin_id,
        utime: new Date(),
        ctime: new Date(),
        name: req.user.name,
        sch_id: req.user.schoolId,
        sch_name: req.user.schoolName
    };
    try {
        await db.collection('user').insertOne(data);
        await user_log_service.register(data);
        resetCookie(req, res, id);
        return resWrap.succ({});
    } catch (err) {
        logger.error(err);
        return resWrap.error('HANDLE_ERROR');
    }
};

const setQiXinBinded = async (req, res) => {
    let resWrap = new ResponseWrapper(req, res);
    let { qixin_id, account, passwd } = req.body;
    if (!qixin_id || !account || !passwd) {
        return resWrap.error('PARAMETERS_ERROR', '缺少参数');
    }
    let SSO_SERVER = config.get('SSO_SERVER')
    let url = URL.format({
        protocol: SSO_SERVER.protocol,
        hostname: SSO_SERVER.hostname,
        port: SSO_SERVER.port,
        pathname: '/passport/v1/user/login_info',
        search: qs.stringify({
            api_key: SSO_SERVER.appKey,
        })
    });
    let data = {
        "app": "TIKU",
        "cert_app": "HFS_TEACHER",
        "user": account,
        "passwd": passwd
    }
    try {
        let userInfo = await axios.post(url, data);
        if (userInfo.data && userInfo.data.code === 0 && userInfo.data.data && userInfo.data.data.id) {
            let userId = userInfo.data.data.id;
            let result = await db.collection('user').findOne({ _id: userId });
            if (result) {
                await db.collection('user').updateOne({ _id: userId }, {
                    $set: {
                        utime: new Date(),
                        qixin_id: qixin_id,
                        name: userInfo.data.data.name,
                        sch_id: userInfo.data.data.schoolId,
                        sch_name: userInfo.data.data.schoolName
                    }
                });
            } else {
                const data = {
                    _id: userId,
                    curr: {},
                    trace: {},
                    finished: 0,
                    utime: new Date(),
                    ctime: new Date(),
                    qixin_id: qixin_id,
                    name: userInfo.data.data.name,
                    sch_id: userInfo.data.data.schoolId,
                    sch_name: userInfo.data.data.schoolName
                };
                await db.collection('user').insertOne(data);
                await user_log_service.register(data);
            }

            resetCookie(req, res, userId);
            return resWrap.succ({});
        }
        resWrap.error('HANDLE_ERROR', '获取用户信息出错');
    } catch (err) {
        logger.error(err);
        return resWrap.error('HANDLE_ERROR', err.message);
    }
};

const postUserLoginSchema = Joi.object({
    app: Joi.string().required(),
    cert_app: Joi.string().required(),
    passwd: Joi.string().required(),
    user: Joi.string().required(),
});

const login = async (req, res) => {
    let resWrap = new ResponseWrapper(req, res);
    try {
        let { app, cert_app, user, passwd } = await Joi.validate(req.body, postUserLoginSchema);
        // 检查账户是否存在
        let passportUrl = URL.format({
            protocol: SSOERV.protocol,
            hostname: SSOERV.hostname,
            pathname: '/passport/v1/user/login_info/',
            port: SSOERV.port,
        });
        let passportOpstions = { headers: { 'Content-Type': 'application/json', }, timeout: 50000 };
        let ssoData = await axios.post(passportUrl, { app, cert_app, user, passwd }, passportOpstions);
        let casDate = {};
        if (!ssoData.data || ssoData.data.code !== 0) {
            // 学生账号只走kb-apps不走passport
            if (cert_app !== 'HFS_TEACHER') return resWrap.error('PARAMETERS_ERROR', '账号密码错误');
            casDate = await axios.post(`${CASSERHOST}/validateUser`, { loginName: user, password: passwd }).then(utils.casHandler);
        }
        ssoData = ssoData.data && ssoData.data.data || {};
        let userId = Number(ssoData.id || casDate.userId);
        let userInfo = await db.collection('user').findOne({ _id: userId });

        let schoolData = await redisCache.getYunXiaoIoSchoolMapCache();
        let schoolId = ssoData.schoolId && Number(schoolData[ssoData.schoolId] || ssoData.schoolId) || (userInfo && Number(userInfo.sch_id));

        let schoolInfo, schoolAppUsage, bossSchoolInfo, location, yjTeacherInfo = {};
        if (schoolId) {
            schoolInfo = await db.collection('school_info').findOne({ _id: schoolId });
            // if (schoolInfo) {
            schoolAppUsage = await redisCache.getBossAppUsageSchoolCache(schoolId);
            bossSchoolInfo = await redisCache.getBossSchoolInfoCache(schoolId);
            location = (bossSchoolInfo && bossSchoolInfo.location || '').split('/');
            let yjUrl = URL.format({
                protocol: YJSERVER.protocol,
                hostname: YJSERVER.hostname,
                pathname: '/v1/teacherInfo/Tiku',
                port: YJSERVER.port,
            });
            let opstions = { headers: {}, timeout: 50000 };
            let yjData = await axios.get(yjUrl, { params: { schoolId: schoolId } }, opstions);
            if (yjData.data && yjData.data.code === 0 && yjData.data.data) {
                yjTeacherInfo = yjData.data.data.find(e => e.bindPhone === user) || {};
            }
            // }
        }

        const accessSpot = {
            attachment: [],
            user_id: userId,
            role: enums.TextToUserType[ssoData.role] || enums.TextToUserType['教师'],
            school_id: schoolId,
            province: location && location[0] || '',
            city: location && location[1] || '',
            ip: req.ip,
            timestamp: new Date(),
            event_id: 'land_web_times',
        };
        try {
            await db.collection('access_spot').insert(accessSpot);
        } catch (err) {
            console.log(err.stack);
        }
        let userData = {
            userId: userId,
            id: userId,
            isVip: false,
            name: ssoData.name || yjTeacherInfo.name || (userInfo && userInfo.name),
            province: location && location[0],
            city: location && location[1],
            district: location && location[2],
            grade: ssoData.grade || lodash.split(yjTeacherInfo._roleNJ, ',')[0] || '',
            avatar: ssoData.avatar,
            role: ssoData.role || '教师',
            schoolId: schoolId,
            schoolName: ssoData.schoolName || (userInfo && userInfo.sch_name),
            phone: ssoData.phone || casDate.cellphone,
        };

        if (!userInfo) {
            let curUserData = {
                _id: userId,
                name: userData.name,
                sch_id: userData.schoolId,
                sch_name: userData.schoolName,
                curr: {},
                trace: {},
                finished: 0,
                ctime: new Date(),
                utime: new Date()
            };
            await db.collection('user').insertOne(curUserData);
            userInfo = curUserData;
            await user_log_service.register(userInfo);
            const curAccessSpot = {
                attachment: [],
                user_id: userId,
                role: enums.TextToUserType[ssoData.role] || enums.TextToUserType['教师'],
                school_id: schoolId,
                province: location && location[0] || '',
                city: location && location[1] || '',
                ip: req.ip,
                timestamp: new Date(),
                event_id: enums.EventType.DL_TIKU,
            };
            try {
                await db.collection('access_spot').insert(curAccessSpot);
            } catch (err) {
                console.log(err.stack);
            }
        }
        let setData = {
            utime: new Date(),
            last_time: new Date(),
            role: enums.TextToUserType[userData.role],
            province: location && location[0] || '',
            city: location && location[1] || '',
            district: location && location[2] || '',
            phone: userData.phone,

            login_name: user,
            name: userData.name || userInfo.name,
            sch_id: userData.schoolId || userInfo.sch_id,
            sch_name: userData.schoolName || userInfo.sch_name,

            'trace.period': enums.GradePhaseMap[userData.grade] || defaultPeriod,
            'trace.subject': subjectUtils.regularSubject(lodash.split(yjTeacherInfo._roleKM, ',')[0]) || defaultSubject,
            finished: 1,
            'curr.period': enums.GradePhaseMap[userData.grade] || defaultPeriod,
            'curr.subject': subjectUtils.regularSubject(lodash.split(yjTeacherInfo._roleKM, ',')[0]) || defaultSubject,
        };
        user_vip_service.sync_school_vip(schoolInfo, setData);
        user_vip_service.copy_vip_info(setData, userData);
        await db.collection('user').updateOne({ _id: userId }, { $set: setData });
        // if (userInfo && userInfo.is_vip && userInfo.expired_time > new Date()) {
        //     userData.isVip = userInfo.is_vip;
        //     if (Object.values(enums.MemberType).includes(userInfo.vip_type)) {
        //         userData.vipType = userInfo.vip_type;
        //     } else {
        //         userData.vipType = enums.MemberType.TIKU_PERSONAL;
        //     }
        //     userData.startTime = userInfo.start_time && userInfo.start_time.getTime();
        //     userData.expiredTime = userInfo.expired_time && userInfo.expired_time.getTime();
        // }

        // if (Object.values(enums.SchoolVipType).includes(userData.vipType)) {
        //     if (schoolInfo && schoolInfo.expired_time > new Date()) {
        //         userData.vipType = schoolInfo.vip_type;
        //         userData.startTime = schoolInfo.start_time && schoolInfo.start_time.getTime();
        //         userData.expiredTime = schoolInfo.expired_time && schoolInfo.expired_time.getTime();
        //         userData.expireMonth = schoolInfo.expire_month || DEFAULT_EXPIRE_MONTH;
        //     }
        // }

        // if (userData.vipType !== enums.MemberType.TIKU_PERSONAL && userData.schoolId && userData.role === '教师') {
        //     if (schoolInfo && schoolInfo.teachers && schoolInfo.teachers.includes(userData.id) && schoolAppUsage && schoolAppUsage.appUsages && schoolAppUsage.appUsages[0] && schoolAppUsage.appUsages[0].status && new Date(schoolAppUsage.appUsages[0].endDate) > new Date()) {
        //         userData.isVip = true;
        //         userData.vipType = enums.CrmVersionToMemberType[schoolAppUsage.appUsages[0].name];
        //         userData.startTime = new Date(schoolAppUsage.appUsages[0].beginDate).getTime();
        //         userData.expiredTime = new Date(schoolAppUsage.appUsages[0].endDate).getTime();
        //         userData.expireMonth = schoolInfo.expire_month || DEFAULT_EXPIRE_MONTH;
        //     } else if (schoolInfo && Object.values(enums.YjSchoolVersionTypeToMemberType).includes(schoolInfo.vip_type) && schoolInfo.expired_time > new Date()) {
        //         userData.isVip = true;
        //         userData.vipType = schoolInfo.vip_type;
        //         userData.startTime = schoolInfo.start_time && schoolInfo.start_time.getTime();
        //         userData.expiredTime = schoolInfo.expired_time && schoolInfo.expired_time.getTime();
        //         userData.expireMonth = schoolInfo.expire_month || DEFAULT_EXPIRE_MONTH;
        //     }
        // }
        await user_log_service.login(userInfo);
        _setCookie(res, userData, userInfo, schoolInfo, schoolAppUsage);

        // let value = aes.encript(jwt.encode(userData));
        // res.cookie(TIKUSERVER.sessionIdCookie.name, value, TIKUSERVER.sessionIdCookie.options);
        // res.cookie(TIKUSERVER.userInfo.name, JSON.stringify(userData), cookie.options);

        return resWrap.succ({});
    } catch (err) {
        logger.error(err);
        return resWrap.error('HANDLE_ERROR')
    }
};

const getUserLoginTokenSchema = Joi.object({
    id: Joi.string().optional(),
    userid: Joi.string().optional(),
    token: Joi.string().optional(),
    go: Joi.string().optional(),
    platform: Joi.string().optional(),
    hfsToken: Joi.string().optional(),
    login_from: Joi.string().optional(),
}).without('token', 'hfsToken')
    .unknown(true);


async function getYjGradeInfo(yjTeacherInfo) {
    if (_.isEmpty(yjTeacherInfo)) return null;
    const schoolId = _.get(yjTeacherInfo, 'schoolId', 0);
    const grades = _.get(yjTeacherInfo, '_roleNJ', '');
    const subjects = _.get(yjTeacherInfo, '_roleKM', '');
    const result = {
        period: defaultPeriod,
        subject: defaultSubject,
        grade: ''
    };
    if (subjects) {
        result.subject = subjectUtils.regularSubject(subjects.split(',')[0]);
    }
    if (grades) {
        const yjGradeMapping = await db.collection('yj_grade_mapping').findOne({yj_grade: grades.split(',')[0]});
        if (!_.isEmpty(yjGradeMapping)) {
            result.period = yjGradeMapping.period;
            result.grade = yjGradeMapping.grade;
        } else {
            // 根据学制获取学段
            if (schoolId) {
                const edu = await yjClient.getSchoolEdu(schoolId);
                const eduSystem = _.get(edu, 'eduSystem', '');
                if (eduSystem) {
                    const periods = enums.ZHISHIXUEDUAN[eduSystem];
                    if (_.size(periods)) {
                        result.period = periods[periods.length - 1];
                    }
                }
            }
        }
    }
    return result;
}

const loginToken = async (req, res, next) => {
    let resWrap = new ResponseWrapper(req, res);

    try {
        let { id, userid, token, go, platform, hfsToken, login_from } = await Joi.validate(req.query, getUserLoginTokenSchema);

        // 生涯学生走其他途径登录
        if (platform === 'shengya') return next();
        // 好分数学生暂时走kbapp和verify登录
        if (login_from === 'kbapp') return next();

        const qixinInfo = userid && token && utils.parseQXToken(token);

        let userData, userId, schoolId, userInfo;
        let userPeriod = '高中', userExactSubject = '数学';
        let schoolData = await redisCache.getYunXiaoIoSchoolMapCache();
        // 企信扫码
        if (userid && token && qixinInfo && qixinInfo[0]) {
            if (userid !== qixinInfo[3]) {
                return resWrap.error('AUTH_ERROR', '企信id与token解析不对应。');
            }

            let userName = qixinInfo[0];
            userInfo = await db.collection('user').findOne({ qixin_id: userid });

            let bind = userInfo ? false : true;
            userId = userInfo && userInfo._id || 0;
            schoolId = userInfo && userInfo.sch_id || 1;
            let schoolName = userInfo && userInfo.sch_name || '企信';

            userData = {
                id: userId, // 默认
                userId: userId, // 默认
                isVip: false,
                name: userName,
                grade: '',
                avatar: '',
                role: '教师',
                schoolId: schoolId, // 默认假的
                schoolName: schoolName, // 默认假的
                need_bind: bind, // 是否需要绑定
                qxid: userid
            }
        } else if (token) { // kb-apps应用中心web跳转
            let passportUrl = URL.format({
                protocol: SSOERV.protocol,
                hostname: SSOERV.hostname,
                pathname: '/passport/v1/user/info/',
                port: SSOERV.port,
            });
            let opstions = { headers: { 'Content-Type': 'application/json', }, timeout: 50000 };
            let ssoData = await axios.post(passportUrl, { token: token }, opstions).then(utils.handler);
            if (!ssoData) return resWrap.error('PARAMETERS_ERROR', '账号密码错误');

            userId = Number(ssoData.id);
            userInfo = await db.collection('user').findOne({ _id: userId });
            schoolId = Number(schoolData[ssoData.school_id] || ssoData.school_id);

            userData = {
                id: userId,
                userId: userId,
                name: ssoData.name,
                grade: ssoData.grade,
                avatar: ssoData.avatar,
                role: ssoData.role,
                schoolId: schoolId,
                schoolName: ssoData.school,
                phone: ssoData.phone,
            };
        } else if (hfsToken) { // hfs教师端web跳转
            const HFS_TEACHER_SERVER = config.get('HFS_TEACHER_SERVER');
            const teacherSession = req.cookies && req.cookies[HFS_TEACHER_SERVER.session];
            const ssoInfoUrl = URL.format({
                protocol: HFS_TEACHER_SERVER.protocol,
                hostname: HFS_TEACHER_SERVER.hostname,
                pathname: '/teacher-v2/teachers/info/hfs-token',
                port: HFS_TEACHER_SERVER.port,
            });
            const opstions = { params: { hfsToken: hfsToken }, headers: { Cookie: HFS_TEACHER_SERVER.session + '=' + teacherSession }, timeout: 50000 };
            const data = await axios.get(ssoInfoUrl, opstions).then(utils.handler);
            if (!data) return resWrap.error('PARAMETERS_ERROR', '账号密码错误');

            schoolId = Number(schoolData[data.schoolId] || data.schoolId);
            userId = Number(data.id);

            const class_ = data.classes[0] || { 'grade': '' };
            userData = {
                id: userId,
                userId: userId,
                name: data.name,
                grade: class_.grade,
                avatar: data.avatar,
                role: '教师',
                source: enums.UserSource.HFS,
                schoolId: schoolId,
                schoolName: data.schoolName,
                phone: data.phone,
            };

            //保存好分数教师角色信息
            let gradePhase = data.classes[0] && enums.GradePhaseMap[data.classes[0].grade];
            userPeriod = data.classes[0] ? (['高中', '初中', '小学'].indexOf(gradePhase) === -1 ? '高中' : gradePhase) : '高中';
            userExactSubject = data.classes[0] && data.classes[0].roles ? (['数学', '语文', '英语', '物理', '化学', '生物', '政治', '历史', '地理', '科学'].indexOf(data.classes[0].roles[0]) === -1 ? '数学' : data.classes[0].roles[0]) : '数学';
            userInfo = await db.collection('user').findOne({ _id: userId });
        }

        let schoolInfo, schoolAppUsage, bossSchoolInfo, yjTeacherInfo = {}, location;
        if (schoolId) {
            schoolInfo = await db.collection('school_info').findOne({ _id: schoolId });
            schoolAppUsage = await redisCache.getBossAppUsageSchoolCache(schoolId);
            bossSchoolInfo = await redisCache.getBossSchoolInfoCache(schoolId);
            location = (bossSchoolInfo && bossSchoolInfo.location || '').split('/');
            let yjUrl = URL.format({
                protocol: YJSERVER.protocol,
                hostname: YJSERVER.hostname,
                pathname: '/v1/teacherInfo/Tiku',
                port: YJSERVER.port,
            });
            let opstions = { headers: {}, timeout: 50000 };
            let yjData = await axios.get(yjUrl, { params: { schoolId: schoolId } }, opstions);
            if (yjData.data && yjData.data.code === 0 && yjData.data.data) {
                yjTeacherInfo = yjData.data.data.find(e => e.userId === userId) || {};
            }
        }

        const { period, grade, subject } = getYjGradeInfo(yjTeacherInfo);

        const accessSpot = {
            attachment: [],
            user_id: userId,
            role: enums.TextToUserType[userData.role],
            school_id: schoolId,
            province: location && location[0] || '',
            city: location && location[1] || '',
            ip: req.ip,
            timestamp: new Date(),
            event_id: 'land_web_times',
        };
        try {
            await db.collection('access_spot').insert(accessSpot);
        } catch (err) {
            console.log(err.stack);
        }

        if (userInfo) {
            let setData = {
                utime: new Date(),
                last_time: new Date(),
                role: enums.TextToUserType[userData.role],
                province: location && location[0] || '',
                city: location && location[1] || '',
                district: location && location[2] || '',
                phone: userData.phone,

                name: userData.name,
                sch_id: userData.schoolId,
                sch_name: userData.schoolName,
                'trace.period': period || enums.GradePhaseMap[userData.grade],
                'trace.subject': subject,
                'trace.grade': grade || userData.grade,
                finished: 1,
                'curr.period': period || enums.GradePhaseMap[userData.grade],
                'curr.subject': subject,
                'curr.grade': grade || userData.grade,
            };
            user_vip_service.sync_school_vip(schoolInfo, setData);
            user_vip_service.copy_vip_info(setData, userData);
            await db.collection('user').updateOne({ _id: userId }, { $set: setData });
        } else if (!qixinInfo) {
            let curUserData = {
                _id: userId,
                name: userData.name,
                sch_id: userData.schoolId,
                sch_name: userData.schoolName,
                curr: {
                    period: period || enums.GradePhaseMap[userData.grade],
                    subject: subject,
                    grade: grade || userData.grade,
                },
                trace: {
                    period: period || enums.GradePhaseMap[userData.grade],
                    subject: subject,
                    grade: grade || userData.grade,
                },
                finished: 0,
                ctime: new Date(),
                utime: new Date(),
                last_time: new Date(),
                role: enums.TextToUserType[userData.role],
                province: location && location[0] || '',
                city: location && location[1] || '',
                district: location && location[2] || '',
                phone: userData.phone,
            };
            user_vip_service.sync_school_vip(schoolInfo, curUserData);
            user_vip_service.copy_vip_info(curUserData, userData);
            await db.collection('user').insertOne(curUserData);
            userInfo = curUserData;
            await user_log_service.register(userInfo);

            const eventId = token ? enums.EventType.DL_YJ : enums.EventType.DL_HFS;
            const curAccessSpot = {
                attachment: [],
                user_id: userId,
                role: enums.TextToUserType[ssoData.role] || enums.TextToUserType['教师'],
                school_id: schoolId,
                province: location && location[0] || '',
                city: location && location[1] || '',
                ip: req.ip,
                timestamp: new Date(),
                event_id: eventId,
            };
            try {
                await db.collection('access_spot').insertOne(curAccessSpot);
            } catch (err) {
                console.log(err.stack);
            }
        }
        await user_log_service.login(userInfo);
        _setCookie(res, userData, userInfo, schoolInfo, schoolAppUsage);

        res.setHeader('content-type', 'text/html');
        if (userData.role === '教师') {
            return res.redirect(TIKUTEACHERHOMEPAGE);
        }
        return res.redirect(TIKUSTUDENTHOMEPAGE);
    } catch (error) {
        return res.redirect(TIKUTEACHERHOMEPAGE);
    }
};

const loginHfsCookie = async (req, res) => {
    let resWrap = new ResponseWrapper(req, res);

    try {
        const HFS_TEACHER_SERVER = config.get('HFS_TEACHER_SERVER');
        const teacherSession = req.cookies && req.cookies[HFS_TEACHER_SERVER.session];

        if (!teacherSession) return resWrap.error('PARAMETERS_ERROR', 'cookie 失效');

        const ssoInfoUrl = URL.format({
            protocol: HFS_TEACHER_SERVER.protocol,
            hostname: HFS_TEACHER_SERVER.hostname,
            pathname: '/teacher-v2/teachers/info',
            port: HFS_TEACHER_SERVER.port,
        });
        const opstions = { headers: { Cookie: HFS_TEACHER_SERVER.session + '=' + teacherSession }, timeout: 50000 };

        const data = await axios.get(ssoInfoUrl, opstions).then(utils.handler);

        let schoolData = await redisCache.getYunXiaoIoSchoolMapCache();
        let schoolId = Number(schoolData[data.schoolId] || data.schoolId);
        let userId = Number(data.id);

        let schoolInfo, schoolAppUsage, bossSchoolInfo, location;
        if (schoolId) {
            schoolInfo = await db.collection('school_info').findOne({ _id: schoolId });
            schoolAppUsage = await redisCache.getBossAppUsageSchoolCache(schoolId);
            bossSchoolInfo = await redisCache.getBossSchoolInfoCache(schoolId);
            location = (bossSchoolInfo && bossSchoolInfo.location || '').split('/');
        }

        const class_ = data.classes[0] || { 'grade': '' };
        let user = {
            userId: userId,
            id: userId,
            isVip: false,
            name: data.name,
            province: location && location[0],
            city: location && location[1],
            district: location && location[2],
            grade: class_.grade,
            avatar: data.avatar,
            role: '教师',
            source: enums.UserSource.HFS,
            schoolId: schoolId,
            schoolName: data.school,
            phone: data.phone,
        };

        const accessSpot = {
            attachment: [],
            role: enums.TextToUserType[user.role],
            user_id: userId,
            school_id: schoolId,
            province: location && location[0] || '',
            city: location && location[1] || '',
            ip: req.ip,
            timestamp: new Date(),
            event_id: enums.EventType.LAND_HFSJS_TIMES,
        };
        await db.collection('access_spot').insert(accessSpot);

        //保存好分数教师角色信息
        let gradePhase = data.classes[0] && enums.GradePhaseMap[data.classes[0].grade];
        let userPeriod = data.classes[0] ? (['高中', '初中', '小学'].indexOf(gradePhase) === -1 ? '高中' : gradePhase) : '高中';
        let userExactSubject = null;
        const classes = _.get(data, 'classes', []);
        if (_.size(classes)) {
            for (const cls of classes) {
                const sub = _.get(cls, ['roles', 0], '');
                if (sub) {
                    userExactSubject = sub;
                    break;
                }
            }
        }
        userExactSubject = subjectUtils.regularSubject(userExactSubject) || defaultSubject;
        let userInfo = await db.collection('user').findOne({ _id: userId });
        if (!userInfo) {
            let curUserData = {
                _id: userId,
                name: user.name,
                sch_id: user.schoolId,
                sch_name: user.schoolName,
                curr: {},
                trace: {},
                finished: 0,
                role: enums.TextToUserType[user.role],
                ctime: new Date(),
                utime: new Date()
            };
            await db.collection('user').insertOne(curUserData);
            userInfo = curUserData;
            await user_log_service.register(userInfo);
            const curAccessSpot = {
                attachment: [],
                user_id: userId,
                role: enums.TextToUserType[user.role] || enums.TextToUserType['教师'],
                school_id: schoolId,
                province: location && location[0] || '',
                city: location && location[1] || '',
                ip: req.ip,
                timestamp: new Date(),
                event_id: enums.EventType.DL_HFS,
            };
            try {
                await db.collection('access_spot').insert(curAccessSpot);
            } catch (err) {
                console.log(err.stack);
            }
        }

        let setData = {
            utime: new Date(),
            last_time: new Date(),
            role: enums.TextToUserType[user.role],
            province: location && location[0] || '',
            city: location && location[1] || '',
            district: location && location[2] || '',
            phone: user.phone,

            name: user.name,
            sch_id: user.schoolId,
            sch_name: user.schoolName,
            'trace.period': userPeriod,
            'trace.subject': userExactSubject,
            finished: 1,
            hfs: 1,
            'curr.period': userPeriod,
            'curr.subject': userExactSubject,
        };
        user_vip_service.sync_school_vip(schoolInfo, setData);
        user_vip_service.copy_vip_info(setData, user);
        await db.collection('user').updateOne({ _id: userId }, { $set: setData });
        await user_log_service.login(userInfo);
        _setCookie(res, user, userInfo, schoolInfo, schoolAppUsage);
        res.setHeader('content-type', 'text/html');
        return res.redirect(TIKUTEACHERHOMEPAGE);
    } catch (err) {
        return res.redirect(TIKUTEACHERHOMEPAGE);
    }
}

const postRegistSchema = Joi.object({
    passwd: Joi.string().required(),
    phone: Joi.string().required(),

    name: Joi.string().required(),
    grade: Joi.string().required(),
    subject: Joi.string().required(),

    vCode: Joi.string().required(),
});
const regist = async (req, res) => {
    let resWrap = new ResponseWrapper(req, res);
    try {
        let { phone, passwd, name, grade, subject, vCode } = await Joi.validate(req.body, postRegistSchema);

        let casDate = await axios.post(`${CASSERHOST}/sapi/user/getUserInfo`, { 'cellphone': phone }).then(utils.casUserHandler);
        if (casDate && casDate[0]) return resWrap.error('PARAMETERS_ERROR', '该手机号已存在用户');

        let str = await rediser.redis.get(`tiku:regist:${phone}`);
        if (!str) return resWrap.error('PARAMETERS_ERROR', '验证码错误');
        vCode = JSON.parse(str);
        if (vCode !== vCode) return resWrap.error('PARAMETERS_ERROR', '验证码错误');

        let yjUrl = URL.format({
            protocol: YJYZSERVER.protocol,
            hostname: YJYZSERVER.hostname,
            pathname: '/v1/user/virtual/additionUsers',
            port: YJYZSERVER.port,
        });
        let opstions = { headers: {}, timeout: 50000 };
        let yjData = await axios.post(yjUrl, {
            users: [{
                name: name,
                phone: phone,
                password: passwd,
                schoolId: virtualSchoolId,
                schoolName: virtualSchoolName,
            }]
        }, opstions).then(utils.handler);

        let casUserDate = await axios.post(`${CASSERHOST}/validateUser`, { loginName: phone, password: passwd }).then(utils.casHandler);
        let userId = Number(casUserDate.userId);

        let curUserData = {
            _id: userId,
            name: name,
            sch_id: virtualSchoolId,
            sch_name: virtualSchoolName,
            curr: {
                period: enums.GradePhaseMap[grade] || defaultPeriod,
                subject: subject || defaultSubject,
            },
            trace: {
                period: enums.GradePhaseMap[grade] || defaultPeriod,
                subject: subject || defaultSubject,
            },
            finished: 1,
            phone: phone,
            login_name: phone,
            role: 'teacher',
            init_right: true,
            last_time: new Date(),
            ctime: new Date(),
            utime: new Date(),
        };
        await db.collection('user').insertOne(curUserData);
        await user_right_service.init_rights(userId);
        const curAccessSpot = {
            attachment: [],
            user_id: userId,
            role: enums.TextToUserType['教师'],
            school_id: virtualSchoolId,
            province: '',
            city: '',
            ip: req.ip,
            timestamp: new Date(),
            event_id: enums.EventType.ZC_TIKU,
        };
        await user_log_service.register(curUserData);
        try {
            await db.collection('access_spot').insert(curAccessSpot);
        } catch (err) {
            console.log(err.stack);
        }
        return resWrap.succ({});
    } catch (err) {
        logger.error(err);
        return resWrap.error('HANDLE_ERROR', err.message);
    }
};

const postRegistVcodesSchema = Joi.object({
    phone: Joi.string().required(),
    reCaptcha: Joi.number().required(),
    captchaId: Joi.string().required(),
});
const registVcodes = async (req, res) => {
    let resWrap = new ResponseWrapper(req, res);
    try {
        let { phone, reCaptcha, captchaId } = await Joi.validate(req.body, postRegistVcodesSchema);

        let casDate = await axios.post(`${CASSERHOST}/sapi/user/getUserInfo`, { 'cellphone': phone }).then(utils.casUserHandler);
        if (casDate && casDate[0]) return resWrap.error('PARAMETERS_ERROR', '该手机号已存在用户');

        const captchaData = await axios.post(`${CAPTCHASERVERHOST}/v3/slider-captcha/match-result`, { captchaId: captchaId, dragPosition: reCaptcha, }).then(utils.handler);
        if (captchaData && !captchaData.isMatch) return resWrap.error('PARAMETERS_ERROR', '账号密码错误');

        // let str = await rediser.redis.get(`tiku:send:msg:count:${phone}`);
        // if (str > 5) return resWrap.error('PARAMETERS_ERROR', '手机号发送短信次数已达上线');

        let str = await rediser.redis.get(`tiku:regist:${phone}`);
        if (str) return resWrap.error('PARAMETERS_ERROR', '手机号已发送注册短信');

        const vCode = utils.getRandomString(vCodeLength, 'n');
        const placeholder = {
            vCode: vCode
        };
        const content = utils.renderTemplate(registContent, placeholder);
        const templateId = enums.ShortMessageBDYMSTemplate['sms-tmpl-QPqQGb64796'];
        const contentVar = {
            vCode: `${vCode}`
        };
        rediser.redis.setex(`tiku:regist:${phone}`, 60 * 3, JSON.stringify(vCode));

        const creareFields = {
            ctime: new Date(),
            utime: new Date(),
            type: enums.MessageSendType.SHORT_MSG,
            situation: enums.MessageSituation.REGIST_USER_V_CODE,
            unique_key: `${enums.MessageSituation.REGIST_USER_V_CODE}-${phone}-${Date.now()}`,
            // user_id: req.user.userId,
            phone: phone,
            send_status: enums.MessageStatus.PREPARED,
            short_msg_content: content,
            short_msg_content_config: {
                platform: 'bdy',
                template_id: templateId,
                content_var: contentVar
            }
        };

        let messageCentreResult = await db.collection('@MessageCentre').insertOne(creareFields);
        const msgId = messageCentreResult.insertedId;
        const msgResult = await shortMsg.sendMsg(templateId, [phone], contentVar);

        const updateFields = {
            send_time: new Date(),
            send_status: enums.MessageStatus.SUCCESS
        };
        const result = { id: msgId.toString() };
        if (msgResult) {
            await db.collection('@MessageCentre').updateOne({ _id: msgId }, { $set: updateFields });
        }
        if (process.env.NODE_ENV !== 'production') {
            result.test = {
                vCode: vCode
            };
        }
        return resWrap.succ(result);
    } catch (err) {
        logger.error(err);
        return resWrap.error('HANDLE_ERROR', err.message);
    }
};

const getUserByPhoneSchema = Joi.object({
    phone: Joi.string().required(),
});
const getUserByPhone = async (req, res) => {
    let resWrap = new ResponseWrapper(req, res);
    try {
        const { phone } = await Joi.validate(req.query, getUserByPhoneSchema);

        let casDate = await axios.post(`${CASSERHOST}/sapi/user/getUserInfo`, { 'cellphone': phone }).then(utils.casUserHandler);
        if (casDate && casDate[0]) return resWrap.error('PARAMETERS_ERROR', '该手机号已存在用户');
        return resWrap.succ({});
    } catch (err) {
        logger.error(err);
        return resWrap.error('HANDLE_ERROR', err.message);
    }
};

const getYjExamListSchema = Joi.object({
    review_mode: Joi.number().required(),
    id: Joi.string().required(),
    exam_name: Joi.string(),
}).unknown(true);
const getYjExamList = async (req, res) => {
    let resWrap = new ResponseWrapper(req, res);
    try {
        const { review_mode, id, exam_name } = await Joi.validate(req.query, getYjExamListSchema);

        let data = {
            reviewMode: review_mode,
        };
        if (exam_name) data.examName = exam_name;
        const token = await utils.getYjToken(data, req.user);

        let yjUrl = URL.format({
            protocol: YJSERVER.protocol,
            hostname: YJSERVER.hostname,
            pathname: '/v1/query/exam/list',
            port: YJSERVER.port,
        });
        let yjDate = await axios.get(yjUrl, { params: data, headers: { token } }).then(utils.yjHandler);

        const exampaperInfo = await db_open.collection(enums.OpenSchema.user_paper).findOne({ _id: new ObjectId(id) });
        yjDate = yjDate.map(e => {
            let subjectInfo = e.subjects.find(item => item.subjectName.includes(exampaperInfo.subject));
            delete e.subjects;
            if (subjectInfo) {
                e.subjectName = subjectInfo.subjectName;
                e.subjectId = subjectInfo.subjectId;
            }
            return lodash.mapKeys(e, function (value, key) {
                return lodash.snakeCase(key);
            });
        }).filter(e => e.subject_name);
        return resWrap.succ(yjDate);
    } catch (err) {
        logger.error(err);
        return resWrap.error('HANDLE_ERROR', err.message);
    }
};

const getYjExamUrlSchema = Joi.object({
    exam_id: Joi.number().required(),
}).unknown(true);
const getYjExamUrl = async (req, res) => {
    let resWrap = new ResponseWrapper(req, res);
    try {
        const { exam_id } = await Joi.validate(req.params, getYjExamUrlSchema);

        let data = {
            userId: req.user.userId,
            schoolId: req.user.schoolId,
            examId: exam_id,
            pageType: 'examInfo',
        };
        const token = await utils.getYjToken(data, req.user);
        data.token = token;
        let querystring = (new URLSearchParams(data)).toString();
        let yjUrl = `${config.get('yjWebUrl')}/filter/yue/v1/api/tiku/user/login?${querystring}`;
        return resWrap.succ({ url: yjUrl });
    } catch (err) {
        logger.error(err);
        return resWrap.error('HANDLE_ERROR', err.message);
    }
};

const getYjGradeSubject = async (req, res) => {
    let resWrap = new ResponseWrapper(req, res);
    try {
        return resWrap.succ({

            subjects: Object.values(enums.Subject),
            grades: {
                '高中': Object.values(enums.HighGrade),
                '初中': Object.values(enums.MiddleGrade),
                '小学': Object.values(enums.PrimaryGrade),
            }
        });
    } catch (err) {
        logger.error(err);
        return resWrap.error('HANDLE_ERROR', err.message);
    }
};

// help
const getVip = async (req, res) => {
    // 上线前旧cookie、升学宝会员直接返回cookie内数据
    if (req.user && req.user.isVip) return;
    const userId = req.user.userId;

    // 学生家长角色 好分数360会员处理
    if (req.user.role !== '教师') {
        const vipCookie = config.get('TIKU_SERVER').isVipCookie;
        const vipCookieName = vipCookie.name;
        const isMember = req.cookies[vipCookieName];
        if (isMember) {
            try {
                var isMemberObj = jwt.decode(aes.decript(isMember));
                if (isMemberObj.isMember) {
                    req.user.isVip = true;
                    req.user.vipType = enums.MemberType.HFS_360;
                    req.user.startTime = isMemberObj.startTime;
                    req.user.expiredTime = isMemberObj.expiredTime;
                }
                return;
            } catch (err) {
                // 如果非token失效的异常，则使用旧逻辑，直接设置用户为VIP
                // 否则，不处理，执行下方的代码，进行重新设置token
                if (err.message !== 'token has expired') {
                    // req.isVip = true;
                    return;
                }
            }
        }
        try {
            let options = {
                path: vipCookie.options.path,
                domain: vipCookie.options.domain
            };
            let cookieValue = {
                userId: userId
            };

            const HFS_VIP_SERVER = config.get('HFS_VIP_SERVER');
            const hfsUrl = URL.format({
                protocol: HFS_VIP_SERVER.protocol,
                hostname: HFS_VIP_SERVER.hostname,
                port: HFS_VIP_SERVER.port,
                pathname: `/v1/students/${userId}/member-status`
            });
            const hfsDate = await axios.post(hfsUrl, {});
            if (hfsDate && hfsDate.data && hfsDate.data.data && hfsDate.data.code === 0 && hfsDate.data.data.isMember) {
                const curDate = hfsDate.data.data;
                req.user.isVip = true;
                req.user.vipType = enums.MemberType.HFS_360;
                req.user.startTime = new Date(curDate.memberStart).getTime();
                req.user.expiredTime = new Date(curDate.memberEnd).getTime();
                options.maxAge = vipCookie.options.maxAgeVip;
                cookieValue.exp = new Date(Date.now() + vipCookie.options.maxAgeVip);
                cookieValue.isMember = true;
                cookieValue.vipType = enums.MemberType.HFS_360;
                cookieValue.startTime = new Date(curDate.memberStart).getTime();
                cookieValue.expiredTime = new Date(curDate.memberEnd).getTime();
            } else {
                options.maxAge = vipCookie.options.maxAgeNvip;
                cookieValue.exp = new Date(Date.now() + vipCookie.options.maxAgeNvip);
                cookieValue.isMember = false;
            }

            const cvalue = aes.encript(jwt.encode(cookieValue)) || '';
            res.cookie(vipCookie.name, cvalue, options);
        } catch (err) {
            logger.error(err);
        }
        return;
    }

    // 系统内会员处理
    try {
        let userInfo = await db.collection('user').findOne({ _id: userId });
        let schoolId = userInfo.sch_id || (req.user && req.user.schoolId);
        if (schoolId) {
            let schoolData = await redisCache.getYunXiaoIoSchoolMapCache();
            schoolId = Number(schoolData[schoolId] || schoolId);
        }

        if (userInfo && userInfo.is_vip && userInfo.expired_time > new Date()) {
            req.user.isVip = userInfo.is_vip;
            req.user.vipType = enums.MemberType.TIKU_PERSONAL;
            req.user.startTime = userInfo.start_time && userInfo.start_time.getTime();
            req.user.expiredTime = userInfo.expired_time && userInfo.expired_time.getTime();
            return;
        }
        if (req.user.role !== '教师') return;

        // 教师角色会员逻辑
        if (schoolId) {
            const schoolInfo = await db.collection('school_info').findOne({ _id: schoolId });
            const schoolAppUsage = await redisCache.getBossAppUsageSchoolCache(schoolId);
            if (schoolInfo && schoolInfo.teachers && schoolInfo.teachers.includes(userId) && schoolAppUsage && schoolAppUsage.appUsages && schoolAppUsage.appUsages[0] && schoolAppUsage.appUsages[0].status && new Date(schoolAppUsage.appUsages[0].endDate) > new Date()) {
                req.user.isVip = true;
                req.user.vipType = enums.CrmVersionToMemberType[schoolAppUsage.appUsages[0].name];
                req.user.startTime = new Date(schoolAppUsage.appUsages[0].beginDate).getTime();
                req.user.expiredTime = new Date(schoolAppUsage.appUsages[0].endDate).getTime();
                req.user.expireMonth = schoolInfo.expire_month || DEFAULT_EXPIRE_MONTH;
            } else if (schoolInfo && Object.values(enums.YjSchoolVersionTypeToMemberType).includes(schoolInfo.vip_type) && schoolInfo.expired_time > new Date()) {
                req.user.isVip = true;
                req.user.vipType = schoolInfo.vip_type;
                req.user.startTime = schoolInfo.start_time && schoolInfo.start_time.getTime();
                req.user.expiredTime = schoolInfo.expired_time && schoolInfo.expired_time.getTime();
                req.user.expireMonth = schoolInfo.expire_month || DEFAULT_EXPIRE_MONTH;
            }

        }
        return;
    } catch (err) {
        logger.error(err);
        return;
    }
}

const homeworkLogin = async (req, res) => {
    let resWrap = new ResponseWrapper(req, res);
    const queryUserId = req.query.userId;
    try {
        if (!queryUserId) {
            return resWrap.error('PARAMETERS_ERROR', '用户ID不能为空!');
        }
        const HFS_TEACHER_V3_SERVER = config.get('HFS_TEACHER_V3_SERVER');
        const ssoInfoUrl = URL.format({
            protocol: HFS_TEACHER_V3_SERVER.protocol,
            hostname: HFS_TEACHER_V3_SERVER.hostname,
            pathname: '/v3/teachers/info/tiku',
            search: qs.stringify({
                userId: queryUserId,
                appKey: HFS_TEACHER_V3_SERVER.appKey,
            })
        });
        const opstions = { timeout: 50000 };
        const data = await axios.get(ssoInfoUrl, opstions).then(utils.handler);
        if (lodash.isEmpty(data)) {
            return resWrap.error('PARAMETERS_ERROR', '用户信息不存在!');
        }
        let schoolData = await redisCache.getYunXiaoIoSchoolMapCache();
        let schoolId = Number(schoolData[data.schoolId] || data.schoolId);
        let userId = Number(data.id);

        let schoolInfo, schoolAppUsage, bossSchoolInfo, location;
        if (schoolId) {
            schoolInfo = await db.collection('school_info').findOne({ _id: schoolId });
            schoolAppUsage = await redisCache.getBossAppUsageSchoolCache(schoolId);
            bossSchoolInfo = await redisCache.getBossSchoolInfoCache(schoolId);
            location = (bossSchoolInfo && bossSchoolInfo.location || '').split('/');
        }
        const class_ = data.classes[0] || { 'grade': '' };
        let user = {
            userId: userId,
            id: userId,
            isVip: false,
            name: data.name,
            province: location && location[0],
            city: location && location[1],
            district: location && location[2],
            grade: class_.grade,
            avatar: data.avatar,
            role: '教师',
            source: enums.UserSource.HFS,
            schoolId: schoolId,
            schoolName: data.school,
            phone: data.phone,
        };
        const accessSpot = {
            attachment: [],
            role: enums.TextToUserType[user.role],
            user_id: userId,
            school_id: schoolId,
            province: location && location[0] || '',
            city: location && location[1] || '',
            ip: req.ip,
            timestamp: new Date(),
            event_id: enums.EventType.LAND_HFSJS_TIMES,
        };
        await db.collection('access_spot').insertOne(accessSpot);
        //保存好分数教师角色信息
        let gradePhase = data.classes[0] && enums.GradePhaseMap[data.classes[0].grade];
        let userPeriod = data.classes[0] ? (['高中', '初中', '小学'].indexOf(gradePhase) === -1 ? '高中' : gradePhase) : '高中';
        let userExactSubject = null;
        const classes = _.get(data, 'classes', []);
        if (_.size(classes)) {
            for (const cls of classes) {
                const sub = _.get(cls, ['roles', 0], '');
                if (sub) {
                    userExactSubject = sub;
                    break;
                }
            }
        }
        userExactSubject = subjectUtils.regularSubject(userExactSubject) || defaultSubject;
        let userInfo = await db.collection('user').findOne({ _id: userId });
        if (!userInfo) {
            let curUserData = {
                _id: userId,
                name: user.name,
                sch_id: user.schoolId,
                sch_name: user.schoolName,
                curr: {},
                trace: {},
                finished: 0,
                role: enums.TextToUserType[user.role],
                ctime: new Date(),
                utime: new Date()
            };
            await db.collection('user').insertOne(curUserData);
            userInfo = curUserData;
            await user_log_service.register(userInfo);
            const curAccessSpot = {
                attachment: [],
                user_id: userId,
                role: enums.TextToUserType[user.role] || enums.TextToUserType['教师'],
                school_id: schoolId,
                province: location && location[0] || '',
                city: location && location[1] || '',
                ip: req.ip,
                timestamp: new Date(),
                event_id: enums.EventType.DL_HFS,
            };

            try {
                await db.collection('access_spot').insertOne(curAccessSpot);
            } catch (err) {
                console.log(err.stack);
            }
        }

        let setData = {
            utime: new Date(),
            last_time: new Date(),
            role: enums.TextToUserType[user.role],
            province: location && location[0] || '',
            city: location && location[1] || '',
            district: location && location[2] || '',
            phone: user.phone,

            name: user.name,
            sch_id: user.schoolId,
            sch_name: user.schoolName,
            'trace.period': userPeriod,
            'trace.subject': userExactSubject,
            finished: 1,
            hfs: 1,
            'curr.period': userPeriod,
            'curr.subject': userExactSubject,
        };
        user_vip_service.sync_school_vip(schoolInfo, setData);
        user_vip_service.copy_vip_info(setData, user);
        await db.collection('user').updateOne({ _id: userId }, { $set: setData });
        // res, user => userData, userInfo => userInfo,
        user.expireMonth = DEFAULT_EXPIRE_MONTH;
        user.exp = Date.now() + TIKUSERVER.sessionIdCookie.options.maxAge;

        // if (userInfo && userInfo.is_vip && userInfo.expired_time > new Date()) {
        //     user.isVip = userInfo.is_vip;
        //     user.vipType = enums.MemberType.TIKU_PERSONAL;
        //     user.startTime = userInfo.start_time && userInfo.start_time.getTime();
        //     user.expiredTime = userInfo.expired_time && userInfo.expired_time.getTime();
        // }

        // if (user.vipType !== enums.MemberType.TIKU_PERSONAL && user.schoolId && user.role === '教师') {
        //     if (schoolInfo && schoolInfo.teachers && schoolInfo.teachers.includes(user.id) && schoolAppUsage && schoolAppUsage.appUsages && schoolAppUsage.appUsages[0] && schoolAppUsage.appUsages[0].status && new Date(schoolAppUsage.appUsages[0].endDate) > new Date()) {
        //         user.isVip = true;
        //         user.vipType = enums.CrmVersionToMemberType[schoolAppUsage.appUsages[0].name];
        //         user.startTime = new Date(schoolAppUsage.appUsages[0].beginDate).getTime();
        //         user.expiredTime = new Date(schoolAppUsage.appUsages[0].endDate).getTime();
        //         user.expireMonth = schoolInfo.expire_month || DEFAULT_EXPIRE_MONTH;
        //     } else if (schoolInfo && Object.values(enums.YjSchoolVersionTypeToMemberType).includes(schoolInfo.vip_type) && schoolInfo.expired_time > new Date()) {
        //         user.isVip = true;
        //         user.vipType = schoolInfo.vip_type;
        //         user.startTime = schoolInfo.start_time && schoolInfo.start_time.getTime();
        //         user.expiredTime = schoolInfo.expired_time && schoolInfo.expired_time.getTime();
        //         user.expireMonth = schoolInfo.expire_month || DEFAULT_EXPIRE_MONTH;
        //     }
        // }
        user_vip_service.add_user_vip(user, userInfo);
        user_vip_service.add_school_vip(user, schoolInfo);
        // let value = aes.encript(jwt.encode(user));
        // res.cookie(TIKUSERVER.sessionIdCookie.name, value, TIKUSERVER.sessionIdCookie.options);
        // res.cookie(TIKUSERVER.userInfo.name, JSON.stringify(user), TIKUSERVER.sessionIdCookie.options);
        const resObj = {
            token: aes.encript(jwt.encode(user))
        }
        return resWrap.succ(resObj);
    } catch (err) {
        logger.error(err.message);
        return resWrap.error('HANDLE_ERROR', err.message);
    }

}

function _setCookie(res, userData, userInfo, schoolInfo, schoolAppUsage) {
    userData.expireMonth = DEFAULT_EXPIRE_MONTH;
    userData.exp = Date.now() + TIKUSERVER.sessionIdCookie.options.maxAge;

    // if (userInfo && userInfo.is_vip && userInfo.expired_time > new Date()) {
    //     userData.isVip = userInfo.is_vip;
    //     if (Object.values(enums.MemberType).includes(userInfo.vip_type)) {
    //         userData.vipType = userInfo.vip_type;
    //     } else {
    //         userData.vipType = enums.MemberType.TIKU_PERSONAL;
    //     }
    //     userData.startTime = userInfo.start_time && userInfo.start_time.getTime();
    //     userData.expiredTime = userInfo.expired_time && userInfo.expired_time.getTime();
    // }

    // if (Object.values(enums.SchoolVipType).includes(userData.vipType)) {
    //     if (schoolInfo && schoolInfo.expired_time > new Date()) {
    //         userData.vipType = schoolInfo.vip_type;
    //         userData.startTime = schoolInfo.start_time && schoolInfo.start_time.getTime();
    //         userData.expiredTime = schoolInfo.expired_time && schoolInfo.expired_time.getTime();
    //         userData.expireMonth = schoolInfo.expire_month || DEFAULT_EXPIRE_MONTH;
    //     }
    // }
    user_vip_service.add_user_vip(userData, userInfo);
    user_vip_service.add_school_vip(userData, schoolInfo);

    // if (userData.vipType !== enums.MemberType.TIKU_PERSONAL && userData.schoolId && userData.role === '教师') {
    //     if (schoolInfo && schoolInfo.teachers && schoolInfo.teachers.includes(userData.id) && schoolAppUsage && schoolAppUsage.appUsages && schoolAppUsage.appUsages[0] && schoolAppUsage.appUsages[0].status && new Date(schoolAppUsage.appUsages[0].endDate) > new Date()) {
    //         userData.isVip = true;
    //         userData.vipType = enums.CrmVersionToMemberType[schoolAppUsage.appUsages[0].name];
    //         userData.startTime = new Date(schoolAppUsage.appUsages[0].beginDate).getTime();
    //         userData.expiredTime = new Date(schoolAppUsage.appUsages[0].endDate).getTime();
    //         userData.expireMonth = schoolInfo.expire_month || DEFAULT_EXPIRE_MONTH;
    //     } else if (schoolInfo && Object.values(enums.YjSchoolVersionTypeToMemberType).includes(schoolInfo.vip_type) && schoolInfo.expired_time > new Date()) {
    //         userData.isVip = true;
    //         userData.vipType = schoolInfo.vip_type;
    //         userData.startTime = schoolInfo.start_time && schoolInfo.start_time.getTime();
    //         userData.expiredTime = schoolInfo.expired_time && schoolInfo.expired_time.getTime();
    //         userData.expireMonth = schoolInfo.expire_month || DEFAULT_EXPIRE_MONTH;
    //     }
    // }

    let value = aes.encript(jwt.encode(userData));
    res.cookie(TIKUSERVER.sessionIdCookie.name, value, TIKUSERVER.sessionIdCookie.options);
    res.cookie(TIKUSERVER.userInfo.name, JSON.stringify(userData), TIKUSERVER.sessionIdCookie.options);
    return;
}


/**
 * 根据用户的IP获取用户的地址
 * @param {*} req
 * @param {*} res
 */
const getIpRegion = async (req, res) => {
    let resWrapper = new ResponseWrapper(req, res);
    try {
        const ip = getClientIp(req);
        const region = await searchRegionByIp(ip);
        if (!region.region) {
            return resWrapper.succ(null);
        }
        let tempRegion = region.region.split('|');
        return resWrapper.succ({
            country: tempRegion[0],
            region: tempRegion[1],
            province: tempRegion[2].replace('省', ''),
            city: tempRegion[3].replace('市', ''),
            isp: tempRegion[4],
        });
    } catch (err) {
        console.log(err);
        return resWrapper.error('HANDLE_ERROR', err.message);
    }
}

async function addFilterInfo(req, res) {
    let resWrapper = new ResponseWrapper(req, res);
    try {
        const userId = req.user.userId;
        const newFilter = req.body;
        if (lodash.isEmpty(newFilter)) {
            return resWrapper.error('PARAMETERS_ERROR');
        }
        let userInfo = await db.collection('user').findOne({ _id: userId });
        let filters = userInfo.filters || [];
        filters = filters.filter(e => !(e.type === newFilter.type && e.period === newFilter.period && e.subject === newFilter.subject));
        filters.push(newFilter);
        const ds = {
            utime: new Date(),
            filters: filters
        };

        await db.collection('user').updateOne({ _id: userId }, { $set: ds });
        return resWrapper.succ('');
    } catch (err) {
        logger.error(err);
        return resWrapper.error('HANDLE_ERROR', err.message);
    }
}

async function getFilterInfo(req, res) {
    let resWrapper = new ResponseWrapper(req, res);
    try {
        const userId = req.user.userId;
        let userInfo = await db.collection('user').findOne({ _id: userId });
        let filters = userInfo.filters || [];
        return resWrapper.succ(filters);
    } catch (err) {
        logger.error(err);
        return resWrapper.error('HANDLE_ERROR', err.message);
    }
}

const JOI_GET_VIP_RIGHT_STATUS = Joi.object({
    period: Joi.string().required(),
    subject: Joi.string().required(),
    type: Joi.string().required(),
    exclude: Joi.string().optional().allow('')
}).unknown(true);

async function getVipRightStatus(req, res) {
    let resWrapper = new ResponseWrapper(req, res);
    try {
        const params = await JOI_GET_VIP_RIGHT_STATUS.validate(req.query);
        const right = await user_right_service.get_use_right(req.user.id, params);
        return resWrapper.succ(right);
    } catch (err) {
        logger.error(err);
        return resWrapper.error('HANDLE_ERROR', err.message);
    }
}

async function getUnifyToken(req, res) {
    let resWrapper = new ResponseWrapper(req, res);
    try {
        const unifyToken = await client.yj.getUnifyToken(req.user.id);
        return resWrapper.succ({unifyToken});
    } catch (err) {
        logger.error(err);
        return resWrapper.error('HANDLE_ERROR', err.message);
    }
}

module.exports = {
    getAssets: getAssets,
    getBooksProfile: getUserBooksProfile,
    putBooksProfile: updateUserBooksProfile,
    getExampapersProfile: getUserExampapersProfile,
    putExampapersProfile: updateUserExampapersProfile,
    getTemporaryKnowItem: getTemporaryKnowItem,
    addTemporaryKnowItem: addTemporaryKnowItem,
    getUserInfo: getUserInfo,
    putUserInfo: putUserInfo,
    updateUserInfoTrace: updateUserInfoTrace,
    getUserInfoTrace: getUserInfoTrace,
    logOut,
    getUserInfoProfile,
    updateHfsInfo,
    updateUserCurrInfo,
    setQiXinUnbind,
    setQiXinBinded,
    getYjExamList,
    getYjExamUrl,
    getYjGradeSubject,

    // 免密
    getUserByPhone,
    login,
    loginToken,
    loginHfsCookie,
    regist,
    registVcodes,
    homeworkLogin,
    getIpRegion,
    addFilterInfo,
    getFilterInfo,
    getVipRightStatus,
    getUnifyToken,
};
