const config = require('config');
const expect = require('chai').expect;
const superagent = require('superagent');
const url = require('url');

const port = process.env.NODE_PORT || 80;

const host = url.format({
	protocol: 'http',
	hostname: config.get('host'),
	port: port
});

describe('获取支持教材答案的学科', function() {
	var url = [host, '/kb_api/v2/book_answers/subjects'].join('');
	it('高中 - 正例测试', function(done) {
		superagent	
		.get(url)
		.query({
			period: '高中',
			api_key: 'iyunxiao_tester'	
		})
		.end(function(err, res){
			expect(err).to.be.a('null');
			expect(res).to.not.be.an('null');
			expect(res.status).to.be.equal(200);
			var data = JSON.parse(res.text);
			expect(data).to.have.keys('subjects');
			expect(data.subjects).to.be.an('array');
			expect(data.subjects.length).to.not.be.equal(0);
			done();
		});
	});

	it('初中 - 正例测试', function(done) {
		superagent	
		.get(url)
		.query({
			period: '初中',
			api_key: 'iyunxiao_tester'	
		})
		.end(function(err, res){
			expect(err).to.be.a('null');
			expect(res).to.not.be.an('null');
			expect(res.status).to.be.equal(200);
			var data = JSON.parse(res.text);
			expect(data).to.have.keys('subjects');
			expect(data.subjects).to.be.an('array');
			expect(data.subjects.length).to.not.be.equal(0);
			done();
		});
	});

	it('小学 - 正例测试', function(done) {
		superagent	
		.get(url)
		.query({
			period: '小学',
			api_key: 'iyunxiao_tester'	
		})
		.end(function(err, res){
			expect(err).to.be.a('null');
			expect(res).to.not.be.an('null');
			expect(res.status).to.be.equal(200);
			var data = JSON.parse(res.text);
			expect(data).to.have.keys('subjects');
			expect(data.subjects).to.be.an('array');
			expect(data.subjects.length).to.not.be.equal(0);
			done();
		});
	});

});

describe('获取包含作业记录的教材打包', function() {
	var url = [host, '/kb_api/v2/books/'].join('');
	it('所有教材打包接口', function(done) {
		superagent	
		.get(url)
		.query({
			api_key: 'iyunxiao_tester'	
		})
		.end(function(err, res){
			expect(err).to.be.a('null');
			expect(res).to.not.be.an('null');
			expect(res.status).to.be.equal(200);
			var data = JSON.parse(res.text);
			expect(data).to.have.keys('book');
			expect(data.book).to.be.an('object');
			expect(data.book).to.have.keys('children');
			done();
		});
	});
});

describe('根据含答案教材ID获取答案内容', function() {

	it('教材正例测试', function(done) {
		var url = [host, '/kb_api/v2/books/'+2142044159+'/answer'].join('');
		superagent	
		.get(url)
		.query({
			api_key: 'iyunxiao_tester'	
		})
		.end(function(err, res){
			expect(err).to.be.a('null');
			expect(res).to.not.be.an('null');
			expect(res.status).to.be.equal(200);
			var data = JSON.parse(res.text);
			expect(data).to.have.keys('id', 
				'period', 
				'subject', 
				'press_version', 
				'chapters',
				'ctime',
				'utime',
				'grade');
			expect(data.chapters).to.be.an('array');
			done();
		});
	});

	it('教材反例测试', function(done) {
		var url = [host, '/kb_api/v2/books/123123123/answer'].join('');
		superagent	
		.get(url)
		.query({
			api_key: 'iyunxiao_tester'	
		})
		.end(function(err, res){
			expect(err).to.not.be.a('null');
			expect(res).to.not.be.an('null');
			expect(res.status).to.be.equal(400);
			var data = JSON.parse(res.text);
			expect(data.code).to.be.equal(5);
			done();
		});
	});

});

describe('根据多个章节ID获取章节详情', function() {

	it('正例测试', function(done) {

		var url = [
			host, 
			'/kb_api/v2/book_chapters/2147418111%2C2147418111%2C2147287039%2C2147221503%2C2147155967/answer'
		].join('');

		superagent	
		.get(url)
		.query({
			api_key: 'iyunxiao_tester'	
		})
		.end(function(err, res){
			expect(err).to.be.a('null');
			expect(res).to.not.be.an('null');
			expect(res.status).to.be.equal(200);
			var data = JSON.parse(res.text);
			expect(data).to.have.keys('chapters');
			expect(data.chapters).to.be.an('array');
			done();
		});
	});

});
