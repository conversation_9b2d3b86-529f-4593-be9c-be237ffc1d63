const mongodb = require('mongodb');
const assert = require('assert');
const Thenjs = require('thenjs');
const _ = require('underscore');
const program = require('commander');
const ObjectID = mongodb.ObjectID;

program
	.version('0.1.0')
	.option('-k, --kb [kb]', 'kb db')
	.option('-t, --tiku [tiku]', 'tiku db')
	.parse(process.argv);

var kbURL = program.kb;
var tikuURL = program.tiku;

console.log(kbURL);
console.log(tikuURL);

function transfer(kbObj){
	var retobj = {};

	retobj._id = kbObj._id;

	if(typeof retobj._id === 'string')
		retobj._id = ObjectID(retobj._id);

	retobj.id = kbObj.resource_id;
	retobj.type = kbObj.resource_type;
	retobj.comment = kbObj.comment;
	retobj.comment = {
		err_fields: [],
		err_msg: ''
	};

	if(Array.isArray(kbObj.comment)){
		retobj.comment.err_fields = kbObj.comment;
	}
	
	if(kbObj.comment.fields){
		retobj.comment.err_fields = kbObj.comment.fields;
	}

	if(retobj.comment.err_msg){
		retobj.comment.err_msg = retobj.comment.err_msg;
	}

	if(retobj.ctime)
		retobj.ctime = kbObj.ctime;

	if(retobj.utime)
		retobj.utime = kbObj.utime;

	retobj.user_id = kbObj.reporter_id;
	retobj.status = kbObj.status;

	if(kbObj.period)
		retobj.period = kbObj.period;

	if(kbObj.subject)
		retobj.subject = kbObj.subject;

	if(retobj.user_id){
		var userId = Number(retobj.user_id.match(/@0*(\d+)$/)[1]);
		retobj.user_id = isNaN(userId) ? -1 : userId;
	}

	return retobj;
}

var lst = [];
// Use connect method to connect to the Server 
mongodb.connect(kbURL, function(err, db) {
	var cursor = db.collection('resource_erratum').find();
	cursor.toArray(function(err, docs){
		_.each(docs, function(doc){
			lst.push(transfer(doc));
		});
		db.close();
		mongodb.connect(tikuURL, function(err, db) {
			Thenjs.each(lst, function(cont, value){
				var _id = value._id;
				delete value._id;
				db.collection('resource_erratum').update({
					_id: _id	
				}, {
					$set: value
				}, {
					upsert: true
				}, 
				function(err, writeResult){
					cont(null, null);
				});
			}).then(function(){
				db.close();	
			});
		});
	});
});

