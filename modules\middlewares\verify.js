/**
 * Desc: verify user is legal
 */
let crypto = require('crypto');
let config = require('config');
const pathToRegexp = require('path-to-regexp');
// const axios = require('axios');
const moment = require('moment');
// const URL = require('url');
// const _ = require('lodash');
const Logger = require('../utils/logger');
let aes = require('../utils/aes');
let jwt = require('../utils/jwt');
// const rediser = require('../utils/rediser');
let mongodber = require('../utils/mongodber');
const KBSERVER = config.get('KB_API_SERVER');
// const QX_TOKEN_SECRET_KEY = config.get('QX_TOKEN_SECRET_KEY');
// const VISIT_FREQ = config.get('VISIT_FREQ');
const VISIT_INTERFACE_LIMIT = config.get('VISIT_INTERFACE_LIMIT');
const VISIT_DEFAULT_LIMIT = config.get('VISIT_DEFAULT_LIMIT');
const VISIT_TRIAL_DEFAULT_LIMIT = config.get('VISIT_TRIAL_DEFAULT_LIMIT');
// const DEFAULT_EXPIRE_MONTH = 3;
// let userPeriod = '高中';
// let userExactSubject = '数学';

const ResponseWrapper = require('./response_wrapper');
const enums = require('../../bin/enum');
const utils = require('../utils/utils');
// const redisCache = require('../utils/redis_cache');
let user_log_service;
let user_vip_service;
let user_login_service;

//初始化 METHOD_PATHREG
let METHOD_PATHREG = {};
let db;
(function () {
    let apis = VISIT_INTERFACE_LIMIT;

    for (let k in apis) {
        if (apis.hasOwnProperty(k)) {
            let arr = apis[k].split(' ');
            let method = arr[0].toUpperCase();
            let reg = pathToRegexp(arr[1], []);

            if (METHOD_PATHREG.hasOwnProperty(method)) {
                METHOD_PATHREG[method].push({
                    'reg': reg,
                    'api_name': k,
                });
            } else {
                METHOD_PATHREG[method] = [{
                    'reg': reg,
                    'api_name': k,
                }];
            }
        }
    }
}());

// /**
//  * Desc: 解析企业微信二维码token
//  * @param {string} token
//  * @returns {Array}
//     ["吴金锋", // 姓名
//     "2302", // 部门
//     "后端开发工程师"
//     "wujinfeng"] // 企信id
//  */
// const parseQXToken = (token) => {
//     try {
//         let decipher = crypto.createDecipher('aes192', QX_TOKEN_SECRET_KEY);
//         let decrypted = decipher.update(token, 'hex', 'utf8');
//         decrypted += decipher.final('utf8');
//         let arr = decrypted.split('::');
//         let now = Math.floor(Date.now() / 1000);
//         if (arr.length === 5 && arr[0] > now) {
//             arr.shift();
//             return arr;
//         }
//         return null;
//     } catch (e) {
//         console.log('parse_jump_token error, token:' + token + ', error message:' + e);
//     }
//     return undefined;
// };

function dataToken(req, res) {
    let token = '';
    token += Number(req.query.id);
    token += Number(req.query.timestamp);
    token += config.get('SCANTRON.sk');
    let decipher = crypto.createHash('md5');
    let tk = decipher.update(token).digest('hex');

    if (tk === req.query.dtk) {
        let cookie = config.get('TIKU_SERVER').sessionIdCookie;
        let _user = {
            id: parseInt(req.query.id),
            name: req.query.name,
            schoolId: parseInt(req.query.schoolId),
            schoolName: req.query.schoolName,
            role: '教师'
        };

        // set or update tiku cookie if necessary
        let newExp = Date.now() + cookie.options.maxAge;
        _user.exp = new Date(newExp);
        let value = aes.encript(jwt.encode(_user));
        res.cookie(cookie.name, value, cookie.options);
        req.user = _user;
        if (!req.headers.cookie) {
            req.headers.cookie = [cookie.name, value].join('=');
        }
        return true;
    }

    return false;
}

const userVerify = async function (req, res, next) {
    db = mongodber.use('tiku');
    user_log_service = require('../user_api/v1/user_log_service');
    user_vip_service = require('../user_api/v1/user_vip_service');
    user_login_service = require('../user_api/v2/user_login_service');
    let responseWrapper = new ResponseWrapper(req, res);

    if (utils.isMobileByUa(req.headers['user-agent'])) {
        req.query.device = 'mobile';
    }

    if (req.query.dtk && dataToken(req, res)) {
        req.query.api_key = KBSERVER.appKey;
        return next();
    }
    let _user = null;
    if (req.query.token && req.query.source === enums.UserSource.HFS_APP_JS) {
        // 教师端APP
        const token = req.query.token;
        const user = await user_login_service.loginByHfsAppTeacher(token);
        if (!user) {
            return responseWrapper.error('AUTH_ERROR', '未登录');
        }
        user.id = user._id;
        user.source = enums.UserSource.HFS_APP_JS;
        delete req.query.token;
        delete req.query.source;
        _user = _get_req_user(user);
        _user.source = user.source;
    } else {
        const cookie = config.get('TIKU_SERVER').sessionIdCookie;
        const tikuSession = req.cookies && req.cookies[cookie.name];

        if (!tikuSession) {
            return responseWrapper.error('AUTH_ERROR', '未登录');
        }
        try {
            const user = jwt.decode(aes.decript(tikuSession));
            // user = await db.collection('user').findOne({_id: user.id});
            _user = user;
        } catch (err) {
            Logger.error(err.stack);
            return responseWrapper.error('COOKIE_INVALID', '登录已过期');
        }
    }

    let disableIds = [
        76325431,
    ]
    if (disableIds.includes(parseInt(_user && _user.id))) {
        return responseWrapper.error('ILLEGAL_USER', '账号被封禁');
    }

    if (!_user || req.query.device === 'mobile') {
        return responseWrapper.error('AUTH_ERROR', '没有权限！');
    }
    // set cookie
    req.query.api_key = KBSERVER.appKey;
    // const _user = _get_req_user(user);
    // _user.source = user.source;
    req.user = _user;
    return next();
}

function _get_req_user(user) {
    if (!user) return null;
    const result = {
        id: user._id,
        userId: user._id,
        ucId: user.uc_id,
        name: user.name,
        schoolId: user.sch_id,
        schoolName: user.sch_name,
        province: user.province,
        city: user.city,
        role: user.role,
        grade: user.grade,
        avatar: user.avatar,
        need_bind: user.need_bind,
        qxid: user.qxid,
        // isVip: false
    };
    return result;
}

// 接口访问频次检查
const visitFreqVerify = async function (req, res, next) {
    const user = req.user;
    let responseWrapper = new ResponseWrapper(req, res);

    if (utils.isMobileByUa(req.headers['user-agent'])) {
        req.query.device = 'mobile';
    }
    if (!utils.isMobileByUa(req.headers['user-agent'])) {
        let apiName = _getApiName(req);
        if (!apiName) {
            return next();
        }

        let userId = user.id;
        let userInfo = await db.collection('user').findOne({ _id: userId });

        // 用户同步
        if (!userInfo) {
            let userPeriod = '高中';
            let userExactSubject = '数学';
            let userData = {
                _id: userId,
                name: user.name,
                sch_id: user.schoolId,
                sch_name: user.schoolName,
                trace: {
                    period: userPeriod,
                    subject: userExactSubject,
                },
                curr: {
                    period: userPeriod,
                    subject: userExactSubject,
                },
                finished: 1,
                utime: new Date(),
                ctime: new Date(),
            };
            await db.collection('user').insertOne(userData);
            userInfo = userData;
            await user_log_service.register(userInfo);
        }

        // 接口访问频次初始化
        let useNum = {
            que_download_num: 0,
            exampaper_download_num: 0,
            assemble_download_num: 0,
            que_details_num: 0,
        };
        if (userInfo.use_time && userInfo.use_time > moment().startOf('month').toDate()) {
            useNum.que_download_num = userInfo.que_download_num;
            useNum.exampaper_download_num = userInfo.exampaper_download_num;
            useNum.assemble_download_num = userInfo.assemble_download_num;
            useNum.que_details_num = userInfo.que_details_num;
        } else {
            await db.collection('user').updateOne({ _id: userId }, { $set: useNum });
        }

        // 查看题目详情接口调用频次 检查以及更新
        useNum[apiName] += 1;
        if (apiName === 'que_details_num') {
            // limit = VISIT_TRIAL_DEFAULT_LIMIT.que_details_num;
            // if (Object.values(enums.PersonalVipType).includes(user.vipType)) {
            //     limit = VISIT_DEFAULT_LIMIT.que_details_num;
            // } else if (Object.values(enums.SchoolVipType).includes(user.vipType)) {
            //     let schoolInfo = await db.collection('school_info').findOne({ _id: user.schoolId });
            //     if (schoolInfo && Object.values(enums.SchoolVipType).includes(schoolInfo.vip_type) && schoolInfo.expired_time > new Date()) {
            //         limit = schoolInfo.que_details_num || 0;
            //     }
            // }
            // if (limit < useNum[apiName]) {
            //     return responseWrapper.error('EXCEED_FRQ_ERROR', '您的账号已达本月数量上限');
            // }
            // updateResult = await db.collection('user').updateOne({ _id: user.id, que_details_num: { $lt: limit } }, {
            //     $set: { use_time: new Date() },
            //     $inc: { que_details_num: 1 }
            // });
            return next();
        }

        // 非校园版用户下载接口 调用频次检查
        if (!Object.values(enums.MemberType).includes(user.vipType)) {
            let memberInfo = VISIT_TRIAL_DEFAULT_LIMIT;
            if (memberInfo[apiName] < useNum[apiName]) {
                return responseWrapper.error('EXCEED_FRQ_ERROR', '您的账号已达本月数量上限');
            }
        } else if (user.vipType === enums.MemberType.TIKU_PERSONAL || user.vipType === enums.MemberType.HFS_360 || Object.values(enums.SyMemberTypeToMemberType).includes(user.vipType)) {
            let memberInfo = VISIT_DEFAULT_LIMIT;
            if (memberInfo[apiName] < useNum[apiName]) {
                return responseWrapper.error('EXCEED_FRQ_ERROR', '您的账号已达本月数量上限');
            }
        }
    }
    return next();
}

function _getApiName(data) {
    let method = data.method.toUpperCase();
    let path = data.path;
    let pathregs = METHOD_PATHREG[method];

    for (let i in pathregs) {
        if (pathregs.hasOwnProperty(i)) {
            let desc = pathregs[i];
            let m = desc.reg.exec(path);
            if (m) {
                if (desc.api_name === 'exampaper_download_num' || desc.api_name === 'que_download_num' || desc.api_name === 'assemble_download_num') {
                    if (data.body.resource_type === 'exampaper') {
                        return 'exampaper_download_num';
                    } else if (data.body.resource_type === 'question') {
                        return 'que_download_num';
                    } else {
                        return 'assemble_download_num'
                    }
                }
                return desc.api_name;
            }
        }
    }
    return null;
};

module.exports = {
    userVerify: userVerify,
    visitFreqVerify: visitFreqVerify,
}
