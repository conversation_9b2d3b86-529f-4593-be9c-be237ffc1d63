let ResponseWrapper = require('../../middlewares/response_wrapper');
let logger = require('../../utils/logger');
const client = require('../../client');
const uuid = require('uuid');
const config = require('config');
const qs = require('querystring');
const utils = require('../../utils/utils');

module.exports = {
    get_jsapi_ticket,
    get_jsapi_config,
}


async function get_jsapi_ticket(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const ticket = await client.wechat.get_jsapi_ticket();
        return responseWrapper.succ({ticket});
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

async function get_jsapi_config(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let url = req.query.url;
        if (!url) return responseWrapper.error('PARAMETERS_ERROR', 'URL不能为空');
        const ticket = await client.wechat.get_jsapi_ticket();
        if (!ticket) return responseWrapper.error('HANDLE_ERROR', '获取签名Ticket失败');
        const result = {
            appid: config['wechat-teacher']['appid'],
            noncestr: uuid.v4().split('-').join(''),
            timestamp: Date.now().toString().slice(0, 10),
            signature: ''
        };
        const str = `jsapi_ticket=${ticket}&noncestr=${result.noncestr}&timestamp=${result.timestamp}&url=${url.split('#')[0]}`;
        const signature = utils.wxSign(str);
        result.signature = signature;
        return responseWrapper.succ(result);
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}


