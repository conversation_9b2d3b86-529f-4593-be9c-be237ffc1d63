/**
 * Desc: verify user is legal
 */
let crypto = require('crypto');
let config = require('config');
const qs = require('qs');
const Thenjs = require('thenjs');
const request = require('request');
const URL = require('url');
const _ = require('lodash');
let http = require('http');
let https = require('https');
const Logger = require('../utils/logger');
let aes = require('../utils/aes');
let jwt = require('../utils/jwt');
let mongodber = require('../utils/mongodber');
const KBSERVER = config.get('KB_API_SERVER');
const rediser = require('../utils/rediser');
const logger = require('../utils/logger');
const authValidator = require('../../modules/middlewares/auth').authValidator;
let TIMEOUT = 1000;  // ms
const QX_TOKEN_SECRET_KEY = config.get('QX_TOKEN_SECRET_KEY');
const BOSSSERV = config.get('BOSS_SERVER');
const enums = require('../../bin/enum');

const DURATION = 30 * 60;

function getUserByTiku(req, callback) {
    var cookie = config.get('TIKU_SERVER').sessionIdCookie;
    var value = req.cookies[cookie.name];
    if (!value) {
        return callback(null, null);
    }
    try {
        var user = jwt.decode(aes.decript(value));
        return callback(null, user);
    } catch (err) {
        return callback(err);
    }
}

function getUserByHFSStudent(req, callback) {
    var server = config.get('HFS_STUDENT_SERVER');
    let db = mongodber.use('tiku');
    var session = req.cookies[server.session];
    if (!session) {
        return callback(null, null);
    }

    var options = {
        hostname: server.hostname,
        port: server.port,
        method: 'GET',
        path: '/v2/user-center/sso-info',
        headers: {
            Cookie: server.session + '=' + session,
        }
    };
    var client = eval(server.protocol);
    var _req = client.request(options, function (_res) {
        _res.setEncoding('utf8');
        var chunck = '';
        _res.on('data', (data) => chunck += data);
        _res.on('end', () => {
            if (_res.statusCode == 200) {
                try {
                    var ret = JSON.parse(chunck);
                    if (ret.code != 0) {
                        var err = new Error('Verify student:' + ret.msg);
                        return callback(err);
                    }
                    var data = ret.data;
                    var roleMap = { 1: '学生', 2: '家长', 3: '教师' };
                    var user = {
                        id: data.id,
                        ucId: data.userId,
                        name: data.name,
                        grade: data.grade,
                        avatar: data.avatar,
                        role: roleMap[data.role],
                        schoolId: data.schoolId,
                        schoolName: data.school,
                    };

                    // 获取题库会员
                    let ucId = Number(data.userId);

                    db.collection('user').findOne({ uc_id: ucId, is_vip: true }, function (err, result) {
                        if (err) {
                            Logger.error(err);
                        }
                        if (result) {
                            user.isVip = result.is_vip;
                        }
                        return callback(null, user);
                    });
                } catch (err) {
                    return callback(err);
                }
            } else {
                return callback(new Error('Verify student failed'));
            }
        });
    });
    _req.on('error', function (err) {
        return callback(err);
    });
    _req.setTimeout(TIMEOUT, function () {
        _req.abort();
    });
    _req.end();
}

function getUserByHFSTeacher(req, callback) {
    let db = mongodber.use('tiku');
    let server = config.get('HFS_TEACHER_SERVER');
    let session = req.cookies[server.session];

    if (!session) {
        return callback(null, null);
    }

    let options = {
        hostname: server.hostname,
        port: server.port,
        method: 'GET',
        path: '/teacher-v2/teachers/info',
        headers: {
            Cookie: server.session + '=' + session,
        }
    };
    let client = eval(server.protocol);
    let _req = client.request(options, function (_res) {
        _res.setEncoding('utf8');
        let chunck = '';
        _res.on('data', (data) => chunck += data);
        _res.on('end', () => {
            if (_res.statusCode == 200) {
                try {
                    var ret = JSON.parse(chunck);
                    if (ret.code !== 0 && ret.code !== undefined) {
                        var err = new Error('Verify teacher:' + ret.msg);
                        return callback(err);
                    }

                    if (ret.data) {
                        ret = ret.data;
                    }
                    var data = ret;
                    var class_ = data.classes[0] || { 'grade': '' };
                    var user = {
                        id: Number(data.id),
                        name: data.name,
                        grade: class_.grade,
                        avatar: data.avatar,
                        role: '教师',
                        source: enums.UserSource.HFS,
                        schoolId: Number(data.schoolId),
                        schoolName: data.schoolName,
                    };
                    const accessSpot = {
                        attachment: [],
                        user_id: Number(data.id),
                        school_id: Number(data.schoolId),
                        province: '',
                        city: '',
                        ip: req.ip,
                        timestamp: new Date(),
                        event_id: 'land_hfsjs_times',
                    };
                    try {
                        db.collection('access_spot').insert(accessSpot);
                    } catch (err) {
                        console.log(err.stack);
                    }

                    //保存好分数教师角色信息
                    try {
                        let userId = Number(ret.id);
                        let userPeriod = ret.classes[0] ? (['高中', '初中', '小学'].indexOf(ret.classes[0].period) === -1 ? '高中' : ret.classes[0].period) : '高中';
                        let userExactSubject = ret.classes[0] ? (['数学', '语文', '英语', '物理', '化学', '生物', '政治', '历史', '地理', '科学'].indexOf(ret.classes[0].exactSubject) === -1 ? '数学' : ret.classes[0].exactSubject) : '数学';
                        db.collection('user').find({ _id: userId }).project({ trace: 1, finished: 1, sources: 1 }).toArray(function (err, arr) {
                            if (err) {
                                Logger.error(err);
                            }
                            if (arr.length > 0 && arr[0].trace && arr[0].finished === 1) {
                                if (arr[0].trace.period !== userPeriod || arr[0].trace.subject !== userExactSubject) {
                                    //hfs:表示从好分数带过来的角色信息覆盖了题库的角色, 0-已经通知用户，1-未通知用户
                                    let setData = {
                                        name: user.name,
                                        sch_id: user.schoolId,
                                        sch_name: user.schoolName,
                                        'trace.period': userPeriod,
                                        'trace.subject': userExactSubject,
                                        finished: 1,
                                        hfs: 1,
                                        'curr.period': userPeriod,
                                        'curr.subject': userExactSubject,
                                        role: user.role,
                                        sources: _.uniq((arr[0].sources || []).concat([enums.UserSource.HFS])),
                                    };
                                    // if (data.is_vip) {
                                    //     setData[`members.${enums.MemberType.HFS_360}`] = {
                                    //         source: enums.UserSource.HFS,
                                    //         type: enums.MemberType.HFS_360,
                                    //         begin_date: data.begin_date,
                                    //         end_date: data.end_date,
                                    //     }
                                    // }
                                    db.collection('user').update({ _id: userId }, { $set: setData });
                                }
                            } else {
                                let setData = {
                                    name: user.name,
                                    sch_id: user.schoolId,
                                    sch_name: user.schoolName,
                                    'trace.period': userPeriod,
                                    'trace.subject': userExactSubject,
                                    finished: 1,
                                    'curr.period': userPeriod,
                                    'curr.subject': userExactSubject,
                                    role: user.role,
                                    sources: _.uniq((arr[0].sources || []).concat([enums.UserSource.HFS])),
                                };
                                // if (data.is_vip) {
                                //     setData[`members.${enums.MemberType.HFS_360}`] = {
                                //         source: enums.UserSource.HFS,
                                //         type: enums.MemberType.HFS_360,
                                //         begin_date: data.begin_date,
                                //         end_date: data.end_date,
                                //     }
                                // }
                                db.collection('user').update({ _id: userId }, { $set: setData });
                            }
                        });
                    } catch (err) {
                        Logger.error(err.stack);
                    }

                    return callback(null, user);
                } catch (err) {
                    return callback(err);
                }
            } else {
                return callback(new Error('Verify teacher failed'));
            }
        });
    });
    _req.on('error', function (err) {
        return callback(err);
    });
    _req.setTimeout(TIMEOUT, function () {
        _req.abort();
    });
    _req.end();
}

/**
 * Desc: 解析企业微信二维码token
 * @param {string} token
 * @returns {Array}
    ["吴金锋", // 姓名
    "2302", // 部门
    "后端开发工程师"
    "wujinfeng"] // 企信id
 */
const parseQXToken = (token) => {
    try {
        let decipher = crypto.createDecipher('aes192', QX_TOKEN_SECRET_KEY);
        let decrypted = decipher.update(token, 'hex', 'utf8');
        decrypted += decipher.final('utf8');
        let arr = decrypted.split('::');
        let now = Math.floor(Date.now() / 1000);
        if (arr.length === 5 && arr[0] > now) {
            arr.shift();
            return arr;
        }
        return null;
    } catch (e) {
        console.log('parse_jump_token error, token:' + token + ', error message:' + e);
    }
    return undefined;
};

const getUserByQiXin = (req, callback) => {
    let db = mongodber.use('tiku');
    let token = req.query.token;
    let qixinUserid = req.query.userid; // 企信扫码登录会有userid字段
    if (!token) {
        return callback(null, null);
    }
    if (!qixinUserid) {
        return callback(null, null);
    }
    let qixinInfo = parseQXToken(token); //
    if (qixinInfo && qixinInfo[0]) {
        if (qixinUserid !== qixinInfo[3]) {
            return callback('企信id与token解析不对应。');
        }
        let userName = qixinInfo[0];
        let cond = { qixin_id: qixinUserid };
        let options = { fields: { _id: 1, qixin_id: 1 } };
        db.collection('user').findOne(cond, options, function (err, userInfo) {
            if (err) {
                return callback(err);
            }
            let bind = true;
            let isVip = false;
            let userId = 0;
            let userSchoolId = 1;
            let userSchoolName = '企信';
            if (userInfo) {
                bind = false;
                isVip = userInfo.is_vip;
                userId = userInfo._id;
                userSchoolId = userInfo.sch_id || 1;
                userSchoolName = userInfo.sch_name || '企信'
            }
            let user = {
                id: userId, // 默认
                isVip: isVip,
                name: userName,
                grade: '',
                avatar: '',
                role: '教师',
                schoolId: userSchoolId, // 默认假的
                schoolName: userSchoolName, // 默认假的
                need_bind: bind, // 是否需要绑定
                qxid: qixinUserid
            }

            if (userInfo) {
                const accessSpot = {
                    attachment: [],
                    user_id: Number(user.id),
                    school_id: Number(user.schoolId),
                    province: '',
                    city: '',
                    ip: req.ip,
                    timestamp: new Date(),
                    event_id: 'land_web_times',
                };
                db.collection('access_spot').insertOne(accessSpot).then();
                //设置用户当前角色信息
                try {
                    let userId = Number(user.id);
                    let userPeriod = '高中';
                    let userExactSubject = '数学';
                    db.collection('user').find({ _id: userId }).project({ trace: 1, finished: 1 }).toArray(async function (err, arr) {
                        if (err) {
                            Logger.error(err);
                            return callback(err);
                        }
                        if (!arr.length) {
                            let userData = {
                                _id: userId,
                                name: user.name,
                                sch_id: user.schoolId,
                                sch_name: user.schoolName,
                                curr: {},
                                trace: {},
                                finished: 0,
                                utime: new Date(),
                                ctime: new Date()
                            };
                            await db.collection('user').insertOne(userData);
                            arr = [userData];
                        }
                        if (arr.length > 0 && arr[0].trace && arr[0].finished === 1) {
                            let setData = {
                                name: user.name,
                                sch_id: user.schoolId,
                                sch_name: user.schoolName,
                                'curr.period': arr[0].trace.period,
                                'curr.subject': arr[0].trace.subject
                            };
                            await db.collection('user').update({ _id: userId }, { $set: setData });

                        } else {
                            let setData = {
                                name: user.name,
                                sch_id: user.schoolId,
                                sch_name: user.schoolName,
                                'trace.period': userPeriod,
                                'trace.subject': userExactSubject,
                                finished: 1,
                                'curr.period': userPeriod,
                                'curr.subject': userExactSubject
                            };
                            await db.collection('user').update({ _id: userId }, { $set: setData });
                        }
                        callback(null, user);
                    });
                } catch (err) {
                    Logger.error(err.stack);
                    return callback(null, user);
                }
            } else {
                callback(null, user);
            }
        });
    } else {
        callback(null, null);
    }
}

function getUserBySSO(req, callback) {
    let db = mongodber.use('tiku');
    let token = req.query.token;
    let loginFrom = req.query.login_from; // kb-app登录
    if (!token) {
        return callback(null, null);
    }
    let server = config.get('SSO_SERVER');
    let options = {
        hostname: server.hostname,
        port: server.port,
        method: 'POST',
        path: '/passport/v1/user/info/',
        headers: {
            'Content-Type': 'application/json',
        }
    };
    let client = eval(server.protocol);
    let _req = client.request(options, function (_res) {
        _res.setEncoding('utf8');
        let chunck = '';
        _res.on('data', (data) => chunck += data);
        _res.on('end', () => {
            if (_res.statusCode == 200) {
                try {
                    let data = JSON.parse(chunck);
                    if (data.code !== 0) {
                        return callback(new Error('verify SSO failed'));
                    }

                    data = data.data;
                    let user = {
                        id: data.id,
                        name: data.name,
                        grade: data.grade,
                        avatar: data.avatar,
                        role: data.role,
                        schoolId: Number(data.school_id),
                        schoolName: data.school,
                    };

                    // 使用kb-app登录的内部用户
                    if (loginFrom === 'kbapp' && data.school_id) {
                        db.collection('school_info').findOne({ _id: data.school_id }, function (err, result) {
                            if (err) {
                                Logger.error(err);
                                return callback(err);
                            }
                            if (result && result.type === '虚拟校') {
                                // 内部员工重定向至扫码登录界面
                                return callback('redirect');
                            }
                            const accessSpot = {
                                attachment: [],
                                user_id: Number(data.id),
                                school_id: Number(data.school_id),
                                province: '',
                                city: '',
                                ip: req.ip,
                                timestamp: new Date(),
                                event_id: 'land_web_times',
                            };
                            try {
                                db.collection('access_spot').insert(accessSpot);
                            } catch (err) {
                                console.log(err.stack);
                            }

                            //设置用户当前角色信息
                            try {
                                let userId = Number(data.id);
                                let userPeriod = '高中';
                                let userExactSubject = '数学';
                                db.collection('user').find({ _id: userId }).project({ trace: 1, finished: 1 }).toArray(async function (err, arr) {
                                    if (err) {
                                        Logger.error(err);
                                        return callback(err);
                                    }
                                    if (!arr.length) {
                                        let userData = {
                                            _id: userId,
                                            name: user.name,
                                            sch_id: user.schoolId,
                                            sch_name: user.schoolName,
                                            curr: {},
                                            trace: {},
                                            finished: 0,
                                            utime: new Date(),
                                            ctime: new Date(),
                                        };
                                        await db.collection('user').insertOne(userData);
                                        arr = [userData];
                                    }
                                    let setData = {};
                                    if (arr.length > 0 && arr[0].trace && arr[0].finished === 1) {
                                        setData = {
                                            name: user.name,
                                            sch_id: user.schoolId,
                                            sch_name: user.schoolName,
                                            'curr.period': arr[0].trace.period,
                                            'curr.subject': arr[0].trace.subject
                                        };
                                    } else {
                                        setData = {
                                            name: user.name,
                                            sch_id: user.schoolId,
                                            sch_name: user.schoolName,
                                            'trace.period': userPeriod,
                                            'trace.subject': userExactSubject,
                                            finished: 1,
                                            'curr.period': userPeriod,
                                            'curr.subject': userExactSubject
                                        };
                                    }
                                    await db.collection('user').update({ _id: userId }, { $set: setData });
                                    callback(null, user);
                                });
                            } catch (err) {
                                Logger.error(err.stack);
                                return callback(null, user);
                            }
                        });
                    } else {
                        // 题库后台管理系统只允许配置的几个用户访问
                        if (loginFrom === 'admin' && !config['ALLOW_ADMIN_LOGIN'].includes(Number(data.id))) {
                            return callback('admin');
                        }
                        const accessSpot = {
                            attachment: [],
                            user_id: Number(data.id),
                            school_id: Number(data.school_id),
                            province: '',
                            city: '',
                            ip: req.ip,
                            timestamp: new Date(),
                            event_id: 'land_web_times',
                        };
                        try {
                            db.collection('access_spot').insert(accessSpot);
                        } catch (err) {
                            console.log(err.stack);
                        }

                        //设置用户当前角色信息
                        try {
                            let userId = Number(data.id);
                            let userPeriod = '高中';
                            let userExactSubject = '数学';
                            db.collection('user').find({ _id: userId }).project({ trace: 1, finished: 1 }).toArray(async function (err, arr) {
                                if (err) {
                                    Logger.error(err);
                                    return callback(err);
                                }
                                if (!arr.length) {
                                    let userData = {
                                        _id: userId,
                                        name: user.name,
                                        sch_id: user.schoolId,
                                        sch_name: user.schoolName,
                                        curr: {},
                                        trace: {},
                                        finished: 0,
                                        utime: new Date(),
                                        ctime: new Date()
                                    };
                                    await db.collection('user').insertOne(userData);
                                    arr = [userData];
                                }
                                let setData = {};
                                if (arr.length > 0 && arr[0].trace && arr[0].finished === 1) {
                                    setData = {
                                        name: user.name,
                                        sch_id: user.schoolId,
                                        sch_name: user.schoolName,
                                        'curr.period': arr[0].trace.period,
                                        'curr.subject': arr[0].trace.subject
                                    };
                                } else {
                                    setData = {
                                        name: user.name,
                                        sch_id: user.schoolId,
                                        sch_name: user.schoolName,
                                        'trace.period': userPeriod,
                                        'trace.subject': userExactSubject,
                                        finished: 1,
                                        'curr.period': userPeriod,
                                        'curr.subject': userExactSubject
                                    };
                                }
                                await db.collection('user').update({ _id: userId }, { $set: setData });


                                callback(null, user);
                            });
                        } catch (err) {
                            Logger.error(err.stack);
                            return callback(null, user);
                        }
                    }
                } catch (err) {
                    return callback(err);
                }
            } else {
                return callback(new Error('verify SSO failed'));
            }
        });
    });
    _req.on('error', function (err) {
        return callback(err);
    });
    _req.setTimeout(TIMEOUT, function () {
        _req.abort();
    });
    let data = JSON.stringify({
        token: token,
    });
    _req.write(data);
    _req.end('\n');
}

function getUserSy(req, callback) {
    let token = req.query.token;
    let db = mongodber.use('tiku');
    if (!token) {
        return callback(null, null);
    }
    let server = config.get('SY_AUTH_SERVER');
    let options = {
        hostname: server.hostname,
        port: server.port,
        method: 'GET',
        path: '/api/platform/zsk/user?sytoken=' + token,
        headers: {
            'Content-Type': 'application/json',
        }
    };
    let client = eval(server.protocol);
    let _req = client.request(options, function (_res) {
        _res.setEncoding('utf8');
        let chunck = '';
        _res.on('data', (data) => chunck += data);
        _res.on('end', () => {
            if (_res.statusCode != 200) {
                return callback(new Error('verify SY_AUTH failed'));
            }
            try {
                let data = JSON.parse(chunck);
                if (data.code != 0) return callback(new Error(data.msg));
                data = data.data;
                let user = {
                    id: Number(data.id),
                    name: data.name,
                    grade: data.grade,
                    avatar: data.avatar,
                    role: "学生",
                    schoolId: Number(data.school_id),
                    schoolName: data.school,
                    logo: data.logo,
                    // type: enums.UserSource.SY,
                    source: enums.UserSource.SY,
                    nav_bg_color: data.nav_bg_color,
                };
                if (data.sxb_info && enums.SyMemberTypeToMemberType[data.sxb_info.role] && new Date(data.sxb_info.role_expire) > new Date()) {
                    user.isSyVip = true;
                    user.syVipType = enums.SyMemberTypeToMemberType[data.sxb_info.role];
                    user.syBeginDate = new Date(data.sxb_info.role_create_time);
                    user.syEndDate = new Date(data.sxb_info.role_expire);
                }
                const accessSpot = {
                    attachment: [],
                    user_id: Number(data.id),
                    school_id: Number(data.school_id),
                    province: '',
                    city: '',
                    ip: req.ip,
                    timestamp: new Date(),
                    event_id: 'land_web_times',
                };
                try {
                    db.collection('access_spot').insert(accessSpot);
                } catch (err) {
                    console.log(err.stack);
                }

                //设置用户当前角色信息
                try {
                    let userId = Number(data.id);
                    let userPeriod = '高中';
                    let userExactSubject = '数学';
                    db.collection('user').find({ _id: userId }).project({ trace: 1, finished: 1, sources: 1 }).toArray(async function (err, arr) {
                        if (err) {
                            Logger.error(err);
                            return callback(err);
                        }
                        if (!arr.length) {
                            let userData = {
                                _id: userId,
                                name: user.name,
                                sch_id: user.schoolId,
                                sch_name: user.schoolName,
                                curr: {},
                                trace: {},
                                finished: 0,
                                utime: new Date(),
                                ctime: new Date(),
                                role: user.role,
                                sources: [enums.UserSource.SY],
                            };
                            if (Object.values(enums.MemberType).includes(user.syVipType)) {
                                userData.members = {};
                                userData.members[`${user.syVipType}`] = {
                                    source: enums.UserSource.SY,
                                    type: user.syVipType,
                                    begin_date: new Date(data.sxb_info.role_create_time),
                                    end_date: new Date(data.sxb_info.role_create_time),
                                };
                            }
                            await db.collection('user').insertOne(userData);
                            arr = [userData];
                        }
                        if (arr.length > 0 && arr[0].trace && arr[0].finished === 1) {
                            let setData = {
                                name: user.name,
                                sch_id: user.schoolId,
                                sch_name: user.schoolName,
                                'curr.period': arr[0].trace.period,
                                'curr.subject': arr[0].trace.subject,
                                role: user.role,
                                sources: _.uniq((arr[0].sources || []).concat([enums.UserSource.SY])),
                            };
                            if (Object.values(enums.MemberType).includes(user.syVipType)) {
                                setData[`members.${user.syVipType}`] = {
                                    source: enums.UserSource.SY,
                                    type: user.syVipType,
                                    begin_date: new Date(data.sxb_info.role_create_time),
                                    end_date: new Date(data.sxb_info.role_create_time),
                                };
                            }
                            await db.collection('user').update({ _id: userId }, { $set: setData });
                        } else {
                            let setData = {
                                name: user.name,
                                sch_id: user.schoolId,
                                sch_name: user.schoolName,
                                'trace.period': userPeriod,
                                'trace.subject': userExactSubject,
                                finished: 1,
                                'curr.period': userPeriod,
                                'curr.subject': userExactSubject,
                                role: user.role,
                                sources: _.uniq((arr[0].sources || []).concat([enums.UserSource.SY])),
                            };
                            if (Object.values(enums.MemberType).includes(user.syVipType)) {
                                setData[`members.${user.syVipType}`] = {
                                    source: enums.UserSource.SY,
                                    type: user.syVipType,
                                    begin_date: new Date(data.sxb_info.role_create_time),
                                    end_date: new Date(data.sxb_info.role_create_time),
                                };
                            }
                            await db.collection('user').update({ _id: userId }, { $set: setData });
                        }
                        callback(null, user);
                    });
                } catch (err) {
                    Logger.error(err.stack);
                    return callback(null, user);
                }
            } catch (err) {
                return callback(err);
            }
        });
    });
    _req.on('error', function (err) {
        return callback(err);
    });
    _req.setTimeout(TIMEOUT, function () {
        _req.abort();
    });
    _req.end('\n');

}

function getUser(req, callback) {
    getUserByQiXin(req, function (err, user) {
        if (user) {
            return callback(null, user);
        }
        getUserBySSO(req, function (err, user) {
            if (['redirect', 'admin'].includes(err)) {
                return callback(err);
            }
            if (user) {
                return callback(null, user);
            }
            getUserByTiku(req, function (err, user) {
                if (user) {
                    return callback(null, user);
                }
                getUserByHFSStudent(req, function (err, user) {
                    if (user) {
                        return callback(null, user);
                    }
                    getUserByHFSTeacher(req, function (err, user) {
                        if (user) {
                            return callback(null, user);
                        }
                        getUserSy(req, function (err, user) {
                            return callback(null, user);
                        });
                    });
                });
            });
        });
    })
}

function dataToken(req, res) {
    let token = '';
    token += Number(req.query.id);
    token += Number(req.query.timestamp);
    token += config.get('SCANTRON.sk');
    let decipher = crypto.createHash('md5');
    let tk = decipher.update(token).digest('hex');

    if (tk === req.query.dtk) {
        let cookie = config.get('TIKU_SERVER').sessionIdCookie;
        let _user = {
            id: parseInt(req.query.id),
            name: req.query.name,
            schoolId: parseInt(req.query.schoolId),
            schoolName: req.query.schoolName,
            role: '教师'
        };

        // set or update tiku cookie if necessary
        let newExp = Date.now() + cookie.options.maxAge;
        _user.exp = new Date(newExp);
        let value = aes.encript(jwt.encode(_user));
        res.cookie(cookie.name, value, cookie.options);
        req.user = _user;
        if (!req.headers.cookie) {
            req.headers.cookie = [cookie.name, value].join('=');
        }
        return true;
    }

    return false;
}

function checkAndAddRegionToUser(user, callback) {
    if (user.province || !user.schoolId) return callback(false);
    try {
        let mongodber = require('../utils/mongodber');
        let db = mongodber.use('tiku');
        db.collection('school').findOne({ _id: user.schoolId }, function (err, school) {
            if (err || !school) return callback(false);
            user.province = school.province;
            user.city = school.city;
            callback(true);
        });
    } catch (err) {
        callback(false);
    }
}

const verify = (callback) => {
    return (req, res, next) => {
        if (req.query.dtk && dataToken(req, res)) {
            req.query.api_key = KBSERVER.appKey;
            return next();
        }
        getUser(req, function (err, user) {
            if (err || !user) {
                // 如果是重定向，则跳转至企信登录界面
                if (err === 'redirect') {
                    return res.redirect(config.get('QX_LOGIN'));
                }
                // 如果是admin，则返回code码
                if (err === 'admin') {
                    return res.json({
                        code: 6,
                        msg: '没有题库后台管理系统权限！'
                    });
                }
                return callback(req, res);
            }

            req.query.api_key = KBSERVER.appKey;
            let cookie = config.get('TIKU_SERVER').sessionIdCookie;
            let _user = {
                id: parseInt(user.id),
                ucId: user.ucId,
                isVip: user.isVip || false,
                name: user.name,
                province: user.province,
                city: user.city,
                role: user.role,
                grade: user.grade,
                avatar: user.avatar,
                need_bind: user.need_bind,
                qxid: user.qxid,
            };
            if (user.isSyVip) {
                _user.isSyVip = user.isSyVip;
                _user.syVipType = user.syVipType;
                _user.syBeginDate = user.syBeginDate;
                _user.syEndDate = user.syEndDate;
            }
            if (user.schoolId) {
                _user.schoolId = parseInt(user.schoolId);
                _user.schoolName = user.schoolName;
            }
            if (user.logo) _user.logo = user.logo;
            if (user.type) _user.type = user.type;
            if (user.nav_bg_color) _user.nav_bg_color = user.nav_bg_color;
            getYjSchoolInfo(user.schoolId, function (err, result) {
                if (err) {
                    Logger.error(err);
                }
                const schoolInfo = result && result.appUsages && result.appUsages[0];
                // 增加阅卷会员判断
                if (user.role === '教师') {
                    if (schoolInfo && schoolInfo.status === enums.YjSchoolStatus.使用中 && (schoolInfo.type === enums.YjSchoolVersionType.专业版 || schoolInfo.type === enums.YjSchoolVersionType.旗舰版)) {
                        _user.isYjVip = true;
                        _user.yjVipType = enums.YjSchoolVersionTypeToMemberType[schoolInfo.type];
                        _user.yjBeginDate = schoolInfo.beginDate;
                        _user.yjEndDate = schoolInfo.endDate;
                    } else
                        _user.isYjVip = false;
                }

                checkAndAddRegionToUser(_user, function (isNeedUpdateCookie) {
                    // set or update tiku cookie if necessary
                    let newExp = Date.now() + cookie.options.maxAge;
                    let preExp = user.exp;
                    if (!preExp || newExp - new Date(preExp) > cookie.interval || isNeedUpdateCookie) {
                        _user.exp = new Date(newExp);
                        let value = aes.encript(jwt.encode(_user));
                        res.cookie(cookie.name, value, cookie.options);
                        res.cookie(config.get('TIKU_SERVER').userInfo.name, JSON.stringify(_user), cookie.options);
                    }

                    // set user information
                    req.user = _user;
                    //用户访问频次异常
                    authValidator(req, (msg) => {
                        if (msg !== '') {
                            req.query.freq = 'true';
                            res.freq = msg;
                            logger.error(`authValidator: user[${req.user.id}] ${msg}`);
                        }
                        return next();
                    });
                });
            });
        });
    };
};

function getYjSchoolInfo(schoolId, callback) {
    if (!schoolId) return callback(null, null);
    const rediser_key = gen_rediser_key(schoolId);
    let YjSchoolInfo;

    Thenjs(function (cont) {
        rediser.get(rediser_key, (err, result) => {
            if (err) return callback(err, null);
            YjSchoolInfo = result;

            if (YjSchoolInfo) return callback(null, YjSchoolInfo);
            cont(null, null);
        });
    }).then((cont, arg) => {
        let bossUrl = URL.format({
            protocol: BOSSSERV.protocol,
            hostname: BOSSSERV.hostname,
            pathname: `/external/api/customer/get_app_usage_by_school_id`,
            port: BOSSSERV.port,
            search: qs.stringify({
                schoolId: schoolId
            })
        });
        request.get({
            url: bossUrl,
            timeout: 2000,
            headers: {
                'apikey': BOSSSERV.apikey
            }
        }, function (error, response, _body) {
            if (error || !response.hasOwnProperty('statusCode') || response.statusCode >= 500) {
                return callback('HANDLE_ERROR');
            }
            let body = JSON.parse(_body);
            if (body.code !== 1) {
                Logger.error(body);
                return callback('get yj school err', null);
            }
            YjSchoolInfo = body.data;
            cont(null, null);
        });
    }).then((cont, arg) => {
        if (YjSchoolInfo) {
            rediser.set(rediser_key, YjSchoolInfo, DURATION, (err, result) => {
                if (err) return callback(err, null);
                cont(null, null);
            });
        } else {
            cont(null, null);
        }
    }).fin(function (cont, err, arg) {
        return callback(err, YjSchoolInfo);
    });
}

function gen_rediser_key(school_id) {
    return 'yj_school_info:' + school_id;
}

module.exports = verify;
