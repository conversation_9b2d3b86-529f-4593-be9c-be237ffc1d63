let router = require('express').Router();
let albumApi = require('../../modules/album_api/v1/album');

// router.post('/album', albumApi.createAlbum);
// router.put('/album/:album_id', albumApi.editAlbum);
// router.delete('/album/:album_id', albumApi.removeAlbum);
router.get('/album/list', albumApi.getAlbumList);
router.get('/album/elite/list', albumApi.getAlbumEliteList);
router.get('/album/type/list', albumApi.getAlbumType);
router.get('/album/:album_id', albumApi.getAlbumDetail);

// router.post('/album/:album_id/collections', albumApi.collectAlbum);
// router.delete('/album/:album_id/collections', albumApi.delCollectAlbum);
// router.get('/album/collections/list', albumApi.getAlbumCollectList);
// router.get('/album/collections/keys', albumApi.getCollectAlbumId);

module.exports = router;