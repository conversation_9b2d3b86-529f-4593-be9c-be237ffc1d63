
const _ = require('lodash');
const ObjectID = require("mongodb").ObjectID;
const mongodber = require('../../utils/mongodber');
const db = mongodber.use('tiku');
const db_open = mongodber.use('tiku_open');
const ResponseWrapper = require('../../middlewares/response_wrapper');
const logger = require('../../utils/logger');
const enums = require('../../../bin/enum');
const Joi = require('@hapi/joi');
const client = require('../../client');
const constants = require('../../utils/constants');
const template = require('./../../assemble_api/v1/template');
const TYPES = template.TYPES;
const DIGIT_MAP_CHINESE = template.DIGIT_MAP_CHINESE;
const subject_utils = require('../../utils/subject_utils');
const config = require('config');
const exam_utils = require('../exam_utils');
const utils = require('../../utils/utils');
//
const collection_exam = db.collection(constants.schema.exam_teacher_paper);
// 用户组卷表
const collection_exampaper= db_open.collection(enums.OpenSchema.user_paper);

const collection_favorite = db.collection(constants.schema.favorite);

module.exports ={
    getList,
    getExamDetail,
    getClassDetail,
    createPaper,
    createTable,
}

/**
 * 获取教师考试列表
 * @param req
 * @param res
 * @returns {Promise<void>}
 */
async function getList(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const user_id = req.user.id;
        const hfsExamList = await client.hfsTeacherV3.getExamList(user_id);
        if (!_.size(hfsExamList)) return responseWrapper.succ([]);
        return responseWrapper.succ(hfsExamList);
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const JOI_GET_EXAM_DETAIL = Joi.object({
    exam_id: Joi.string().required(),
    paper_id: Joi.string().required(),
}).unknown(true);

/**
 * 获取考试详细
 * @param req
 * @param res
 * @returns {Promise<*>}
 */
async function getExamDetail(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const { exam_id, paper_id } = await JOI_GET_EXAM_DETAIL.validate(req.query);
        const user_id = req.user.id;

        const result = {
            original_question_count: 0,
            papers: [],
            table: {},
            table_id: null,
        };
        const paper_sign = paper_id.split('-')[0];
        const yp_data = await client.yuanpei.getPaperQuestions(paper_id);
        if (_.isEmpty(yp_data)) return responseWrapper.succ(result);
        const yp_questions = _.get(yp_data, 'questions', []);
        result.original_question_count = _.size(yp_questions);
        // 平行组卷
        let exam = await collection_exam.findOne({user_id: user_id, exam_id: exam_id, paper_id: paper_id});
        if (!_.isEmpty(exam)) {
            const ids = (exam.papers || []).map(e => e.id);
            if (_.size(ids)) {
                const fields = {_id: 1, name: 1, period: 1, subject: 1, type: 1, exampaper_download_num: 1, volumes: 1, category: 1, ctime: 1};
                const exampapers = await collection_exampaper.find({_id: { $in: ids.map(e => new ObjectID(e))}}).project(fields).toArray();
                const favorite = await collection_favorite.findOne({user_id: user_id});
                for (const paper of exam.papers) {
                    const exampaper = (exampapers || []).find(e => e._id.toString() === paper.id);
                    if (_.isEmpty(exampaper)) continue;
                    result.papers.push(_get_paper_result_data(exampaper, favorite));
                }
            }
            result.table_id = exam.table_id;
        } else {
            exam = await exam_utils.initExam(user_id, exam_id, paper_id, yp_data);
        }
        if (!_.size(exam.questions)) {
            exam_utils.buildPaperRecomendQuestions(exam, yp_data);
        }
        // 细目表
        const yp_paper = _.get(yp_data, 'paper', null);
        const period = _.get(yp_paper, 'period', '');
        const grade = _.get(yp_paper, 'grade', '');
        const subject = subject_utils.regularSubject(_.get(yp_paper, 'subject', ''));
        const tableIds = _.get(config.get('englishTwList'), period, []);
        if (subject === enums.Subject.ENGLISH && _.size(tableIds)) {
            const arr = Array.from(paper_sign);
            const index = +(arr[arr.length -1]);
            const table_paper_id = tableIds[index];
            const kb_table = await client.kb.getTableByRelevanceId(table_paper_id);

            const table = {
                name: '',
                period: period,
                grade: grade,
                subject: subject,
                blocks: _.get(kb_table, 'blocks', [])
            };
            for (const block of table.blocks) {
                for (const q of block.questions || []) {
                    q.difficulty = '不限';
                    q.knowledges = q.knowledges.length <= 3 ? q.knowledges : q.knowledges.slice(0, 3);
                }
            }
            result.table = table;
        } else {
            result.table = await _transform_to_table(user_id, exam_id, paper_id, yp_data);
        }

        return responseWrapper.succ(result);
    } catch (e) {
        logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const JOI_GET_CLASS_DETAIL = Joi.object({
    exam_id: Joi.string().required(),
    paper_id: Joi.string().required(),
    class_id: Joi.string().required(),
}).unknown(true);

async function getClassDetail(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const { exam_id, paper_id, class_id } = await JOI_GET_CLASS_DETAIL.validate(req.query);
        const user_id = req.user.id;
        const hfs_class_paper_info = await client.hfsTeacherV3.getClassPaperStatistics(user_id, exam_id, paper_id, class_id);
        if (_.isEmpty(hfs_class_paper_info)) return responseWrapper.error('HANDLE_ERROR', '班级信息不存在');

        const hfs_questions = await client.hfsTeacherV3.getPaperQuestions(user_id, exam_id, paper_id, class_id);
        if (!_.size(hfs_questions)) return responseWrapper.error('HANDLE_ERROR', '班级考试信息不存在');
        //
        const result = {
            grade_score: {
                avg: _.get(hfs_class_paper_info, 'grade.avg', -1), // 年级平均分
                excellent_rate: _.get(hfs_class_paper_info, 'grade.excellentRate', -1), // 优秀率
                class_count: _.get(hfs_class_paper_info, 'grade.classCount', -1), // 班级数量
            },
            class_score: {
                avg: _.get(hfs_class_paper_info, 'myClass.avg', -1), // 班级平均分
                excellent_rate: _.get(hfs_class_paper_info, 'myClass.excellentRate', -1), // 优秀率
                avg_rank: _.get(hfs_class_paper_info, 'myClass.avgRank', -1), // 平均分年级排名
            },
            knowledges: [], // 知识点
            paper: null, // 当前薄弱知识点组卷
            table: null, // 细目表
            table_id: null, // 个人细目表
        };
        // 元培数据
        const paper_sign = paper_id.split('-')[0];
        const yp_paper_info = await client.yuanpei.getPaperQuestions(paper_sign);
        if (_.isEmpty(yp_paper_info))  return responseWrapper.succ(result);
        // 平行组卷
        const exam = await collection_exam.findOne({user_id: user_id, exam_id: exam_id, paper_id: paper_id});
        if (!_.isEmpty(exam)) {
            const class_paper = (exam.class_papers || []).find(e => e.class_id === class_id);
            if (!_.isEmpty(class_paper)) {
                if (class_paper.id) {
                    const exampaper = await collection_exampaper.findOne({_id: new ObjectID(class_paper.id)});
                    const favorite = await collection_favorite.findOne({user_id: user_id});
                    result.paper = _get_paper_result_data(exampaper, favorite);
                }
                result.table_id = class_paper.table_id;
            }
        }

        const { knowledges, table } = await _get_class_knowledges(paper_sign, hfs_questions, yp_paper_info);
        result.knowledges = knowledges; // 薄弱知识点
        result.table = table; // 薄弱知识点细目表
        return responseWrapper.succ(result);
    } catch (e) {
        logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const JOI_CREATE_PAPER = Joi.object({
    exam_id: Joi.string().required(),
    exam_name: Joi.string().required(),
    paper_id: Joi.string().required(),
    class_id: Joi.string().optional(),
    class_name: Joi.string().optional(),
    category: Joi.array().items(Joi.number()).required().min(1),
    table: Joi.object({ // 细目表
        name: Joi.string().optional(), // 名称
        period: Joi.string().required(), // 学段
        grade: Joi.string().optional(), // 年级
        subject: Joi.string().required(), // 学科
        blocks: Joi.array().items(Joi.object({
            name: Joi.string().required(),
            type: Joi.string().required(),
            questions: Joi.array().items(Joi.object({
                type: Joi.string().required(),
                difficulty: Joi.string().required(),
                score: Joi.number().required(),
                period: Joi.string().required(), // 学段
                subject: Joi.string().required(), // 学科
                knowledges: Joi.array().items(Joi.object({
                    id: Joi.number().required(), // 知识点ID
                    name: Joi.string().required(), // 知识点名称
                })).required().min(1)
            })).required().min(1)
        })).required().min(1)
    }).required().unknown(true)
}).unknown(true);

async function createPaper(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const { exam_id, paper_id, class_id, class_name, category, table } = await JOI_CREATE_PAPER.validate(req.body);
        if (category.includes(4) && !class_id) throw new Error('薄弱知识点组卷，班级信息不能为空!');
        const user = req.user;
        const user_id = user.id;
        const now = new Date();
        const result = [];
        let user_exam = await collection_exam.findOne({user_id: user_id, exam_id: exam_id, paper_id: paper_id});

        if (!category.includes(4)) {
            // 1-基础，2-巩固，3-拔高
            await exam_utils.buildExamPapers(req.user, user_exam);
            for (const cat of category) {
                if (cat === 4) continue;
                const paper = user_exam.papers.find(e => e.category === cat);
                if (_.isEmpty(paper)) continue;
                result.push(paper);
            }
        } else {
            // 4-薄弱
            const cat = 4;
            const cat_table = _.assign({}, table);
            const params = exam_utils.build_detail_paper_params(req.user, user_exam, cat, cat_table, req.body);
            const algoPaper = await client.algo.detailTablePaper(params);
            const user_exampaper = await _transform_to_user_exampaper(user, cat, req.body, algoPaper);
            let paper = user_exam.class_papers.find(e => e.class_id === class_id);
            if (_.isEmpty(paper)) {

                paper = {
                    class_id: class_id,
                    id: user_exampaper.id,
                    type: user_exampaper.type,
                    category: cat, // 分类：1-基础，2-巩固，3-拔高，4-薄弱
                    time: now,
                };
                user_exam.class_papers.push(paper);
            } else {
                paper.id = user_exampaper.id;
                paper.category = cat;
                paper.type = user_exampaper.type;
                paper.time = now;
            }
            const update_data = {
                class_papers: user_exam.class_papers,
                utime: new Date()
            }
            await collection_exam.updateOne({ _id: user_exam._id }, { $set: update_data });
            paper.question_count = _get_paper_question_count(user_exampaper);
            result.push(paper);
        }


        // 返回题量
        if (_.size(result)) {
            const exampapers = await collection_exampaper.find({_id: {$in: result.map(e => new ObjectID(e.id))}}).toArray();
            for (const p of result) {
                if (p.question_count) continue;
                const paper = exampapers.find(e => e._id.toString() === p.id);
                p.question_count = _get_paper_question_count(paper);
            }
        }
        return responseWrapper.succ(result);
    } catch (e) {
        logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const JOI_CREATE_PAPER_TABLE = Joi.object({
    exam_id: Joi.number().required(),
    paper_id: Joi.string().required(),
    class_id: Joi.string().optional(),
    type: Joi.number().required(),
    table: Joi.object({ // 细目表
        id: Joi.string().optional(),
        name: Joi.string().required(), // 名称
        period: Joi.string().required(), // 学段
        grade: Joi.string().optional(), // 年级
        subject: Joi.string().required(), // 学科
        blocks: Joi.array().items(Joi.object({
            name: Joi.string().required(),
            type: Joi.string().optional(),
            questions: Joi.array().items(Joi.object({
                type: Joi.string().required(),
                difficulty: Joi.string().required(),
                score: Joi.number().required(),
                period: Joi.string().required(), // 学段
                subject: Joi.string().required(), // 学科
                knowledges: Joi.array().items(Joi.object({
                    id: Joi.number().required(), // 知识点ID
                    name: Joi.string().required(), // 知识点名称
                })).required().min(1)
            })).required().min(1)
        })).required().min(1)
    }).required().unknown(true)
}).unknown(true);

async function createTable(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const { id, exam_id, paper_id, class_id, type, table } = await JOI_CREATE_PAPER_TABLE.validate(req.body);
        const user = req.user;
        const user_id = user.id;
        const now = new Date();
        table.permission = 'private';
        table.relevance_type = null;
        table.relevance_id = null;
        //
        let difficulty = '不限';
        if (type === 2) {
            difficulty = '容易';
        }
        for (const b of table.blocks) {
            for (const q of b.questions) {
                q.difficulty = difficulty;
                q.period = table.period;
                q.subject = table.subject;
            }
        }
        let table_id = null;
        if (id) { // 修改
            await client.kb.updateTable(id, table, user_id);
            table_id = id;
        } else { // 新增
            const kb_table_result = await client.kb.createTable(table, user_id);
            table_id = kb_table_result.id;
            // 保存细目表
            await db_open.collection(enums.OpenSchema.tw_specification).insertOne({
                user_id: utils.formatUserId(user_id),
                table_id: table_id,
                ctime: now,
                period: table.period,
                subject: table.subject,
                type: '考后巩固',
                grade: table.grade,
                province: table.province
            });
        }
        let user_exam = await collection_exam.findOne({user_id: user_id, exam_id: exam_id, paper_id: paper_id});
        if (_.isEmpty(user_exam)) {
            user_exam = {
                user_id: user_id,
                exam_id: exam_id,
                paper_id: paper_id,
                grade: table.grade, // 年级
                period: table.period, // 学段
                subject: table.subject, // 科目
                papers: [],
                class_papers: [],
                ctime: now,
                utime: now,
            };
            const insert_result = await collection_exam.insertOne(user_exam);
            user_exam._id = insert_result.insertedId;
        }

        // 保存
        const update_data = {
            utime: now,
        }
        if (type === 1) {
            update_data.table_id = table_id;
        } else {
            const paper = user_exam.class_papers.find(e => e.class_id === class_id);
            if (_.isEmpty(paper)) {
                user_exam.class_papers.push({
                    class_id: class_id
                });
            } else {
                paper.table_id = table_id;
            }
            update_data.class_papers = user_exam.class_papers;
        }
        await collection_exam.updateOne({ _id: user_exam._id }, { $set: update_data });

        return responseWrapper.succ({table_id});
    } catch (e) {
        logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

/**
 *
 * @param user 用户信息
 * @param category 类型
 * @param body 请求参数
 * @param algoPaper 算法组卷信息
 * @returns {Promise<{template: string, period: string, subject: string, attentions: null, volumes: [{note: string, blocks: [], title: string}, {note: string, blocks: [], title: string}], secret_tag: null, duration: null, score: null, gutter: null, subtitle: null, name: null, _id: *, paper_info: null, cand_info: null}>}
 * @private
 */
async function _transform_to_user_exampaper(user, category, body, algoPaper) {
    const { table } = body;
    const { grade } = table;
    const { type, period, subject, blocks } = algoPaper;

    const date = new Date();
    const basket = _init_basket(user.id);
    delete basket['_id'];
    basket.type = '考后巩固';
    basket.category = category;
    // basket.display = 0; // 默认不展示
    basket.ctime = date;
    basket.utime = date;
    basket.user_id = utils.formatUserId(user.id);
    basket.sch_id = user.schoolId;
    basket.sch_name = user.schoolName;
    basket.period = period;
    basket.subject = subject;
    basket.grade = grade;
    basket.partsList = ["name", "subtitle", "paper_info", "gutter", "attentions", "volumes", "blocks", "secret_tag"];
    const ids = [];
    for (const b of blocks) {
        for (const q of b.questions) {
            ids.push(q.id);
        }
    }
    const kb_questions = await client.kb.getQuestions(ids);
    const ques_map = {};
    for (const q of kb_questions) {
        ques_map[q.id] = q;
    }
    for (const q of kb_questions) {
        if (_.isEmpty(q)) continue;
        const simple_question =  _.pick(q, ['id', 'type', 'period', 'subject']);
        _insert_questions(basket, simple_question);
    }
    _render_basket(basket);
    // 平行试卷名称：考试名称+平行基础卷/巩固卷/拔高卷
    let name = `${body.exam_name}${category_name[category]}`;
    if (category === 4) {
        // 薄弱点巩固卷：考试名称+班级名称+薄弱点巩固卷
        let class_name = body.class_name || '';
        if (class_name) {
            class_name = body.class_name.includes('班') ? body.class_name : body.class_name + '班';
        }
        name = `${body.exam_name}${class_name}${category_name[category]}`;
    }
    basket.name = name;
    basket.source_type = enums.PaperSourceType.EXAM;
    basket.source = enums.PaperSourceType.EXAM;
    basket.valid = enums.BooleanNumber.YES;
    basket.status = enums.PaperStatus.DONE;
    basket.exam_status = enums.ExamStatus.EDITABLE;
    const insert_result = await collection_exampaper.insertOne(basket);
    basket.id = insert_result.insertedId.toString();
    delete basket._id;
    _traverse_questions(basket, ques_map);
    basket.ctime = basket.ctime.getTime();
    basket.utime = basket.utime.getTime();
    return basket;
}

const category_name = {
    1: '简单卷',
    2: '中等卷',
    3: '培优卷',
    4: '薄弱点巩固卷',
    5: '共性卷',
}



function _render_basket(basket) {
    // 试题篮分卷信息
    basket.score = 0;
    let block_num = 0;
    const volumes = basket.volumes;
    for (const v in volumes) {
        const volume = volumes[v];
        const blocks = volume.blocks;
        for (const b in blocks) {
            const block = blocks[b];
            const questions = block.questions;
            const n = questions.length;
            if (questions.length <= 0) {
                continue;
            }
            let s = Number(questions[0].score);
            let ts = Number(s);
            let tag = true;
            for (let i = 1; i < n; ++i) {
                if (questions[i].score !== questions[i - 1].score) {
                    tag = false;
                }
                ts += Number(questions[i].score);
            }
            const order = `${DIGIT_MAP_CHINESE[++block_num]}`;
            const ss = tag ? `，每题${s}分` : '';
            block.default_score = tag ? s : block.default_score;
            const detail = `本大题共计${n}小题${ss}，共计${ts}分`;
            const note = (block.note.length > 0) ? `，${block.note}` : '';
            block.title = `${order}、${block.type}（${detail}${note}）`;
            // block.title = `${block.type}（${detail}${note}）`;
            basket.score += Number(ts);
        }
    }
    // 试卷名称， 2017年5月3日
    // var t = new Date();
    // var info = `${t.getFullYear()}年${t.getMonth() + 1}月${t.getDate()}日${basket.period}${basket.subject}`;
    basket.name = basket.name || genExamPaperName(basket);
    // 副标题
    basket.subtitle = basket.subtitle || '';
    // 考试时间，单位分钟
    basket.duration = basket.duration || 120;
    // 试卷信息栏，考试总分：100分；考试时间：100分钟
    const paper_info = `考试总分：${basket.score}   考试时间：${basket.duration}`;
    basket.paper_info = basket.paper_info || paper_info;
    // 候选人信息栏
    const line = '__________ ';
    const cand_info = `学校：${line}班级：${line}姓名：${line}考号：${line}`;

    basket.cand_info = basket.cand_info || cand_info;
    // 注意事项
    basket.attentions = basket.attentions || '注意事项：<br>1．答题前填写好自己的姓名、班级、考号等信息; <br>2．请将答案正确填写在答题卡上;<br>';
    // 保密标记文字
    basket.secret_tag = basket.secret_tag || '绝密★启用前';
    // 装订线
    basket.gutter = basket.gutter || 0;
    return basket;
}


function genExamPaperName(basket) {
    const nowDate = new Date();
    const year = nowDate.getFullYear();

    // 学年
    const academicYearStartDate = new Date();
    academicYearStartDate.setMonth(0, 1);
    academicYearStartDate.setHours(0, 0, 0, 0);

    const academicYearEndDate = new Date();
    academicYearEndDate.setMonth(7, 15);
    academicYearEndDate.setHours(0, 0, 0, 0);
    let academicYearLong = `${year.toString()}-${(year + 1).toString()}`;
    if (nowDate.getTime() >= academicYearStartDate.getTime() && nowDate.getTime() < academicYearEndDate.getTime()) {
        academicYearLong = `${(year - 1).toString()}-${year.toString()}`;
    }
    // 学期
    const semesterStartDate = new Date();
    semesterStartDate.setMonth(1, 15);
    semesterStartDate.setHours(0, 0, 0, 0);

    const semesterEndDate = new Date();
    semesterEndDate.setMonth(7, 15);
    semesterEndDate.setHours(0, 0, 0, 0);

    let semester = "上";//学期
    if (nowDate.getTime() >= semesterStartDate.getTime() && nowDate.getTime() < semesterEndDate.getTime()) {
        semester = '下';
    }
    const formatDateStr = `${nowDate.getFullYear()}年${nowDate.getMonth()+1}月${nowDate.getDate()}日`;
    let info = `${academicYearLong}学年${basket.period}${basket.subject} (${semester}) ${basket.type || ''}试卷(${formatDateStr})`;
    return info;
}

function _init_basket(user_id) {
    let basket = {
        _id: user_id,     // 用户唯一标记, app + user_id

        period: '',       // 学段
        subject: '',      // 学科

        name: null,         // 试卷名称
        subtitle: null,     // 副标题
        score: null,        // 试卷总分
        duration: null,     // 考试时间，单位分钟
        paper_info: null,   // 试卷信息栏，
        cand_info: null,
        attentions: null,   // 注意事项
        secret_tag: null,   // 保密标记文字
        gutter: null,       // 装订线
        template: 'standard',   // 组卷类型，stardard, exam, homework

        volumes: [{
            title: '卷I（选择题）',  // 卷I分卷名
            //note: '请点击修改第I卷的文字说明', // 分卷说明
            note: '', // 分卷说明
            blocks: [],
        }, {
            title: '卷II（非选择题）', // 卷II分卷名
            //note: '请点击修改第II卷的文字说明', // 分卷说明
            note: '', // 分卷说明
            blocks: [],
        }]
    }
    return basket;
}

/**
 * 插入试题篮
 * @param basket
 * @param ques
 * @returns {*}
 * @private
 */
function _insert_questions(basket, ques) {
    const type = ques['type'];
    const type_t = TYPES[type] || TYPES['default'];

    const vol_pos = type_t['vol_pos'];
    const volume = basket.volumes[vol_pos];

    basket.period = ques['period'] || '';
    basket.subject = ques['subject'] || '';
    if (ques['exampaper_type'] && ques['exampaper_type'] !== enums.ExamPaperOtherType) {
        basket.type = ques['exampaper_type'];
    }

    const ques_ = {
        id: ques['id'],
        period: ques['period'],
        subject: ques['subject'],
        type: ques['type']
    }

    for (const b in volume.blocks) {
        const block = volume.blocks[b];
        if (block.type !== type) {
            continue
        }
        // deduplicated
        const questions = block.questions;
        for (const q in questions) {
            if (questions[q]['id'] === ques['id']) {
                return basket;
            }
        }
        ques_['score'] = ques['score'] ? ques['score'] : block['default_score'];
        block.questions.push(ques_);
        return basket;
    }
    // init a new block and push the question
    ques_['score'] = ques['score'] ? ques['score'] : type_t['default_score'];
    const block = {
        title: '',
        note: '',
        type: type,
        default_score: type_t['default_score'],
        questions: [ques_],
    };
    // find the proper postion to insert the block
    const blk_pos = type_t['blk_pos'];
    for (const i in volume.blocks) {
        const type_t_ = TYPES[volume.blocks[i].type] || TYPES['default'];
        const pos = type_t_['blk_pos'];
        if (pos > blk_pos) {
            volume.blocks.splice(parseInt(i), 0, block);
            return basket;
        }
    }
    // not find proper postion, then insert to the last
    volume.blocks.push(block);
    return basket;
}

function _traverse_questions(basket, ques_map) {
    const fileds = ['id', 'elite', 'subject', 'period', 'description', 'comment', 'blocks', 'knowledges', 'difficulty', 'type', 'score', 'refer_exampapers', 'year', 'ctime', 'utime'];
    for (const volume of basket.volumes || []) {
        for (const block of volume.blocks || []) {
            for (const i in block.questions) {
                const question = block.questions[i];
                const q = ques_map[question.id];
                if (!q) {
                    delete block.questions[i];
                    continue;
                }
                block.questions[i] = _.pick(q, fileds);
                block.questions[i]['score'] = question['score'];
            }
        }
    }
}

function _build_create_paper_params(user, category, table, body) {
    const default_difficulty = category_difficulty[category];
    const param_table = _.assign({}, table);
    const blocks = [];
    for (const b of param_table.blocks) {
        const block = {
            type: b.questions[0].type,
            questions: b.questions.map(e => {
                const type_t = TYPES[e.type] || TYPES['default'];
                const obj = {
                    type: e.type,
                    score: e.score || type_t.default_score,
                    knowledges: e.knowledges,
                    difficulty: category === 4 ? e.difficulty : default_difficulty
                };
                if (category === 4 || obj.difficulty === '不限') {
                    obj.difficulty = '中等';
                }
                return obj;
            })
        }
        blocks.push(block);
    }
    const params = {
        period: body.table.period,               // 必选，学段
        subject: body.table.subject,              // 必选，科目
        type: '平行组卷',                 // 必填，平行组卷
        school_id: user.schoolId,            // 选填，学校ID
        user_id: user.id.toString(),              // 选填，用户ID
        paper_id: body.paper_id,             // 选填，试卷ID
        class_name: body.class_name || '',           // 选填，班级名
        grade: body.table.grade,                // 选填，年级
        // min_year: int,             // 选填，最小试题年份限制
        blocks,
    }
    return params;
}

const category_difficulty = {
    1: '容易',
    2: '中等',
    3: '困难',
    4: '容易',
}

/**
 * 获取班级知识点列表
 * @param paper_sign 科目ID
 * @param hfs_questions 试题列表
 * @param yp_paper_info 元培试卷信息
 * @private
 */
async function _get_class_knowledges(paper_sign, hfs_questions, yp_paper_info) {
    const subject = subject_utils.regularSubject(_.get(yp_paper_info, 'paper.subject'));
    const period = _.get(yp_paper_info, 'paper.period', '');
    const grade = _.get(yp_paper_info, 'paper.grade', '');
    const yp_questions = _.get(yp_paper_info, 'questions', []);
    let knowledges = [];
    for (const hfs_question of hfs_questions) {
        const question_knowledges = hfs_question.knowledges;
        const yp_question = yp_questions.find(e => {
           const point = e.points.find(it => parseInt(it.id.split('-')[1]) === hfs_question.questionId);
           if (!_.isEmpty(point)) return true;
           return false;
        });
        const type = _get_hfs_question_type(subject, hfs_question, yp_question);
        if (!_.size(question_knowledges)) continue;
        for (const q_k of question_knowledges) {
            let k = knowledges.find(e => e.id === q_k.id);
            if (_.isEmpty(k)) {
                k = {
                    id: q_k.id,
                    name: q_k.name,
                    classAvgScore: 0,
                    gradeAvgScore: 0,
                    score: 0,
                    count: 0,
                    question_detail: []
                };
                knowledges.push(k);
            }
            k.classAvgScore = _.add(k.classAvgScore, hfs_question.avgScore);
            k.gradeAvgScore = _.add(k.gradeAvgScore, hfs_question.gradeScore);
            k.score = _.add(k.score, hfs_question.manfen);
            k.count += 1;

            const y_q = k.question_detail.find(e => e.type === type);
            if (_.isEmpty(y_q)) {
                k.question_detail.push({
                    id: yp_question.id,
                    classScoreRate: hfs_question.classScoreRate,
                    type
                });
            } else {
                if (hfs_question.classScoreRate < y_q.classScoreRate) {
                    y_q.classScoreRate = hfs_question.classScoreRate;
                }
            }
        }
    }
    if (!_.size(knowledges)) return {};
    // 计算差值
    for (const k of knowledges) {
        const classScoreRate = _.round(k.classAvgScore / k.score, 4);
        const gradeScoreRate = _.round(k.gradeAvgScore / k.score, 4);
        k.diff = _.round(classScoreRate - gradeScoreRate, 4);
        k.classScoreRate = classScoreRate;
        k.gradeScoreRate = gradeScoreRate;
    }

    if (_.size(knowledges)) {
        knowledges = knowledges.sort( (a, b) => {
            if (a.diff > b.diff) {
                return 1;
            } else {
                return -1;
            }
        });
    }
    // 细目表
    let table = null;
    // 英语特殊处理
    const tableIds = _.get(config.get('englishTwList'), period, []);
    if (subject === enums.Subject.ENGLISH && _.size(tableIds)) {
        const arr = Array.from(paper_sign);
        const index = +(arr[arr.length -1]);
        const table_paper_id = tableIds[index];
        const kb_table = await client.kb.getTableByRelevanceId(table_paper_id);
        table = {
            name: '',
            period: period,
            grade: grade,
            subject: subject,
            blocks: _.get(kb_table, 'blocks', [])
        };
        for (const block of table.blocks) {
            for (const q of block.questions || []) {
                q.difficulty = '容易';
                q.knowledges = q.knowledges.length <= 3 ? q.knowledges : q.knowledges.slice(0, 3);
            }
        }
    } else {
        table = _get_class_knowledges_table(knowledges, yp_paper_info);
    }
    // 清理数据
    for (const k of knowledges) {
        delete k.classAvgScore;
        delete k.gradeAvgScore;
        delete k.count;
        delete k.question_detail;
    }
    // 返回结果
    return { knowledges, table };
}

/**
 * 根据班级薄弱知识点生成细目表
 * @param knowledges
 * @param yp_paper_info
 * @returns {{period: undefined, subject: *, blocks: *, grade: undefined, name: string}}
 * @private
 */
function _get_class_knowledges_table(knowledges, yp_paper_info) {
    // 筛选知识点
    let select_knowledge = [];
    for (const k of knowledges) {
        if (k.diff < 0) {
            select_knowledge.push(k);
        }
        if (select_knowledge.length === 10) break;
    }
    if (_.size(select_knowledge) < 5) { // 小于5个薄弱知识点，根据得分差获取前5个
        select_knowledge = knowledges.slice(0, 5);
    }
    // 生成薄弱知识点细目表
    const knowledge_questions = [];
    for (const k of select_knowledge) {
        let arr = k.question_detail;
        if (arr.length > 1) {
            arr = arr.sort((a, b) => {
                if (a.classScoreRate > b.classScoreRate) {
                    return 1;
                } else {
                    return -1;
                }
            });
            for (let i = 0; i < 2; i++) {
                const q = arr[i];
                knowledge_questions.push({
                    type: q.type,
                    knowledges: [{
                        id: k.id,
                        name: k.name
                    }]
                });
            }
        } else {
            for (let i = 0; i < 2; i++) {
                knowledge_questions.push({
                    type: arr[0].type,
                    knowledges: [{
                        id: k.id,
                        name: k.name
                    }]
                });
            }
        }
    }
    // 组合细目表
    const blocks = [];
    for (const q of knowledge_questions) {
        const new_ques = {
            knowledges: q.knowledges, //
            type: q.type,
            period: _.get(yp_paper_info, 'paper.period'),
            subject: subject_utils.regularSubject(_.get(yp_paper_info, 'paper.subject', '')),
            difficulty: '容易'
        };
        const type = new_ques.type;
        const type_t = TYPES[type] || TYPES['default'];
        new_ques.score = type_t.default_score;
        let block = blocks.find(e => e.type === type);
        if (_.isEmpty(block)) {
            block = {
                type: type,
                questions: []
            };
            const blk_pos = type_t['blk_pos'];
            let add = false;
            for (const i in blocks) {
                const type_t_ = TYPES[blocks[i].type] || TYPES['default'];
                const pos = type_t_['blk_pos'];
                if (pos > blk_pos) {
                    blocks.splice(parseInt(i), 0, block);
                    add = true;
                    break;
                }
            }
            if (!add) blocks.push(block);
        }
        // 加入试题
        block.questions.push(new_ques);
    }
    // 设置名字
    for (let i = 0; i < blocks.length; i++) {
        const block = blocks[i];
        block.name = `${DIGIT_MAP_CHINESE[i+1]}、${block.type}`;
        // delete block.type;
    }

    const table = {
        name: '',
        period: _.get(yp_paper_info, 'paper.period'),
        grade: _.get(yp_paper_info, 'paper.grade'),
        subject: subject_utils.regularSubject(_.get(yp_paper_info, 'paper.subject', '')),
        blocks
    };
    return table;
}


/**
 * 试题转换为细目表
 * 采用大题模式
 * @param user_id
 * @param exam_id
 * @param paper_id
 * @returns {Promise<{period: string, subject: *, blocks: *, grade: *, name: string}|null>}
 * @private
 */
async function _transform_to_table(user_id, exam_id, paper_id, yp_data) {
    // 好分数试题
    const hfs_questions = await client.hfsTeacherV3.getPaperQuestions(user_id, exam_id, paper_id, null);
    if (!_.size(hfs_questions)) return null;

    const yp_paper = _.get(yp_data, 'paper', null);
    const period = _.get(yp_paper, 'period', '');
    const grade =_.get(yp_paper, 'grade', '');
    const subject = subject_utils.regularSubject(_.get(yp_paper, 'subject', ''));

    const yp_questions = _.get(yp_data, 'questions', []);
    const blocks = [];
    for (const q of yp_questions) {
        const point = q.points[0]; // 大题模式-默认取第一个给分点
        const index = parseInt(point.id.split('-')[1]);
        const hfs_question = hfs_questions.find(e => e.questionId === index);
        if (_.isEmpty(hfs_question)) continue;
        const knowledges = [];
        for (const p of q.points) {
            const questionId = parseInt(point.id.split('-')[1]);
            const knowledge_questions = hfs_questions.filter( e=> e.questionId === questionId);
            if (_.size(knowledge_questions)) {
                for (const k_q of knowledge_questions) {
                    if (!_.size(k_q.knowledges)) continue;
                    const arr = k_q.knowledges.filter(n => !knowledges.find(k => k.id === n.id))
                    if (_.size(arr)) knowledges.push(...arr);
                }
            }
        }
        if (!_.size(knowledges)) continue; // 没有知识点的题目跳过
        const new_ques = {
            knowledges: knowledges.length <= 3 ? knowledges : knowledges.slice(0, 3), //
            type: _get_hfs_question_type(subject, hfs_question, q),
            period,
            subject,
            difficulty: '不限'
        };
        const type = new_ques.type;
        const type_t = TYPES[type] || TYPES['default'];
        new_ques.score = type_t.default_score;
        let block = blocks.find(e => e.type === type);
        if (_.isEmpty(block)) {
            block = {
                type: type,
                questions: []
            };
            const blk_pos = type_t['blk_pos'];
            let add = false;
            for (const i in blocks) {
                const type_t_ = TYPES[blocks[i].type] || TYPES['default'];
                const pos = type_t_['blk_pos'];
                if (pos > blk_pos) {
                    blocks.splice(parseInt(i), 0, block);
                    add = true;
                    break;
                }
            }
            if (!add) blocks.push(block);
        }
        // 加入试题
        block.questions.push(new_ques);
    }
    // 设置名字
    for (let i = 0; i < blocks.length; i++) {
        const block = blocks[i];
        block.name = `${DIGIT_MAP_CHINESE[i+1]}、${block.type}`;
        // delete block.type;
    }

    const table = {
        name: '',
        period,
        grade,
        subject,
        blocks
    };
    return table;
}

/**
 * 获取好分数试题类型
 * @param subject
 * @param hfs_question
 * @param yp_question
 * @returns {Promise<*>}
 * @private
 */
function _get_hfs_question_type(subject, hfs_question, yp_question) {
    const signle_subject_arr = ['语文', '英语'];
    let type = '';
    if (hfs_question.style === '客观题') {
        if (hfs_question.policy === 1) {
            type = '多选题';
        } else {
            if (hfs_question.optionstr === 'TF') {
                type = '判断题';
            } else {
                if (signle_subject_arr.includes(subject)) {
                    type = '单选题';
                } else {
                    type = '选择题';
                }
            }
        }
    } else {
        type = yp_question.type;
    }
    return type;
}

function _get_paper_result_data(exampaper, favorite) {
    const result = {};
    if (!_.isEmpty(exampaper)) {
        result.id=exampaper._id.toString();
        result.name = exampaper.name;
        result.type = exampaper.type;
        result.category = exampaper.category;
        result.period = exampaper.period;
        result.subject = exampaper.subject;
        result.question_count = _get_paper_question_count(exampaper);
        result.download = exampaper.exampaper_download_num ? 1 : 0; // 是否下载
        result.favorite = 0; // 是否收藏
        result.ctime = exampaper.ctime.getTime(); // 组卷时间
        if (!_.isEmpty(favorite)) {
            const favorite_exampaper = _.get(favorite, 'exampaper', []);
            result.favorite = favorite_exampaper.find(f => f.id === paper.id) ? 1 : 0; // 是否收藏
        }
    }
    return result;
}

function _get_paper_question_count(exampaper) {
    let count = 0;
    if (!_.isEmpty(exampaper)) {
        const question = [];
        if (exampaper.hasOwnProperty('volumes')) {
            for (const v of exampaper.volumes) {
                for (const b of v.blocks) {
                    for (const q of b.questions) {
                        question.push(q);
                    }
                }
            }
        } else if (exampaper.hasOwnProperty('blocks')) {
            for (const b of exampaper.blocks) {
                for (const q of b.questions) {
                    question.push(q);
                }
            }
        } else if (exampaper.hasOwnProperty('questions')) {
            for (const q of exampaper.questions) {
                question.push(q);
            }
        }
        count = _.size(question);
    }
    return count;
}

const subject_type_dict = {
    '高中':{
        '语文': {'单选题':[], '填空题':[], '默写题':[], '文言文翻译': [], '文言文阅读':[], '古诗词鉴赏':[], '现代文阅读':[], '综合读写':[]},
        '数学': {'选择题':[], '多选题':[], '填空题':[], '解答题':[]},
        '英语': {'单选题':[], '填空题':[], '语法填空':[], '完形填空':[], '阅读理解':[], '七选五':[], '短文改错':[]},
        '物理': {'选择题':[], '多选题':[], '填空题':[], '解答题':[], '实验探究题':[]},
        '化学': {'选择题':[], '多选题':[], '填空题':[], '解答题':[], '实验探究题':[]},
        '生物': {'选择题':[], '多选题':[], '填空题':[], '解答题':[]},
        '政治': {'选择题':[], '多选题':[], '判断题':[], '填空题':[], '辨析题':[], '简答题':[], '论述题':[], '材料分析题':[]},
        '历史': {'选择题':[], '多选题':[], '填空题':[], '辨析题':[], '简答题':[], '论述题':[], '材料分析题':[], '解答题':[]},
        '地理': {'选择题':[], '多选题':[], '填空题':[], '判断题':[], '解答题':[]}
    },
    '初中':{
        '语文': {'单选题':[], '填空题':[], '默写题':[], '文言文翻译': [], '文言文阅读':[], '古诗词鉴赏':[], '现代文阅读':[], '名著阅读':[], '综合读写':[]},
        '数学': {'选择题':[], '填空题':[], '解答题':[]},
        '英语': {'单选题':[], '填空题':[], '语法填空':[], '完形填空':[], '阅读理解':[], '七选五':[], '短文改错':[]},
        '物理': {'选择题':[], '多选题':[], '填空题':[], '作图题':[], '实验探究题':[], '解答题':[], '材料分析题':[]},
        '化学': {'选择题':[], '多选题':[], '填空题':[], '实验探究题':[], '解答题':[]},
        '生物': {'选择题':[], '多选题':[], '填空题':[], '判断题':[], '连线题':[], '解答题':[], '实验探究题':[]},
        '政治': {'选择题':[], '多选题':[], '填空题':[], '判断题':[], '材料分析题':[], '简答题':[], '辨析题':[], '论述题':[]},
        '历史': {'选择题':[], '多选题':[], '填空题':[], '改错题':[], '连线题':[], '辨析题':[], '简答题':[], '论述题':[], '材料分析题':[]},
        '地理': {'选择题':[], '多选题':[], '填空题':[], '判断题':[], '连线题':[], '解答题':[]}
    },
    '小学':{
        '语文': {'单选题':[], '填空题':[], '默写题':[], '文言文翻译': [], '文言文阅读':[], '古诗词鉴赏':[], '现代文阅读':[], '综合读写':[]},
        '数学': {'选择题':[], '多选题':[], '填空题':[], '解答题':[], '判断题':[], '计算题':[], '操作题':[], '应用题':[]},
        '英语': {'单选题':[], '填空题':[], '语法填空':[], '完形填空':[], '阅读理解':[], '七选五':[], '短文改错':[]},
    }
};
