let mongodber = require('../../utils/mongodber');
let ResponseWrapper = require('../../middlewares/response_wrapper');
let logger = require('../../utils/logger');
let enums = require('../../../bin/enum');
let db = mongodber.use('tiku');
const Joi = require('@hapi/joi');
const { ObjectId } = require('mongodb');

const getNoticeCenterSchema = Joi.object({
    limit: Joi.number().required(),
    offset: Joi.number().required(),
}).unknown(true);

const getNoticeCenterList = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let { limit, offset } = await Joi.validate(req.query, getNoticeCenterSchema);

        const cond = { status: enums.NoticeCenterStatus.ON };
        let total = await db.collection('@NoticeCenter').count(cond);
        let data = await db.collection('@NoticeCenter').find(cond).sort({ push_time: -1 }).skip(offset).limit(limit).toArray();

        if (!data) {
            throw new Error('没有数据');
        }
        let resObj = {
            total: total,
            list: data.map(item => {
                return {
                    id: item._id.toString(),
                    ctime: new Date(item.ctime).getTime(),
                    pushTime: new Date(item.push_time).getTime(),
                    title: item.title,
                    detail: item.detail,
                }
            })
        };

        return responseWrapper.succ(resObj);
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

const getNoticeCenterById = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let id = req.params.id;

        const cond = { _id: new ObjectId(id) };
        let data = await db.collection('@NoticeCenter').findOne(cond);

        if (!data) {
            throw new Error('没有数据');
        }
        let resObj = {
            id: data._id.toString(),
            ctime: new Date(data.ctime).getTime(),
            pushTime: new Date(data.push_time).getTime(),
            title: data.title,
            detail: data.detail,
        }

        return responseWrapper.succ(resObj);
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

const postNoticeCenterSchema = Joi.object({
    title: Joi.string().required(),
    detail: Joi.string().required(),
});

const postNoticeCenter = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let { title, detail } = await Joi.validate(req.body, postNoticeCenterSchema);

        const now = new Date();
        let insertData = {
            ctime: now,
            utime: now,

            title: title,
            detail: detail,
            status: enums.NoticeCenterStatus.OFF,
        };

        const noticeCenterResult = await db.collection('@NoticeCenter').insertOne(insertData);

        return responseWrapper.succ({ id: noticeCenterResult.insertedId.toString() });
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

module.exports = {
    getNoticeCenterList,
    getNoticeCenterById,
    postNoticeCenter,
};