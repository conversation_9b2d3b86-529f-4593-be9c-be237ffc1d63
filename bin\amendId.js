const _ = require('underscore');
const mongodb = require('mongodb');
const Thenjs = require('thenjs');
const program = require('commander');

program
	.version('0.1.0')
	.option('-k, --kb [kb]', 'kb db')
	.option('-t, --tiku [tiku]', 'tiku db')
	.parse(process.argv);

var kbURL = program.kb;
var tikuURL = program.tiku;

mongodb.connect(tikuURL, function(err, db){
	db.collection('resource_erratum').find({id: {$type: 2}}, {id: 1}).
	toArray(function(err, docs){
		Thenjs.each(docs, function(cont, doc){
			db.collection('resource_erratum').update({
				_id: doc._id	
			}, {
				$set: {
					id: Number(doc.id)
				}
			}, function(err, writeResult){
				cont(null, null);	
			});
		}).then(function(){
			db.close();	
		});
	});
});
