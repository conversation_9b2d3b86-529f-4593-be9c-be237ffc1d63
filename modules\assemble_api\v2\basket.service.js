/*!
 * Author: guochanghui
 * Date: 2017-04-24
 */

/**
 * Module dependencies.
 * @private
 */
const _ = require('lodash');
const config = require('config');
const ObjectID = require("mongodb").ObjectID;
const KBSERVER = config.get('KB_API_SERVER');
const URL = require('url');
const mongodber = require('../../utils/mongodber');
const ResponseWrapper = require('../../middlewares/response_wrapper');
const logger = require('../../utils/logger');
const deleteQuestionBlocks = require('../v1/utils').deleteQuestionBlocks;
const template = require('./../v1/template');
const enums = require('../../../bin/enum');
const axios = require('axios');
const db = mongodber.use('tiku');
const db_open = mongodber.use('tiku_open');
const qs = require('qs');
const Joi = require('@hapi/joi');
const user_vip_service = require('../../user_api/v1/user_vip_service');
const user_right_service = require('../../user_api/v2/user_right_service');
const paper_utils = require('../../utils/paper_utils');
const utils = require('../../utils/utils');
/**
 * Module variables.
 * @private
 */
var DURATION = 120 * 60;
var collection = db_open.collection(enums.OpenSchema.user_basket);
var TYPES = template.TYPES;
var DIGIT_MAP_CHINESE = template.DIGIT_MAP_CHINESE;

/**
 * Module functions.
 * @private
 */
function gen_rediser_key(user_id) {
    return 'assemble_api:v2:' + user_id + ':basket';
}

const JOI_GET_QUESTIONS = Joi.object({
    period: Joi.string().required(), //  学段
    subject: Joi.string().required(), // 科目
}).unknown(true);

async function get_questions(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {

        const { period, subject, category = enums.BasketCategory.BASKET } = await JOI_GET_QUESTIONS.validate(req.query);
        const user_id = req.user.id;
        const open_user_id = utils.formatUserId(user_id);
        const questions = await collection.find({ user_id: open_user_id, period, subject, category }).toArray();
        const result = paper_utils.get_profile_basket(user_id, questions);
        return responseWrapper.succ(result);
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}


// const JOI_POST_QUESTIONS = Joi.object({
//     period: Joi.string().required(), //  学段
//     subject: Joi.string().required(), // 科目
//     questions: Joi.array().items(Joi.object({
//         id: [
//             Joi.number().required(),
//             Joi.string().required()
//         ],
//         type: Joi.string().required(),
//         period: Joi.string().required(), //  学段
//         subject: Joi.string().required(), // 科目
//     }).unknown(true)).required().min(1),
// }).unknown(true);

async function post_questions(user_id, period, subject, questions) {
    const result = {
        error: false,
        type: '',
        desc: ''
    };
    const open_user_id = utils.formatUserId(user_id);
    const category = enums.BasketCategory.BASKET;
    try {
        const user_questions = await collection.find({ user_id: open_user_id, period, subject, category }).toArray();
        const new_arr = questions.filter(e => {
           if (!_.size(user_questions)) return true;
           return !user_questions.find(uq => uq.id === e.id);
        });
        if (!_.size(new_arr))  return paper_utils.get_profile_basket(user_id, user_questions);
        const limit_right = await user_right_service.get_right_limit(user_id, {
            type: enums.NewRightType.basket_ques_limit,
            period,
            subject
        });
        const total_count = _.size(user_questions) + _.size(new_arr);
        if (total_count > limit_right.limit) {
            result.error = true;
            result.type = 'TIMES_NOT_ENOUGH_ERROR';
            if (limit_right.group !== enums.NewVipType.personal_pro) {
                result.desc = `非常抱歉，您的试题篮最多可添加${limit_right.limit}道题，升级尊享会员可添加更多试题~`;
            } else {
                result.desc = `您添加试题超过上限100道，试题篮装不下啦~`;
            }
            return result;
        }
        // 保存试题
        const now = new Date();
        for (const q of new_arr) {
            const data = _.assign({}, q);
            data.user_id = open_user_id;
            data.category = category;
            data.ctime = now;
            data.utime = now;
            if (!data.source) {
                data.source = enums.QuestionSource.SYS;
                data.source_id = data.id;
            }
            await collection.insertOne(data);
        }
        return result;
    } catch (e) {
        logger.error(e.stack);
        result.error = true;
        result.type = 'HANDLE_ERROR';
        result.desc = e.message;
        return result;
    }
}

const JOI_DEELTE_QUESTIONS = Joi.object({
    period: Joi.string().required(), //  学段
    subject: Joi.string().required(), // 科目
    questions: Joi.array().items([
        Joi.number(),
        Joi.string()
    ]).required().min(1),
}).unknown(true);

async function delete_questions(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        let result = [];
        const { period, subject, questions, category = enums.BasketCategory.BASKET } = await JOI_DEELTE_QUESTIONS.validate(req.body);
        const user_id = req.user.id;
        const open_user_id = utils.formatUserId(user_id);
        let user_questions = await collection.find({ user_id: open_user_id, period, subject, category }).toArray();
        if (!_.size(user_questions)) return responseWrapper.succ(result);
        for (const qid of questions) {
            const delete_data = user_questions.find(d => d.id === qid);
            if (delete_data) {
                await collection.deleteOne({_id: delete_data._id});
                user_questions = user_questions.filter(e => e.id !== delete_data.id);
            }
        }
        result = paper_utils.get_profile_basket(user_id, user_questions);
        return responseWrapper.succ(result);
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
}

const JOI_GET_BASKET = Joi.object({
    period: Joi.string().required(), //  学段
    subject: Joi.string().required(), // 科目
    exampaper_id: [Joi.string().optional(), Joi.number().optional()], // 试卷ID
    op: Joi.string().valid('delete', 'merge').optional().default('delete'),
    source_type: Joi.string().valid('sys', 'assemble', 'upload').optional().default(enums.PaperSourceType.SYS),
    template: Joi.string().valid('standard', 'exam', 'homework').optional()
}).unknown(true);

async function get_basket(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const user_id = req.user.id;
        const open_user_id = utils.formatUserId(user_id);
        const { period, subject, exampaper_id, op, source_type, template, category = enums.BasketCategory.BASKET } = await JOI_GET_BASKET.validate(req.query);
        const basket = paper_utils.init();
        delete basket['_id'];
        basket.period = period;
        basket.subject = period;

        let user_questions = await collection.find({ user_id: open_user_id, period, subject, category  }).toArray();
        if (exampaper_id) {
            if (op === 'delete') {
                // 删除同学段、科目数据
                await collection.deleteMany({user_id: open_user_id, period, subject, category});
                user_questions = [];
            }
            const limit_right = await user_right_service.get_right_limit(user_id, {
                type: enums.NewRightType.basket_ques_limit,
                period,
                subject
            });
            const fun = HANDLER_EXAMPAPER[source_type];
            let { exampaper, questions }= await fun(req, exampaper_id);
            // 校验试题数量
            const new_questions = questions.filter( e => !(user_questions || []).find(uq => uq.id === e.id) );
            const total_count = _.size(user_questions) + _.size(new_questions);
            if (total_count > limit_right.limit) {
                if (limit_right.group !== enums.NewVipType.personal_pro) {
                    return responseWrapper.error('TIMES_NOT_ENOUGH_ERROR', `非常抱歉，您的试题篮最多可添加${limit_right.limit}道题，升级尊享会员可添加更多试题~`);
                } else {
                    return responseWrapper.error('TIMES_NOT_ENOUGH_ERROR', `您添加试题超过上限100道，试题篮装不下啦~`);
                }
            }
            // 保存试题
            if (_.size(new_questions)) {
                const insert_questions = [];
                const now = new Date();
                for (const question of new_questions) {
                    const insert_data = _.pick(question, ['id', 'type', 'period', 'subject', 'score', 'source', 'source_id']);
                    insert_data.user_id = open_user_id;
                    insert_data.category = category;
                    insert_data.ctime = now;
                    insert_data.utime = now;
                    insert_questions.push(insert_data);
                    user_questions.push(insert_data);
                }
                await collection.insertMany(insert_questions);
            }
            // 处理试题篮复用信息
            if (exampaper.name) {
                basket.name = exampaper.name + '(副本)';
            }
            if (exampaper.template) {
                basket.template = template;
            }
            if (exampaper.partsList) {
                basket.partsList = exampaper.partsList;
            }
            if (exampaper.grade) {
                basket.grade = exampaper.grade;
            }
            if (exampaper.type) {
                basket.type = exampaper.type;
            }
        }

        // 插入试题
        const kb_ids = [];
        const zx_ids = [];
        const zyk_ids = [];
        const ques_map = {};
        for (const question of user_questions) {
            if (question.source === enums.QuestionSource.UPLOAD) {
                ques_map[question.id] = question;
            } else if (question.source === enums.QuestionSource.ZX) {
                zx_ids.push(question.id);
            } else if (question.source === enums.QuestionSource.ZYK) {
                zyk_ids.push(question.id);
            } else {
                if (_.isNumber(question.id)) kb_ids.push(question.id);
            }
        }

        // for (const question of user_questions) {
        //     if (_.isNumber(question.id)) {
        //         kb_ids.push(question.id);
        //     } else {
        //         user_ids.push(question.id);
        //     }
        // }
        if (_.size(kb_ids)) {
            const questions = await client.kb.getQuestions(kb_ids);
            questions.forEach(q => ques_map[q.id] = q);
        }
        if (_.size(zx_ids)) {
            const questions = await paper_utils.getZxQuestionByIds(zx_ids);
            questions.forEach(q => ques_map[q.id] = q);
        }
        if (_.size(zyk_ids)) {
            const questions = await paper_utils.getZykQuestionByIds(zyk_ids);
            questions.forEach(q => ques_map[q.id] = q);
        }
        for (const q of user_questions) {
            let question =ques_map[q.id];
            if (_.isEmpty(question)) continue;
            // question =  _.pick(question, ['id', 'type', 'period', 'subject']);
            paper_utils.insert_questions(basket, question);
        }
        paper_utils.traverse_questions(basket, ques_map);
        paper_utils.render_basket(basket);
        // 返回结果
        return responseWrapper.succ(basket);
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

async function put_basket(req, res) {
    // 弃用
    const responseWrapper = new ResponseWrapper(req, res);
    return responseWrapper.succ('');
}

const HANDLER_EXAMPAPER = {
    [enums.PaperSourceType.SYS]: _handler_sys_exampaper,
    [enums.PaperSourceType.ASSEMBLE]: _handler_assemble_exampaper,
    [enums.PaperSourceType.UPLOAD]: _handler_assemble_exampaper,
}

async function _handler_sys_exampaper(req, exampaper_id) {
    // 系统试卷库
    const kb_exampaper = client.kb.getExampaper(exampaper_id);
    if (_.isEmpty(kb_exampaper)) {
        logger.error(`KB获取试卷失败exampaper_id: ${exampaper_id}`);
        throw new Error('获取系统试卷失败');
    }
    const questions = [];
    for (const b of _.get(kb_exampaper, 'blocks', [])) {
        for (const ques of b.questions) {
            questions.push(ques);
        }
    }
    return { exampaper: kb_exampaper, questions };
}



async function _handler_assemble_exampaper(req, exampaper_id) {
    const query = { _id: new ObjectID(exampaper_id), user_id: utils.formatUserId(req.user.id)};
    const exampaper = await db_open.collection(enums.OpenSchema.user_paper).findOne(query);
    if (_.isEmpty(exampaper)) {
        logger.error(`组卷信息不存在${exampaper_id}`);
        throw new Error('组卷信息不存在');
    }
    const questions = [];
    for (const volume of _.get(exampaper, 'volumes', [])) {
        for (const block of _.get(volume, 'blocks', [])) {
            for (const question of _.get(block, 'questions', [])) {
                questions.push(question);
            }
        }
    }
    return { exampaper, questions };
}

// async function _handler_upload_exampaper(req, exampaper_id) {
//     const query = { _id: new ObjectID(exampaper_id), user_id: req.user.id };
//     const exampaper = await db.collection('upload_exampaper').findOne(query);
//     if (_.isEmpty(exampaper)) {
//         logger.error(`用户上传试卷信息不存在${exampaper_id}`);
//         throw new Error(`用户上传试卷信息不存在`);
//     }
//     const ids = [];
//     for (const volume of exampaper.volumes) {
//         for (const b of volume.blocks) {
//             for (const q of b.questions) {
//                 ids.push(q.id);
//             }
//         }
//     }
//     const questions = await get_user_upload_questions(ids);
//     return { exampaper, questions };
// }

// const JOI_DELETE_BASKET = Joi.object({
//     period: Joi.string().required(), //  学段
//     subject: Joi.string().required(), // 科目
// }).unknown(true);

async function delete_basket(user_id, period, subject) {
    const result = {
        error: false,
        type: '',
        desc: ''
    };
    const category = enums.BasketCategory.BASKET;
    const open_user_id = utils.formatUserId(user_id);
    try {
        await collection.deleteMany({user_id: open_user_id, period, subject, category});
        return result;
    } catch (e) {
        logger.error(e.stack);
        result.error = true;
        result.type = 'HANDLE_ERROR';
        result.desc = e.message;
        return result;
    }
}

module.exports = {
    get_questions,
    post_questions,
    delete_questions,
    get_basket,
    delete_basket,
    put_basket,
}
