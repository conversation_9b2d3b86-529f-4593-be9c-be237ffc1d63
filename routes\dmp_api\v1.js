var express = require('express');
var router = express.Router();
var dmpAPI = require('../../modules/dmp_api/v1/index.js');
var apikey = require('../../modules/middlewares/apikey');

// kb api key middleware
router.use(apikey('DMP'));

router.get('/erratums', dmpAPI.getErratumList);
router.get('/erratums/:err_id', dmpAPI.getErratum);
router.put('/erratums/:err_id/status', dmpAPI.updateErratum);

module.exports = router;
