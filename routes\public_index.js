const payments_api_v1 = require('./payments_api/notification')
const user_api_v1 = require('./user_api/public')
const kb_api_v2 = require('./kb_api/public')
const goods_api_v1 = require('./goods_api/v1')
const goods_api_v2 = require('./goods_api/v2')
const notice_center_api_v1 = require('./notice_center_api/public')
const album_api_v1 = require('./album_api/public')
const statistic_api_v1 = require('./statistic_api/public')
const statistic_api_v2 = require('./statistic_api/v2')
const tiku_api = require('./tiku_api/v1')
const assemble_api_v1 = require('./assemble_api/public')
const tiku_api_v1 = require('./tiku_api_v1/public')
const search_api_v2 = require('./search_api/public')
const questions_api_v1 = require('./questions_api/public')
const exampaper_api_v1 = require('./exampaper_api/public.js')
const banner_api_v1 = require('./banners_api/public')
const login_api_v2 = require('./user_api/login')
const micro_course_api_v1 = require('./micro_course_api/public')
const edu_assistant_file_api_v1 = require('./edu_assistant_file_api/public')
const edu_assistant_tool_api_v1 = require('./edu_assistant_tool_api/public')
const notice_message_api_v1 = require('./notice_message_api/public')
const wechat_api_v1 = require('./wechat/public')
const topic_api_v1 = require('./topic_api/public')
const learning_analysis_public = require('./learning_analysis/public');
const album_api_v2 = require('./album_api/public_v2');

function route (app) {
  // 运维心跳检测接口
  app.get('/health_check/', function (req, res) {
    res.json({ msg: 'healthy' })
  })

  app.use('/payments_api/v1', payments_api_v1)
  app.use('/user_api/v1', user_api_v1)
  app.use('/kb_api/v2', kb_api_v2)
  app.use('/goods_api/v1', goods_api_v1)
  app.use('/goods_api/v2', goods_api_v2)
  app.use('/notice_center_api/v1', notice_center_api_v1)
  app.use('/album_api/v1', album_api_v1)
  app.use('/statistic_api/v1', statistic_api_v1)
  app.use('/gateway/v1', tiku_api)
  app.use('/assemble_api/v1', assemble_api_v1)
  app.use('/tiku_api/v2', tiku_api_v1)
  app.use('/se_kb/v2', search_api_v2)
  app.use('/questions_api/v1', questions_api_v1)
  app.use('/exampaper_api/v1', exampaper_api_v1)
  app.use('/banners/v1', banner_api_v1)
  app.use('/statistic_api/v2', statistic_api_v2)
  app.use('/micro-course/v1', micro_course_api_v1)
  app.use('/edu_assistant_file/v1', edu_assistant_file_api_v1)
  app.use('/edu_assistant_tools/v1', edu_assistant_tool_api_v1)
  app.use('/login/v2', login_api_v2);
  app.use('/notice_msg_api/v1', notice_message_api_v1);
  app.use('/wechat_api/v1', wechat_api_v1);
  app.use('/topic/v1', topic_api_v1);
  app.use('/learning_analysis/v1', learning_analysis_public);
  app.use('/album_api/v2', album_api_v2);

  app.use('/', user_api_v1)
}

module.exports = route
