let mongodber = require('../../utils/mongodber');
let enums = require('../../../bin/enum');
let db = mongodber.use('tiku');
const ObjectId = require('mongodb').ObjectId;

async function deductionTicketItem(count, userId, ucId, type, expireIds) {
    let ticketItemCond = {
        status: enums.TicketItemStatus.AVAILABLE
    }
    // if (ucId) ticketItemCond.uc_id = ucId;
    // else ticketItemCond.user_id = userId;
    ticketItemCond.user_id = userId;
    const items = await db.collection('@TicketItem').find(ticketItemCond).sort({ ctime: 1, _id: 1 }).limit(count).project({ _id: 1 }).toArray();
    if (items.length !== count) {
        throw new Error('下载券余额不足');
    }

    let ids = items.map(i => i._id.toString());
    const now = new Date();
    const updateData = {
        status: enums.TicketItemStatus.USED,
        utime: now,
    };
    const updateResult = await db.collection('@TicketItem').updateMany({
        _id: { $in: ids.map(i => new ObjectId(i)) },
        status: enums.TicketItemStatus.AVAILABLE,
    }, { $set: updateData });

    if (updateResult.modifiedCount !== items.length) {
        throw new Error('下载券余额不足');
    }

    await db.collection('@TicketRecord').insertOne({
        ctime: now,
        utime: now,

        user_id: userId,
        uc_id: ucId,
        type: type,

        download_ids: expireIds,
        items: ids,
        amount: -count,
    });

    return ids;
}

module.exports = {
    deductionTicketItem,
};