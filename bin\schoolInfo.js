const config = require('config');
const MongoClient = require('mongodb').MongoClient;
const sch = require('./school');

const memberInfo = [
	{
		"level_name": "体验V1",
		"que_download_num": {
			"day": 100,
			"month": 2000
		},
		"exampaper_download_num": {
			"day": 40,
			"month": 800
		},
		"intelligence_volume_num": {
			"day": 10,
			"month": 100
		},
		"specification_volume_num": {
			"day": 10,
			"month": 100
		},
		"volume_exampaper_num": {
			"question": 30
		},
		"que_details_num": {
			"day": 20,
			"month": 400
		},
		"exampaper_details_num": {
			"day": 10,
			"month": 200
		},
		"ctime": new Date(),
		"utime": new Date()
	},
	{
		"level_name": "付费V1",
		"que_download_num": {
			"day": 500,
			"month": 10000
		},
		"exampaper_download_num": {
			"day": 200,
			"month": 4000
		},
		"intelligence_volume_num": {
			"day": 50,
			"month": 500
		},
		"specification_volume_num": {
			"day": 50,
			"month": 500
		},
		"volume_exampaper_num": {
			"question": 50
		},
		"que_details_num": {
			"day": 100,
			"month": 2000
		},
		"exampaper_details_num": {
			"day": 50,
			"month": 1000
		},
		"ctime": new Date(),
		"utime": new Date()
	},
	{
		"level_name": "虚拟V1",
		"que_download_num": {
			"day": 5000,
			"month": 100000
		},
		"exampaper_download_num": {
			"day": 2000,
			"month": 40000
		},
		"intelligence_volume_num": {
			"day": 500,
			"month": 5000
		},
		"specification_volume_num": {
			"day": 500,
			"month": 5000
		},
		"volume_exampaper_num": {
			"question": 200
		},
		"que_details_num": {
			"day": 1000,
			"month": 20000
		},
		"exampaper_details_num": {
			"day": 500,
			"month": 10000
		},
		"ctime": new Date(),
		"utime": new Date()
	}
];

const run = async () => {
	const tikuclient = await MongoClient.connect(config.get('MONGODBS').tiku);
	const tikuDB = tikuclient.db('tiku');
	const schoolColl = tikuDB.collection('school_info');
	const exampaperColl = tikuDB.collection('exampaper');
	const memeberColl = tikuDB.collection('member');

	let memberCount = 0;
	let schoolCount = 0;
	console.log(`会员权益表总数据：${memberInfo.length}`);
	for (const value of memberInfo) {
		if (value) {
			await memeberColl.insertOne(value);
			memberCount++;
		}
	}
	console.log(`会员权益表共计刷了数据：${memberCount}`);

	// 获取member_id
	let result = await memeberColl.find({}).toArray();
	let configMember = {};

	for (const value of result) {
		if (value) {
			if (value.level_name === '体验V1') {
				configMember.experience = value._id.toString();
			}
			if (value.level_name === '付费V1') {
				configMember.pay = value._id.toString();
			}
			if (value.level_name === '虚拟V1') {
				configMember.virtual = value._id.toString();
			}
		}
	}

	console.log(`学校表总数据：${sch.schoolInfo.length}`);
	for (const value of sch.schoolInfo) {
		if (value) {
			let data = {
				_id: value.id,
				name: value.name,
				type: value.type,
				valid: true,
				ctime: new Date(),
				utime: new Date()
			}
			if (value.type === '体验校') {
				data.member_id = configMember.experience;
			}
			if (value.type === '付费校') {
				data.member_id = configMember.pay;
			}
			if (value.type === '虚拟校') {
				data.member_id = configMember.virtual;
			}
			await schoolColl.insertOne(data);
			schoolCount++;
		}
	}
	console.log(`学校表共计刷了数据：${schoolCount}`);
	// 创建索引
	exampaperColl.createIndex({ share_sort: -1, period: 1 }, { background: true });
	exampaperColl.createIndex({ view_count: -1 }, { background: true });
	exampaperColl.createIndex({ exampaper_download_num: -1 }, { background: true });
};

run().then(() => {
	console.log('刷！！！');
}).catch(e => {
	console.log(e);
});