
let mongodber = require('../../utils/mongodber');
let ResponseWrapper = require('../../middlewares/response_wrapper');
let logger = require('../../utils/logger');
const { ObjectID } = require('mongodb');
let db = mongodber.use('tiku');
const URL = require('url');
const axios = require('axios');
const config = require('config');
const schoolCollection = db.collection('school_info');
const memberCollection = db.collection('member');

const schType = {
    'Tryout': '体验校',
    'Signed': '付费校'
};
// 公用的
function memberData(req) {
    let data = {};
    data.level_name = req.body.level_name;
    data.que_download_num = req.body.que_download_num;
    data.exampaper_download_num = req.body.exampaper_download_num;
    data.intelligence_volume_num = req.body.intelligence_volume_num;
    data.specification_volume_num = req.body.specification_volume_num;
    data.volume_exampaper_num = req.body.volume_exampaper_num;
    data.que_details_num = req.body.que_details_num;
    data.exampaper_details_num = req.body.exampaper_details_num;
    return data;
}

// 会员权益管理
const putMemberInfo = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    let id = req.params.id;
    let data = memberData(req);
    if (!id) {
        return responseWrapper.error('PARAMETERS_ERROR', 'id 为必传字段！');
    }
    try {
        data.utime = new Date();
        let resId = await memberCollection.updateOne({ _id: ObjectID(id) }, { $set: data });
        return responseWrapper.succ({ id: resId });
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

const postMemberInfo = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    let data = memberData(req);
    try {
        data.ctime = new Date();
        data.utime = new Date();
        let resId = await memberCollection.insertOne(data);
        return responseWrapper.succ({ id: resId });
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

const getMemberInfoById = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    let id = req.params.id;
    if (!id) {
        return responseWrapper.error('PARAMETERS_ERROR', 'id 为必传字段！');
    }
    try {
        let result = await memberCollection.findOne({ _id: ObjectID(id) });
        if (result) {
            result.id = result._id;
            delete result._id;
        }
        return responseWrapper.succ({ result });
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};
const getMemberList = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    let limit = Number(req.query.limit || 10);
    let offset = Number(req.query.offset || 0);
    let status = req.query.status;
    let cond = {};
    if (!status) {
        return responseWrapper.error('PARAMETERS_ERROR', 'status 为必传字段！')
    }
    try {
        if (status === 'page') {
            let result = await memberCollection.find(cond).sort({ utime: -1 }).limit(limit).skip(offset).toArray();
            for (const value of result) {
                if (value) {
                    value.id = value._id;
                    delete value._id;
                }
            }
            let total_num = await memberCollection.find(cond).count();
            return responseWrapper.succ({ list: result, total_num: total_num });
        }
        if (status === 'all') {
            let result = await memberCollection.aggregate([
                { $group: { _id: "$level_name", level_name: { $first: "$_id" } } }
            ]).toArray();
            for (const value of result) {
                value.id = value.level_name;
                value.level_name = value._id;
                delete value._id;
            }
            return responseWrapper.succ({ list: result });
        }
        return responseWrapper.succ({});
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};


// 学校权限管理
const getSchoolInfoById = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    let id = Number(req.params.id);
    if (!id) {
        return responseWrapper.error('PARAMETERS_ERROR', 'id 为必传字段！');
    }
    try {
        let result = await schoolCollection.findOne({ _id: id });
        if (result) {
            result.id = result._id;
            delete result._id;
        }
        return responseWrapper.succ({ result });
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

const getSchoolList = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    let limit = Number(req.query.limit || 10);
    let offset = Number(req.query.offset || 0);
    let type = req.query.type;
    let school = req.query.school;

    let cond = {};
    if (type) {
        cond.type = type;
    }
    if (school) {
        school = parseInt(req.query.school);
        if (isNaN(school)) {
            cond.name = new RegExp(req.query.school);
        } else {
            cond._id = school;
        }
    }
    cond.valid = true;
    let project = { utime: 0, valid: 0, is_edit_type: 0 };
    try {

        let result = await schoolCollection.find(cond).sort({ _id: -1 })
            .limit(limit).skip(offset).project(project).toArray();

        for (let value of result) {
            if (value) {
                value.id = value._id;
                delete value._id;
                if (value.member_id) {
                    let options = { fields: { level_name: 1 } };
                    let resMember = await memberCollection.findOne({ _id: ObjectID(value.member_id) }, options);
                    if (resMember) {
                        value.level_name = resMember.level_name;
                    }
                }
            }
        }
        let total_num = await schoolCollection.find(cond).count();
        return responseWrapper.succ({ list: result, total_num: total_num });
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

const putSchoolInfo = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    let id = Number(req.params.id);
    let type = req.body.type;
    let member_id = req.body.member_id;
    let data = {};
    if (!id) {
        return responseWrapper.error('PARAMETERS_ERROR', 'id 为必传字段！');
    }
    if (!type || member_id.length !== 24) {
        return responseWrapper.error('PARAMETERS_ERROR', 'type 和 member_id 为必传字段，且member_id为24位！');
    }
    try {
        data.type = type;
        data.member_id = member_id;
        data.is_edit_type = true;
        data.utime = new Date();
        let result = await schoolCollection.findOne({ _id: id });
        if (!result) {  // 第一次
            let setData = {
                _id: id,
                name: req.body.name || '',
                type: type,
                member_id: member_id,
                is_edit_type: true,
                valid: true,
                ctime: new Date(),
                utime: new Date()
            }
            let resId = await schoolCollection.insertOne(setData);
            return responseWrapper.succ({ id: resId });
        }
        let resId = await schoolCollection.updateOne({ _id: id }, { $set: data });
        return responseWrapper.succ({ id: resId });
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};
module.exports = {
    getMemberInfoById: getMemberInfoById,
    putMemberInfo: putMemberInfo,
    postMemberInfo: postMemberInfo,
    getMemberList: getMemberList,
    getSchoolInfoById: getSchoolInfoById,
    getSchoolList: getSchoolList,
    putSchoolInfo: putSchoolInfo,
};