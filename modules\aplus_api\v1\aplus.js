/*
 * Desc: aplus api
 *
 */
const config = require('config');
const qs = require('querystring');
const Thenjs = require('thenjs');
const Logger = require('../../utils/logger');
const ResponseWrapper = require('../../middlewares/response_wrapper');
const HttpWrapper = require('../../utils/http_wrapper');

const YUANPEI_API_SERVER = config['YUANPEI_API_SERVER'];
const TIMEOUT = 1000;
const RETRY_TIMES = 1;

function excellentAnswerFilter(stuId, quesIds, callback) {
    var server = config.get('HFS_VIP_SERVER'); 
    var _body = {
        studentId: stuId,
        questions: quesIds,
    };
    var options = {
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        method: 'POST',
        path: '/v1/questions/xb-answers/configs',
        timeout: TIMEOUT,
        body: JSON.stringify(_body),
        headers: {
            'content-type': 'application/json',
        },
    };
    var httpWrapper = new HttpWrapper();
    httpWrapper.request(options, function(err, ret){
        if (err){
            Logger.error('hfs xb-answers api failed' + err);
            return callback(null, []);
        }
        if (ret.statusCode == 200){
            try {
                // KBOE更改结构，兼容app
                ret = JSON.parse(ret.data);
                if (ret.code != 0) {
                    var err = new Error('Verify student:' + ret.msg);
                    return callback('hfs xb-answers api parsing failed' + err);
                    return callback(null, []);
                }
                return callback(null, ret.data);
            } catch (err){
                Logger.error('hfs xb-answers api parsing failed' + err);
                return callback(null, []);
            }
        } else {
            Logger.error('hfs xb-answers api failed');
            return callback(null, []);
        }
    });
}

function paper(req, res){
    var resWrapper = new ResponseWrapper(req, res);
    var paper_id = req.params.paper_id;
    var fields_type = req.query.fields_type || 'stem';
    var stu_id = req.user.id;
    if (fields_type != 'all' && fields_type != 'stem'){
        return resWrapper.error('PARAMETERS_ERROR', 'fields_type error');
    }
    // access control
    // if (fields_type == 'all' && !req.isVip){
    //     return resWrapper.error('NEED_VIP_ERROR');
    // }

    var _query = {
        fields_type: fields_type,
        api_key: req.apiKey,
    };
    var _path = `/yuanpei_api/v3/exampapers/recognition/${paper_id}/?`;
    _path += qs.stringify(_query);

    var options = {
        protocol: YUANPEI_API_SERVER.protocol,
        hostname: YUANPEI_API_SERVER.hostname,
        port: YUANPEI_API_SERVER.port,
        method: 'GET',
        path: _path,
        timeout: TIMEOUT,
        retryTimes: RETRY_TIMES,
    };

    var httpWrapper = new HttpWrapper();
    Thenjs(function(cont) {
        httpWrapper.request(options, function(err, ret){
            if (err){
                return resWrapper.error('HANDLE_ERROR', err.message);
            }
            if (ret.statusCode == 200){
                try {
                    // KBOE更改结构，兼容app
                    var exam = JSON.parse(ret.data);
                    var questions = exam.questions;
                    for (var i in questions){
                        var ques = questions[i];
                        ques['image_url'] = ques['id'];
                        delete ques['id'];
                    }
                    return cont(null, exam);
                } catch (err){
                    return resWrapper.error('HANDLE_ERROR');
                }
            } else if (ret.statusCode >= 400 && ret.statusCode < 500){
                var data = JSON.parse(ret.data);
                return resWrapper.send(data);
            } else if (ret.statusCode >= 500){
                return resWrapper.error('HANDLE_ERROR');
            } else {
                return resWrapper.error('HANDLE_ERROR');
            }
        });
    }).then(function(cont, exam) {
        var ques_ids = [];
        for (var i=0; i<exam.questions.length; i++) {
            ques_ids.push(exam.questions[i].image_url);
        }
        excellentAnswerFilter(stu_id, ques_ids, function(err, filter) {
            if (err) {
                Logger.error('filter error');
                return resWrapper.error('HANDLE_ERROR');
            }
            return cont(null, exam, filter);
        });
        
    }).then(function(cont, exam, filter) {
        var filterDict = {};
        for (var i=0; i<filter.length; i++) {
            filterDict[filter[i]['questionKey']] = filter[i]['hideXbAnswer'];
        }
        for (var i=0; i<exam.questions.length; i++) {
            var _id = exam.questions[i]['image_url'];
            exam.questions[i]['hideXbAnswer'] = filterDict[_id];

            if (filterDict[_id]==true || filterDict[_id]==null) {
                if (exam.questions[i]['answer'] && exam.questions[i]['answer']['type'] == 'excellent') {
                    exam.questions[i]['answer']['contents'] = [[]];
                }
            }
        }
        return resWrapper.succ(exam);
    }).fail(function(cont, err) {
        Logger.error(err);
        return resWrapper.error('HANDLE_ERROR');
    }).finally(function(cont, err) {
        Logger.error(err);
        return resWrapper.error('HANDLE_ERROR');
    });
}

function postQuestions(req, res){
    var resWrapper = new ResponseWrapper(req, res);

    // access control
    // if (!req.isVip){
    //     return resWrapper.error('NEED_VIP_ERROR');
    // }

    var _query = {
        api_key: req.apiKey,
    };
    var _path = `/yuanpei_api/v3/questions/recognition/?`;
    _path += qs.stringify(_query);
    var image_urls = req.body['image_urls'];
    var stu_id = req.user.id;
    if (!(image_urls instanceof Array) || image_urls.length == 0){
        return resWrapper.error('PARAMETERS_ERROR');
    }
    // KBOE更改结构，兼容APP
    var reqData = {
        'ids': image_urls,
        'fields_type': 'all'
    }
    var _body = JSON.stringify(reqData);

    var options = {
        protocol: YUANPEI_API_SERVER.protocol,
        hostname: YUANPEI_API_SERVER.hostname,
        port: YUANPEI_API_SERVER.port,
        method: 'POST',
        path: _path,
        body: _body,
        headers: {
            'content-type': 'application/json',
        },
        timeout: TIMEOUT,
        retryTimes: RETRY_TIMES,
    };
    var httpWrapper = new HttpWrapper();
    Thenjs(function(cont) {
        httpWrapper.request(options, function(err, ret){
            if (err){
                return resWrapper.error('HANDLE_ERROR', err.message);
            }
            if (ret.statusCode == 200){
                var queses = JSON.parse(ret.data);
                for (var i in queses){
                    queses[i]['image_url'] = queses[i]['id'];  // 数据格式兼容app端
                    delete queses[i]['id'];
                }
                return cont(null, queses);
            } else if (ret.statusCode >= 400 && ret.statusCode < 500){
                var data = JSON.parse(ret.data);
                return resWrapper.send(data);
            } else if (ret.statusCode >= 500){
                return resWrapper.error('HANDLE_ERROR');
            } else {
                return resWrapper.error('HANDLE_ERROR');
            }
        });
    }).then(function(cont, queses) {
        var ques_ids = [];
        for (var i=0; i<queses.length; i++) {
            ques_ids.push(queses[i].image_url);
        }
        excellentAnswerFilter(stu_id, ques_ids, function(err, filter) {
            if (err) {
                Logger.error('filter error');
                return resWrapper.error('HANDLE_ERROR');
            }
            return cont(null, queses, filter);
        });
    }).then(function(cont, queses, filter) {
        var filterDict = {};
        for (var i=0; i<filter.length; i++) {
            filterDict[filter[i]['questionKey']] = filter[i]['hideXbAnswer'];
        }
        for (var i=0; i<queses.length; i++) {
            var _id = queses[i]['image_url'];
            queses[i]['hideXbAnswer'] = filterDict[_id];
            if (filterDict[_id]==true || filterDict[_id]==null) {
                if (queses[i]['answer'] && queses[i]['answer']['type'] == 'excellent') {
                    queses[i]['answer']['contents'] = [[]];
                }
            }
        }
        return resWrapper.succ(queses);
    }).fail(function(cont, err) {
        Logger.error(err);
        return resWrapper.error('HANDLE_ERROR');
    }).finally(function(cont, err) {
        Logger.error(err);
        return resWrapper.error('HANDLE_ERROR');
    });
}

/*
 * Desc: update download times of paper
 */
function paperDownloadTimes(req, res){
    var resWrapper = new ResponseWrapper(req, res);
    var paper_id = req.params.paper_id;
    if (!paper_id){
        return resWrapper.error('PARAMETERS_ERROR', 'paper_id cannot empty');
    }
    var _query = {
        api_key: req.apiKey,
    };
    var _path = `/yuanpei_api/v3/exampapers/recognition/${paper_id}/download_times/?`;
    _path += qs.stringify(_query);

    var options = {
        protocol: YUANPEI_API_SERVER.protocol,
        hostname: YUANPEI_API_SERVER.hostname,
        port: YUANPEI_API_SERVER.port,
        method: 'PUT',
        path: _path,
        timeout: TIMEOUT,
        retryTimes: RETRY_TIMES,
    }
    var httpWrapper = new HttpWrapper();
    httpWrapper.request(options, function(err, ret){
        if (err){
            return resWrapper.error('HANDLE_ERROR', err.message);
        }
        if (ret.statusCode == 200){
            return resWrapper.succ(null);
        } else if (ret.statusCode >= 400 && ret.statusCode < 500){
            return resWrapper.send(JSON.parse(ret.data));
        } else if (ret.statusCode >= 500){
            return resWrapper.error('HANDLE_ERROR');
        } else {
            return resWrapper.error('HANDLE_ERROR');
        }
    });
}

module.exports = {
    paper: paper,
    postQuestions: postQuestions,
    paperDownloadTimes: paperDownloadTimes,
}
