# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个基于 Node.js v10.24.1 的教育平台后端 API 服务器（题库系统），使用 Express.js 框架构建，提供题目、试卷、作业等教育资源的管理功能。

## 开发命令

### 环境准备
```bash
# 使用正确的 Node.js 版本
nvm use  # 读取 .nvmrc 文件，使用 v10.24.1

# 安装依赖
npm install
```

### 基础命令
- `npm run dev` - 启动开发服务器（使用 nodemon 自动重启）
- `npm start` 或 `node app.js` - 启动生产服务器
- `./build` - 构建前端资源（从独立仓库）

### 代码质量
```bash
# ESLint 检查
./node_modules/.bin/eslint modules/ routes/ app.js

# 检查特定文件
./node_modules/.bin/eslint modules/kb_api/v2/downloader.js
```

### 测试
```bash
# 运行所有测试
./node_modules/.bin/mocha test/

# 运行特定测试文件
./node_modules/.bin/mocha test/homework.test.js

# 运行测试需要配置环境变量 NODE_ENV 和 NODE_PORT
NODE_ENV=test NODE_PORT=3000 ./node_modules/.bin/mocha test/homework.test.js
```

## 架构说明

### 请求处理流程
1. **app.js** - Express 应用入口，初始化中间件和路由
2. **routes/** - 路由层，定义 API 端点，调用对应的业务模块
3. **modules/** - 业务逻辑层，处理具体业务
4. **modules/utils/** - 工具层，提供数据库连接、缓存、日志等基础服务

### 核心模块架构
```
/modules/
├── tiku_api/      # 题库核心 API，处理题目相关操作
├── exam_api/      # 考试管理，处理考试相关业务
├── exampaper_api/ # 试卷管理，试卷的创建、编辑、发布
├── homework_api/  # 作业管理，作业布置、提交、批改
├── user_api/      # 用户系统，认证、权限、用户信息管理
├── client/        # 外部服务客户端封装
│   ├── casClient.js    # CAS 单点登录
│   ├── hfs.js          # HFS 文件服务
│   ├── wechat.js       # 微信支付/登录
│   └── yjClient.js     # 云小阅卷服务
├── middlewares/   # Express 中间件
│   ├── auth.js         # JWT/CAS 认证
│   ├── response_wrapper.js # 统一响应格式
│   └── verify.js       # 参数验证
└── utils/         # 基础工具
    ├── mongodber.js    # MongoDB 连接池管理
    ├── rediser.js      # Redis 客户端
    └── logger.js       # log4js 日志系统
```

### 数据库架构
- **MongoDB**: 主数据库，使用连接池管理
  - `tiku` - 主库，存储题目、试卷、用户等核心数据
  - `tiku_open` - 开放数据库，存储用户生成内容
- **Redis**: 缓存系统，用于会话、临时数据、高频访问数据

### API 版本管理策略
- `/v1/*` - 旧版本 API，保持向后兼容
- `/v2/*` - 当前版本 API，新功能在此开发
- 路由文件按版本组织：`routes/模块名/v1.js`, `routes/模块名/v2.js`

### 认证与授权流程
1. **JWT Token 认证**（主要方式）
   - 通过 `modules/middlewares/auth.js` 中间件验证
   - Token 存储在请求头的 `Authorization` 字段
2. **CAS 单点登录**
   - 通过 `modules/client/casClient.js` 集成
   - 用于企业/学校统一认证场景

## 开发注意事项

### 环境配置管理
- 使用 `config` 包，根据 `NODE_ENV` 自动加载对应配置
- 配置文件位于 `config/` 目录
- 敏感信息不要提交到代码库

### 统一响应格式
```javascript
// 成功响应
{
  ok: true,
  data: { ... }
}

// 错误响应
{
  ok: false,
  errCode: "ERROR_CODE",
  error: "错误描述"
}
```

### 数据库操作模式
```javascript
// 获取数据库连接
const db = mongodber.use('tiku');

// 使用集合
const users = db.collection('user');
const result = await users.findOne({ _id: userId });
```

### 日志记录规范
- 使用 `Logger` 对象记录日志
- 包含请求追踪 ID (SN) 便于问题排查
- 敏感信息（密码等）自动过滤

### 新增功能开发流程
1. 在 `modules/` 对应模块中实现业务逻辑
2. 在 `routes/` 中定义路由，调用业务模块
3. 使用 `modules/middlewares/` 中的中间件处理认证、验证等
4. 更新 `docs/操作记录/` 记录修改内容

### 最近的重要更新
- **推荐数据日志功能**（2025-01-21）
  - 在 `reco_data_log` 表记录搜索接口的请求和响应
  - 涉及接口：`/kb_api/v2/questions/by_search`, `/kb_api/v2/exampapers/by_search`
  - 使用 `buildRecoLogData` 函数统一处理日志数据

### 中文注释规范
项目使用中文注释，新增代码应保持一致的中文注释风格，清晰说明功能和业务逻辑。

## 工作流程

### 文档管理体系

项目使用 `docs/` 目录进行文档管理，结构如下：

```
docs/
├── 操作汇总.md       # 汇总每日操作的概览文档
├── 待办事项.md       # 项目任务管理，分优先级记录
├── 项目结构.md       # 项目架构和目录结构详细说明
└── 操作记录/         # 按日期组织的详细操作日志
    └── YYYY/         # 年份目录
        └── MM/       # 月份目录
            └── DD.md # 具体日期的操作记录
```

### 文档使用规范

1. **操作记录**（`docs/操作记录/YYYY/MM/DD.md`）
   - 每日工作结束后创建或更新当天的操作记录
   - 记录具体的代码修改、功能实现、问题解决等
   - 包含操作时间、任务描述、具体步骤、完成情况

2. **操作汇总**（`docs/操作汇总.md`）
   - 按月份汇总每日的主要操作
   - 作为快速查看历史工作的索引
   - 只记录关键操作，详细内容链接到对应的操作记录

3. **待办事项**（`docs/待办事项.md`）
   - 分为高、中、低优先级管理任务
   - 标记任务状态：待办 [ ]、进行中 [~]、已完成 [x]
   - 定期更新和整理，移除过时任务

4. **项目结构**（`docs/项目结构.md`）
   - 详细描述项目的目录结构和模块功能
   - 新增重要模块或目录时及时更新
   - 帮助新成员快速了解项目架构

### 文档维护流程

**重要提醒**: 每次操作完成后，必须立即更新相关文档，确保文档与代码同步。

1. 开始工作前：
   - 查看 `待办事项.md` 确定今日任务
   - 查看 `操作汇总.md` 了解近期进展

2. 工作过程中：
   - 重要决策和修改及时记录
   - 遇到新问题立即添加到待办事项

3. **每次操作完成后（必须执行）**：
   - 更新或创建当天的操作记录（`/docs/操作记录/YYYY/MM/DD.md`）
   - 更新待办事项状态（完成的标记 ✅，新增的添加进去）
   - 重要操作添加到操作汇总
   - 如有新模块或重要变更，更新项目结构文档

4. 提交代码前：
   - 确认所有文档已更新
   - 文档变更与代码变更一起提交
