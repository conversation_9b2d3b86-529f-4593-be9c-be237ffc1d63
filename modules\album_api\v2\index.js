const _ = require('lodash');
const ObjectId = require("mongodb").ObjectId;
const mongodber = require('../../utils/mongodber');
const db = mongodber.use('tiku');
const ResponseWrapper = require('../../middlewares/response_wrapper');
const logger = require('../../utils/logger');
const enums = require('../../../bin/enum');
const Joi = require('@hapi/joi');
const client = require('../../client');
const constants = require('../../utils/constants');

const coll_resource_album = db.collection(constants.schema.resource_album);
const coll_resource_album_data = db.collection(constants.schema.resource_album_data);
const coll_resource_album_summary = db.collection(constants.schema.resource_album_summary);

const JOI_ID = Joi.object({
   id: Joi.string().required()
});

async function getAlbumSummary(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const {id } = await JOI_ID.validate(req.params);
        const summary = await coll_resource_album_summary.findOne({_id: new ObjectId(id)});
        if (_.isEmpty(summary) || !summary.valid) return responseWrapper.error('PARAMETERS_ERROR', '数据不存在');
        summary.id = summary._id.toString();
        summary.ctime = summary.ctime.getTime();
        summary.utime = summary.utime.getTime();
        delete summary._id;
        delete summary.valid;
        const ds = { $set: {utime: new Date()}, $inc: { view_times: 1 } };
        await coll_resource_album_summary.updateOne({_id: new ObjectId(id)}, ds);
        return responseWrapper.succ(summary);
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

async function getAlbum(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const {id } = await JOI_ID.validate(req.params);
        const album = await coll_resource_album.findOne({_id: new ObjectId(id)});
        if (_.isEmpty(album) || !album.valid) return responseWrapper.error('PARAMETERS_ERROR', '数据不存在');
        album.id = album._id.toString();
        album.ctime = album.ctime.getTime();
        album.utime = album.utime.getTime();
        delete album._id;
        delete album.valid;
        return responseWrapper.succ(album);
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const JOI_DATA_ID = Joi.object({
    id: Joi.string().required(),
    data_id: Joi.string().required()
});

async function getAlbumData(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const { id, data_id } = await JOI_DATA_ID.validate(req.params);
        const album = await coll_resource_album.findOne({_id: new ObjectId(id)});
        if (_.isEmpty(album) || !album.valid) return responseWrapper.error('PARAMETERS_ERROR', '专辑不存在');
        // 校验节点是否可以查询数据
        const chapter_ids = getQueryChapter(album, data_id);
        if (!_.size(chapter_ids)) return responseWrapper.error('PARAMETERS_ERROR', '专辑节点不可查询');
        const list = await coll_resource_album_data.find({_id: {$in: chapter_ids.map(e => new ObjectId(e)) } }).toArray();
        const result = [];
        for (const chapter_id of chapter_ids) {
            const data = list.find(e => e._id.toString() === chapter_id);
            if (!data) continue;
            data.id = data._id.toString();
            data.ctime = data.ctime.getTime();
            data.utime = data.utime.getTime();
            delete data._id;
            delete data.valid;
            // 系统资源统一加载
            await buildSystemResource(data);
            data.view_times = (data.view_times || 0) + 1;
            result.push(data);
        }
        // 增加浏览量
        await coll_resource_album.updateOne({_id: new ObjectId(id)}, { $set: {utime: new Date()}, $inc: { view_times: _.size(chapter_ids) } });
        await coll_resource_album_data.updateMany({_id: {$in: chapter_ids.map(e => new ObjectId(e)) }}, { $set: {utime: new Date()}, $inc: { view_times: 1 } });
        // 获取系统数据并
        return responseWrapper.succ(result);
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

function getQueryChapter(album, data_id) {
    const result = [];
    const getChild = (list, data_id, child = false) => {
        if (!_.size(list)) return;
        for (const chapter of list) {
            if (chapter.id === data_id || child) {
                if (chapter.type === 'album') result.push(chapter.id);
                if (chapter.is_query) getChild(chapter.children, data_id, true);
            } else {
                getChild(chapter.children, data_id);
            }
        }
    }
    getChild(album.children, data_id, false);
    return result;
}

async function buildSystemResource(data) {
    const id_map = {
        knowledge_ids: [],
        question_ids: [],
        exampaper_ids: [],
        edu_file_ids: [],
        edu_tool_ids: [],
        text_question_ids: []
    };

    getIds = (children) => {
        if (!_.size(children)) return;
        for (const chapter of children) {
            if (chapter.type === enums.AlbumResourceType.KNOWLEDGE) {
                for (const content of chapter.content || []) {
                    if (content.id) id_map.knowledge_ids.push(content.id);
                }
            } else if (chapter.type === enums.AlbumResourceType.QUESTION) {
                for (const content of chapter.content || []) {
                    if (content.id) id_map.question_ids.push(content.id);
                }
            } else if (chapter.type === enums.AlbumResourceType.EXAMPAPER) {
                for (const content of chapter.content || []) {
                    if (content.id) id_map.exampaper_ids.push(content.id);
                }
            } else if (chapter.type === enums.AlbumResourceType.EDU_FILE) {
                for (const content of chapter.content || []) {
                    if (content.id) id_map.edu_file_ids.push(content.id);
                }
            } else if (chapter.type === enums.AlbumResourceType.EDU_TOOL) {
                for (const content of chapter.content || []) {
                    if (content.id) id_map.edu_tool_ids.push(content.id);
                }
            } else if (chapter.type === enums.AlbumResourceType.TEXT_QUESTION) {
                for (const content of chapter.content || []) {
                    if (content.id) id_map.text_question_ids.push(content.id);
                }
            }
            getIds(chapter.children);
        }
    };
    getIds(data.children);
    // 获取系统资源
    const value_map = {};
    if (_.size(id_map.knowledge_ids)) {
        value_map.knowledges = await client.kb.getKnowledgeByIds(id_map.knowledge_ids);
    }
    if (_.size(id_map.question_ids)) {
        value_map.questions = await client.kb.getQuestions(id_map.question_ids);
        const fileds = ['id', 'elite', 'subject', 'period', 'description', 'comment', 'blocks', 'knowledges', 'difficulty', 'type', 'score', 'refer_exampapers', 'year', 'ctime', 'utime'];
        for (const index in value_map.questions) {
            const q = value_map.questions[index];
            if (q.blocks) {
                q.blocks = {
                    stems: q.blocks.stems,
                    knowledges: q.blocks.knowledges,
                    types: q.blocks.types,
                }
            }
            value_map.questions[index] = _.pick(q, fileds);
        }
    }
    if (_.size(id_map.exampaper_ids)) {
        value_map.exampapers = await client.kb.getExampapers(id_map.exampaper_ids);
    }
    if (_.size(id_map.edu_file_ids)) {
        value_map.edu_files = await client.kb.getEduFileByIds(id_map.edu_file_ids);
        for (const data of value_map.edu_files) {
            delete data.url;
            delete data.host;
        }
    }
    if (_.size(id_map.edu_tool_ids)) {
        value_map.edu_tools = await client.kb.getEduToolByIds(id_map.edu_tool_ids);
        for (const data of value_map.edu_tools) {
            delete data.url;
            delete data.host;
        }
    }
    if (_.size(id_map.text_question_ids)) {
        value_map.text_questions = await client.kb.getQuestions(id_map.text_question_ids);
    }
    const handler = (children) => {
        if (!_.size(children)) return;
        for (const chapter of children) {
            if (chapter.type === enums.AlbumResourceType.KNOWLEDGE) {
                for (const content of chapter.content || []) {
                    if (content.id) content.value = (value_map.knowledges || []).find(e => e.id === content.id);
                }
            } else if (chapter.type === enums.AlbumResourceType.QUESTION) {
                for (const content of chapter.content || []) {
                    if (content.id) content.value = (value_map.questions || []).find(e => e.id === content.id);
                }
            } else if (chapter.type === enums.AlbumResourceType.EXAMPAPER) {
                for (const content of chapter.content || []) {
                    if (content.id) content.value = (value_map.exampapers || []).find(e => e.id === content.id);
                }
            } else if (chapter.type === enums.AlbumResourceType.EDU_FILE) {
                for (const content of chapter.content || []) {
                    if (content.id) content.value = (value_map.edu_files || []).find(e => e.id === content.id);
                }
            } else if (chapter.type === enums.AlbumResourceType.EDU_TOOL) {
                for (const content of chapter.content || []) {
                    if (content.id) content.value = (value_map.edu_tools || []).find(e => e.id === content.id);
                }
            } else if (chapter.type === enums.AlbumResourceType.TEXT_QUESTION) {
                for (const content of chapter.content || []) {
                    if (content.id) content.value = (value_map.text_questions || []).find(e => e.id === content.id);
                }
            }
            handler(chapter.children);
        }
    }
    handler(data.children);
}

const JOI_DOWNLOAD = Joi.object({
    id: Joi.string().optional(),
    album_id: Joi.string().required(),
    data_id: Joi.string().required()
});

async function download(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const { id, album_id, data_id } = await JOI_DOWNLOAD.validate(req.body);
        const ds = { $set: {utime: new Date()}, $inc: { download_times: 1 } };
        if (id) {
            await coll_resource_album_summary.updateOne({_id: ObjectId(id)}, ds);
        }
        await coll_resource_album.updateOne({_id: ObjectId(album_id)}, ds);
        await coll_resource_album_data.updateOne({_id: ObjectId(data_id)}, ds);
        return responseWrapper.succ('');
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

module.exports = {
    getAlbumSummary,
    getAlbum,
    getAlbumData,
    download,
}
