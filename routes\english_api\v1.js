var express = require('express');
var router = express.Router();
var enAPI = require('../../modules/english_api/v1/index.js');
var apikey = require('../../modules/middlewares/apikey');

// kb api key middleware
router.use(apikey('KB'));

router.post('/speaking/scores/', enAPI.addScores);
router.get('/speaking/book/', enAPI.getSpeakBook);
router.get('/speaking/reco/', enAPI.getSpeakingReco);
router.put('/speaking/book/', enAPI.updateSpeakBookProfile);
router.put('/speaking/book/trace/', enAPI.updateBookTrace);

module.exports = router;
