let express = require('express');
let router = express.Router();
const filter = require('../../modules/utils/filters/index');
const yuejuan = require('../../modules/tiku_api_v1/v1/yuejuan');
const exampaper = require('../../modules/tiku_api_v1/v1/exampaper');
const tikuManagement = require('../../modules/tiku_api_v1/v1/tikuManagement');


router.post('/yuejuan/schools', yuejuan.postSchoolInfo);
router.put('/exampaper/:id/status', exampaper.putShareExampaper);
router.get('/exampaper/list', exampaper.getShareExampaper);
router.get('/school_info/:id/info', tikuManagement.getSchoolInfoById);
router.get('/school_info/list', tikuManagement.getSchoolList);
router.put('/school_info/:id', tikuManagement.putSchoolInfo);
router.get('/members/:id/info', tikuManagement.getMemberInfoById);
router.put('/members/:id', filter.member.checkMemberData, tikuManagement.putMemberInfo);
router.post('/members/', filter.member.checkMemberData, tikuManagement.postMemberInfo);
router.get('/members/list', tikuManagement.getMemberList);
// router.get('/exampaper_unit/time', exampaper.getExampaperUnitTime);

module.exports = router;

