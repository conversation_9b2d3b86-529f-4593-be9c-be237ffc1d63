var config  = require('config');
var request = require('request');
var rediser = require('../utils/rediser');
var logger = require('../utils/logger');
var aes = require('../utils/aes');
var jwt = require('../utils/jwt');
const enums = require('../../bin/enum');

const URL = require('url');
const ETIMEOUT = 1500;

'use strict';

function vipName(userId){
	return `tiku:vip:${userId}`;
}

function accessControlWithRedis(req, res, next){
	var user = req.user;
	var userId = user.id;

	if(user.role === '教师'){
		req.isVip = true;
		return next();
	}else{
		rediser.get(vipName(userId), function(err, value){
			if(value === 'true'){
				req.isVip = true;
				return next();
			}else if(value === 'false'){
				req.isVip = false;
				return next();
			}else{
				var HFS_VIP_SERVER = config.get('HFS_VIP_SERVER');

				var url = URL.format({
					protocol: HFS_VIP_SERVER.protocol,	
					hostname: HFS_VIP_SERVER.hostname,
					port: HFS_VIP_SERVER.port,
					pathname: `/v1/students/${userId}/member-status`
				});

				request({
					url: url,
					timeout: ETIMEOUT 
				}, function(err, response, body){
					if(err && err.code == 'ETIMEDOUT'){
						logger.error(url+'响应超时，请联系刘静紧急修复：186-2227-2119');
						req.isVip = false;
						rediser.set(vipName(userId),
							'false', 
							60 * 3, 
							function(err){
								if(err){
									logger.error(err.message);
								}
								return next();
							});
					}

					body = JSON.parse(body);
					if(body.code === 0 && body.data && body.data.isMember){
						req.isVip = true;
						rediser.set(vipName(userId),
							'true', 
							3600 * 6, 
							function(err){
								if(err){
									logger.error(err.message);
								}
								return next();
							});
					}else{
						req.isVip = false;
						rediser.set(vipName(userId),
							'false', 
							60 * 3, 
							function(err){
								if(err){
									logger.error(err.message);
								}
								return next();
							});
					}
				});
			}
		});
	}
}

function accessControlWithCookie(req, res, next){

	if(req.url.indexOf('dmp_api') !== -1){
		return next();
	}

	var user = req.user;
	var userId = user.id;
	var cookie = config.get('TIKU_SERVER').isVipCookie;
	const cookieName = cookie.name;

	if(user.role === '教师'){
		req.isVip = true;
		return next();
	}else{
		var isMember = req.cookies[cookieName];
		if(isMember){
			try{
				var isMemberObj = jwt.decode(aes.decript(isMember));
				if(isMemberObj.isMember){
					req.isVip = true;
					req.user.isVip = true;
					req.user.vipType = enums.MemberType.HFS_360;
					req.user.startTime = isMemberObj.startTime;
					req.user.expiredTime = isMemberObj.expiredTime;
					return next();
				}else{
					req.isVip = false;
					return next();
				}
			}catch(err){
				// 如果非token失效的异常，则使用旧逻辑，直接设置用户为VIP
				// 否则，不处理，执行下方的代码，进行重新设置token
				if (err.message !== 'token has expired') {
					req.isVip = true;
					return next();
				}
			}
		}

		var HFS_VIP_SERVER = config.get('HFS_VIP_SERVER');

		var url = URL.format({
			protocol: HFS_VIP_SERVER.protocol,	
			hostname: HFS_VIP_SERVER.hostname,
			port: HFS_VIP_SERVER.port,
			pathname: `/v1/students/${userId}/member-status`
		});

		request({
			url: url,
			timeout: ETIMEOUT 
		}, function(err, response, body){

			var options = {
				path: cookie.options.path,
				domain: cookie.options.domain
			};

			var cookieValue = {
				userId: userId
			};

			if(err && err.code == 'ETIMEDOUT'){
				logger.error(url+'响应超时，请联系刘静紧急修复：186-2227-2119');
				req.isVip = true;
				return next();
			}

			try{
				if(response.statusCode !== 200){
					req.isVip = true;
					return next();
				}

				body = JSON.parse(body);
				var cvalue = '';
				if(body.code === 0 && body.data && body.data.isMember){
					req.isVip = true;
					req.user.isVip = true;
					req.user.vipType = enums.MemberType.HFS_360;
					req.user.startTime = new Date(body.data.memberStart).getTime();
					req.user.expiredTime = new Date(body.data.memberEnd).getTime();
					options.maxAge = cookie.options.maxAgeVip;
					cookieValue.exp = new Date(Date.now() + cookie.options.maxAgeVip);
					cookieValue.isMember = true;
					cookieValue.vipType = enums.MemberType.HFS_360;
					cookieValue.startTime = new Date(body.data.memberStart).getTime();
					cookieValue.expiredTime = new Date(body.data.memberEnd).getTime();
				}else{
					req.isVip = false;
					options.maxAge = cookie.options.maxAgeNvip;
					cookieValue.exp = new Date(Date.now() + cookie.options.maxAgeNvip);
					cookieValue.isMember = false;
				}

				cvalue = aes.encript(jwt.encode(cookieValue));
				res.cookie(cookie.name, cvalue, options);
			}catch(err){
				req.isVip = true;
				logger.error('');
			}
			return next();
		});
	}
}

var middlewareConfig = {
	cookie: accessControlWithCookie,
	redis: accessControlWithRedis,
};

function accessControl(type){
	return middlewareConfig[type];
}

module.exports = accessControl;
