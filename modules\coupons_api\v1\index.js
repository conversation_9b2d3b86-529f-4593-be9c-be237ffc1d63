let mongodber = require('../../utils/mongodber');
let ResponseWrapper = require('../../middlewares/response_wrapper');
let logger = require('../../utils/logger');
let enums = require('../../../bin/enum');
const rediser = require('../../utils/rediser');
const { ObjectId } = require('mongodb');
const _ = require('lodash');
const Joi = require('@hapi/joi');
let db = mongodber.use('tiku');
const moment = require('moment');
const SCHEMA = 'coupons';


const rules = [
    {
        key: 'user_vip',
        key_desc: '用户会员',
        val_type: 'boolean',
        val: ['true', 'false'],
        operator: ['eq']
    },{
        key: 'user_reg_day_num',
        key_desc: '用户注册天数',
        val_type: 'number',
        val: ['number'],
        operator: ['gte', 'lte', 'lt', 'gt', 'eq']
    },{
        key: 'user_vip_out_day_num',
        key_desc: '会员过期天数',
        val_type: 'number',
        val: ['number'],
        operator: ['gte', 'lte', 'lt', 'gt', 'eq']
    }
];

const RULE_KEY = {
    user_vip: 'user_vip',
    user_reg_day_num: 'user_reg_day_num',
    user_vip_out_day_num: 'user_vip_out_day_num',
    schools: 'schools',
}
// 获取可领取的优惠券列表
const getList = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        // 查询发放中的列表
        const nowDate = new Date();
        const cond = {
            send_from: { $lt: nowDate },
            send_to: { $gte: nowDate },
            shelf_status: 2, // 已发布
            del_status: 0, // 未删除
        };
        let resObj = {
            total: 0,
            list: []
        };
        let list = await db.collection(SCHEMA).find(cond).sort({ ctime: -1 }).toArray();
        if (!_.size(list)) {
            return responseWrapper.succ(resObj);
        }
        const reqUser = req.user || {};
        const userInfo = await db.collection('user').findOne({ _id: req.user.userId });
        // 获取可领取的列表
        list = list.filter(it => _checkCouponRules(reqUser, userInfo, it));
        if (!_.size(list)) return responseWrapper.succ(resObj);
        // 过滤用户已领取的
        const getList = await db.collection('user_coupons').find({user_id: reqUser.userId}, {_id: 1, coupon_id: 1}).toArray();
        if (_.size(getList)) {
            const ids = getList.map(it => it.coupon_id);
            list = list.filter(it => !ids.includes(it._id.toString()));
            if (!_.size(list)) return responseWrapper.succ(resObj);
        }
        // 返回结果
        resObj.total = _.size(list);
        resObj.list = list.map(it => {
            const obj = {
                id: it._id.toString(),
                name: it.name,
                discount_type: it.discount_type, // 优惠方式 1-固定金额，2-折扣
                discount_count: it.discount_count, // 优惠数量 类型 1-减免金额，类型2-折扣率(1-100)
                target_goods: it.target_goods, // 定向商品
                valid_type: it.valid_type, // 有效类型 1-固定有效期，2-领取后计算
                valid_day: it.valid_day || 0, // 有效期天 0
                valid_from: it.valid_type === 1 ? it.valid_from.getTime() : 0,
                valid_to: it.valid_type === 1 ? it.valid_to.getTime() : 0,
                desc: it.desc, // 描述
                send_from: it.send_from.getTime(),
                send_to: it.send_to.getTime(),
                ctime: it.ctime.getTime(),
                utime: it.utime.getTime()
            };
            return obj;
        });
        return responseWrapper.succ(resObj);
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

const GET_MY_LIST = Joi.object({
    limit: Joi.number().required(),
    offset: Joi.number().required(),
    status: Joi.number().required().min(1).max(3),
}).unknown(true);
/**
 * 获取我的优惠券列表
 * @param req
 * @param res
 * @returns {Promise<void>}
 */
const getMyList = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let { limit, offset, status } = await Joi.validate(req.query, GET_MY_LIST);
        const user = req.user;
        const userId = user.userId;
        let resObj = {
            total: 0,
            list: []
        };
        const cond = { user_id: userId};
        if (status === enums.CouponsStatus.NORMAL) {
            cond.usage_status = 0;
            cond.valid_to = {
                $gt: new Date()
            }
        } else if (status === enums.CouponsStatus.USED) {
            cond.usage_status = 1;
        }  else if (status === enums.CouponsStatus.OUT)  {
            cond.usage_status = 0;
            cond.valid_to = {
                $lt: new Date()
            }
        }
        let total = await db.collection('user_coupons').count(cond);
        let data = await db.collection('user_coupons').find(cond).sort({ ctime: -1 }).skip(offset).limit(limit).toArray();
        if (!_.size(data)) return responseWrapper.succ(resObj);

        data.forEach(it => {
            const obj = {
                id: it._id.toString(),
                coupon_id: it.coupon_id, // 优惠券ID
                coupon_name: it.coupon_name, // 优惠券名称
                discount_type: it.discount_type, // 优惠方式 1-固定金额，2-折扣
                discount_count: it.discount_count, // 优惠数量 类型 1-减免金额，类型2-折扣率(1-100)
                target_goods: it.target_goods, // 定向商品(为空的话不限制使用商品)
                desc: it.desc, // 说明描述
                usage_status: it.usage_status, // 是否使用 0-否，1-是 2-使用中
                usage_time: it.usage_time ? it.usage_time.getTime() : 0, // 使用时间
                valid_from: it.valid_from.getTime(),
                valid_to: it.valid_to.getTime(),
                ctime: it.ctime.getTime(),
                utime: it.utime.getTime()
            }
            resObj.list.push(obj);
        });
        resObj.total = total;
        return responseWrapper.succ(resObj);
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
}

// 领取优惠券
const getToUser = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const reqUser = req.user;
        const userId = reqUser.userId;
        const couponId = req.params.id;
        // 是否已领取过
        let userCoupon = await db.collection('user_coupons').findOne({user_id: userId, coupon_id: couponId});
        if (!_.isEmpty(userCoupon)) {
            throw new Error('不可重复领取!');
        }
        const nowDate = new Date();
        const coupon = await db.collection('coupons').findOne({
            _id: new ObjectId(couponId),
            del_status: 0, // 未删除
            shelf_status: 2, // 上架
            send_from: { $lte: nowDate },
            send_to: { $gt: nowDate },
        });
        if (_.isEmpty(coupon)) {
            throw new Error('优惠券信息不存在!');
        }
        // 检查领取条件
        let userInfo = await db.collection('user').findOne({ _id: userId });
        if (!_checkCouponRules(reqUser, userInfo, coupon)) {
            throw new Error('不满足领取条件!');
        }
        // 发放优惠券
        userCoupon = {
            user_id: +userId, // 用户ID
            coupon_id: coupon._id.toString(), // 优惠券ID
            coupon_name: coupon.name, // 优惠券名称
            discount_type: coupon.discount_type, // 优惠方式 1-固定金额，2-折扣
            discount_count: coupon.discount_count, // 优惠数量 类型 1-减免金额，类型2-折扣率(1-100)
            target_goods: coupon.target_goods, // 定向商品(为空的话不限制使用商品)
            valid_from: coupon.valid_from, // 有效期开始时间
            valid_to: coupon.valid_to, // 有效期结束时间
            desc: coupon.desc, // 说明描述
            usage_status: enums.BooleanNumber.NO, // 是否使用 0-否，1-是
            ctime: nowDate,
            utime: nowDate,
        };
        // 领取后计算有效期
        if (coupon.valid_type === enums.CouponValidType.GET) {
            userCoupon.valid_from = new Date();
            userCoupon.valid_to = moment(userCoupon.valid_from).add(coupon.valid_day, 'day').toDate();
        }
        const insertResult = await db.collection('user_coupons').insertOne(userCoupon);
        // 修改优惠券发放数量
        // const redis_lock_key = `coupons_send_lock:${couponId}`;
        // const lock = await rediser.lock(redis_lock_key, '1', 5);
        // if (lock) {
        //     try {
        //         await db.collection('coupons').updateOne({ _id: coupon._id }, { $inc:{ send_count: 1 } });
        //     } finally {
        //         await rediser.unlock(redis_lock_key, '1');
        //     }
        // }
        await db.collection('coupons').updateOne({ _id: coupon._id }, { $inc:{ send_count: 1 } });
        // 返回结果
        const resObj = {
            id: insertResult.insertedId.toString(),
            coupon_id: coupon._id.toString(), // 优惠券ID
            coupon_name: coupon.name, // 优惠券名称
            discount_type: coupon.discount_type, // 优惠方式 1-固定金额，2-折扣
            discount_count: coupon.discount_count, // 优惠数量 类型 1-减免金额，类型2-折扣率(1-100)
            target_goods: coupon.target_goods, // 定向商品(为空的话不限制使用商品)
            valid_from: userCoupon.valid_from, // 有效期开始时间
            valid_to: userCoupon.valid_to, // 有效期结束时间
            desc: coupon.desc, // 说明描述
            usage_status: enums.BooleanNumber.NO, // 是否使用 0-否，1-是
            usage_time: 0,
            ctime: userCoupon.ctime.getTime(),
            utime: userCoupon.utime.getTime(),
        };
        return responseWrapper.succ(resObj);
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
}

/**
 * 校验优惠券规则是否满足
 * @param reqUser
 * @param userInfo
 * @param coupon
 * @returns {boolean|*}
 * @private
 */
function _checkCouponRules(reqUser, userInfo, coupon) {
    if (!_.size(coupon.send_rules)) return true;
    const now = new Date();
    return coupon.send_rules.every(r => {
        let userVal = null;
        switch (r.key) {
            case RULE_KEY.user_vip:
                userVal = !!reqUser.isVip;
                break;
            case RULE_KEY.user_reg_day_num:
                if (userInfo.ctime) {
                    userVal = moment(now).diff(userInfo.ctime, 'day');
                }
                break;
            case RULE_KEY.user_vip_out_day_num:
                if (userInfo.expired_time && now.getTime() > userInfo.expired_time.getTime()) {
                    userVal = moment(now).diff(userInfo.expired_time, 'day');
                }
                break;
            case RULE_KEY.schools:
                userVal = reqUser.schoolId || 0;
                break;
            default:
                userVal = null;
        }
        if (!_.isNil(userVal)) {
            return _checkRule(r, userVal);
        }
        return false;
    });
}

/**
 * 检查规则
 * @param rule    规则
 * @param userVal 用户比对信息
 * @returns {boolean}
 * @private
 */
function _checkRule(rule, userVal) {
    const operator = rule.operator.val;
    let result;
    switch (operator) {
        case 'eq':
            result = rule.val.every(e => String(userVal) === e.val);
            break;
        case 'gt':
            result = rule.val.every(e => userVal > Number(e.val));
            break;
        case 'gte':
            result = rule.val.every(e => userVal >= Number(e.val));
            break;
        case 'lt':
            result = rule.val.every(e => userVal < Number(e.val));
            break;
        case 'lte':
            result = rule.val.every(e => userVal <= Number(e.val));
            break;
        case 'in':
            result = !!rule.val.find(e => e.val === String(userVal));
            break;
        default:
            result = false;
    }
    return result;
}

module.exports = {
    getList,
    getMyList,
    getToUser,
}
