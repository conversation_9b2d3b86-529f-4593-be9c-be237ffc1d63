const mongodber = require('../../utils/mongodber');
const ResponseWrapper = require('../../middlewares/response_wrapper');
const logger = require('../../utils/logger');
const enums = require('../../../bin/enum');
const db = mongodber.use('tiku');
const db_open = mongodber.use('tiku_open');
const ObjectId = require('mongodb').ObjectId;
const config = require('config');
const Joi = require('@hapi/joi');
const _ = require('lodash');
const client = require('../../client');
const rediser = require('../../utils/rediser');

const albumFilters = {
    '小学': [{
        name: '阶段测试',
        list: [
            '月考试卷', '期中试卷', '期末试卷'
        ],
        filter: {
            grade: ['一年级上', '一年级下', '二年级上', '二年级下', '三年级上', '三年级下', '四年级上', '四年级下', '五年级上', '五年级下', '六年级上', '六年级下'],
            year: [2023, 2022, 2021, 2020, 2019, 2018, 2017, 2016, 2015, 2014],
        }
    }
    ],
    '初中': [{
        name: '阶段测试',
        list: [
            '月考试卷', '期中试卷', '期末试卷'
        ],
        filter: {
            grade: ['七年级上','七年级下','八年级上','八年级下','九年级上','九年级下'],
            year: [2023, 2022, 2021, 2020, 2019, 2018, 2017, 2016, 2015, 2014],
        }
    }, {
        name: '中考专题',
        list: [
            '中考模拟', '中考真卷'
        ],
        filter: {
            year: [2023, 2022, 2021, 2020, 2019, 2018, 2017, 2016, 2015, 2014],
        }
    }],
    '高中': [{
        name: '阶段测试',
        list: [
            '月考试卷', '期中试卷', '期末试卷'
        ],
        filter: {
            grade: ['高一上','高一下','高二上','高二下','高三上','高三下'],
            year: [2023, 2022, 2021, 2020, 2019, 2018, 2017, 2016, 2015, 2014],
        }
    },
    {
        name: '高考专题',
        list: [
            '高考模拟', '高考真卷'
        ],
        filter: {
            year: [2023, 2022, 2021, 2020, 2019, 2018, 2017, 2016, 2015, 2014],
        }
    }]
};

const getExampaperAlbumFiltersSchema = Joi.object({
    period: Joi.string().required(),
}).unknown(true);

const getExampaperAlbumFilters = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let { period } = await Joi.validate(req.query, getExampaperAlbumFiltersSchema);
        const retObj = albumFilters[period] || [];

        return responseWrapper.succ(retObj);
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

const GET_EXAMPAPER_DOWNLOAD = Joi.object({
    id: [Joi.number().required(), Joi.string().required()],
    resource_type: Joi.string().optional()
}).unknown(true);

async function download(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let { id, resource_type } = await Joi.validate(_.assign(req.params, req.query), GET_EXAMPAPER_DOWNLOAD);
        let paper = null;
        const user_id = req.user.id;
        if (resource_type === enums.TopicResourceType.EXAM_CONSOLIDATE) { // 考后巩固
            const arr = id.split('-');
            const exam = await db.collection('exam_teacher_paper').findOne({user_id: user_id, paper_id: `${arr[0]}-${arr[1]}`});
            const exam_paper = (exam && exam.papers || []).find(e => e.category === Number(arr[2]));
            if (exam_paper) id = exam_paper.id;
        }
        if (_.isNumber(id)) {
            paper = await client.kb.getExampaper(id);
        } else {
            const doc = await db_open.collection(enums.OpenSchema.user_paper).findOne({_id: new ObjectId(id)});
            if (!_.isEmpty(doc)) {
                paper = {
                    id: doc._id.toString(),
                    name: doc.name,
                    type: doc.type,
                    period: doc.period || '',
                    subject: doc.subject || '',
                    grade: doc.grade || '',
                    from_year: doc.from_year || '',
                    to_year: doc.to_year || '',
                    province: doc.province || '',
                    city: doc.city || '',
                    score: doc.score || '',
                    blocks: []
                }
                const que_ids = [];
                for (const volume of doc.volumes) {
                    for (const b of volume.blocks) {
                        que_ids.push(...b.questions.map(e => e.id));
                    }
                }
                const questions = await client.kb.getQuestions(que_ids);
                for (const volume of doc.volumes) {

                    for (const b of volume.blocks) {
                        const block = _.assign({}, b);
                        block.questions = [];
                        paper.blocks.push(block);
                        for (const q of b.questions) {
                            const que = questions.find(e => e.id === q.id);
                            if (!que) continue;
                            block.questions.push(que);
                        }
                    }
                }
            }
        }
        if (_.isEmpty(paper)) {
            return responseWrapper.error('NULL_ERROR', '试卷不存在');
        }
        const response = await client.utilBox.downloadExampaper(paper);
        // res.set(response.headers);
        // response.data.pipe(res);
        let retval = await createMemoryDocx(response);
        return responseWrapper.succ({
            'url': retval
        });
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
}


const createMemoryDocx = async (response) => {
    try {
        let cd = response.headers['content-disposition'] ? response.headers['content-disposition'].split('=') : '';
        let filename = (cd.length >= 2) ? cd[1] : (new Date()).getTime() + '.docx'; //已经编码后安全字符
        const key = gen_key(decodeURIComponent(filename));
        const value = response.data.toString('base64');
        await rediser.set(key, value, 120);
        // const serv = config.get('TIKU_SERVER');
        // return serv.webUrl + '/kb_api/v2/docx/' + filename;
        return '/kb_api/v2/docx/' + filename;
    } catch (err) {
        throw err;
    }
}

function gen_key(value) {
    return 'kb:docx:' + value;
}

module.exports = {
    getExampaperAlbumFilters,
    download
};
