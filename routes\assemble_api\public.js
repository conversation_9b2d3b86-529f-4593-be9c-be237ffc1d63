const config = require('config');
const express = require('express');
const router = express.Router();
const tw_specification = require('../../modules/assemble_api/v1/tw_specification');
const exampaper = require('../../modules/assemble_api/v1/exampaper');

const KBSERVCFG = config.get('KB_API_SERVER');

// kb api key middleware
router.use(function (req, res, next) {
    req.apiKey = KBSERVCFG.appKey;
    next();

});

router.get('/exampapers/:exampaper_id/tw_specification', tw_specification.getTableFromExampaper);
// router.post('/tw_specifications/exampapers', tw_specification.createExampaperAsync);
router.post('/tw_specifications/knowledge/', tw_specification.getUnableKnowledge);
router.get('/tw_specifications/hot', tw_specification.getHotTable);
router.get('/tw_specifications/:table_id/info', tw_specification.getTableInfo);

router.post('/knowledge/question_num/', exampaper.getQuestionNumByKnowledge);

module.exports = router;
