# 操作记录规范

本文档规定了操作记录的编写规范，确保记录的一致性和可追溯性。

## 记录原则

1. **及时性**：每次重要操作完成后立即记录
2. **准确性**：记录真实、准确的操作内容
3. **完整性**：包含操作的目的、步骤、结果
4. **可追溯**：记录足够的细节以便后续查阅

## 文件组织

```
docs/
├── 操作汇总.md          # 所有操作的简要汇总
├── 待办事项.md          # 项目待办和已完成任务
├── 项目结构.md          # 项目架构说明
└── 操作记录/            # 详细操作日志
    └── 年份/
        └── 月份/
            └── 日期.md  # 每日详细操作记录
```

## 每日操作记录格式

### 文件名
- 格式：`日期.md`（如：`21.md` 表示21日）

### 内容结构
```markdown
# 年月日 操作记录

## 操作时间：YYYY-MM-DD

### 主要任务
简要描述当日的主要任务

### 具体操作

#### 1. 任务名称
- 操作步骤详细说明
- 涉及的文件和代码变更
- 遇到的问题及解决方案

#### 2. 任务名称
...

### 完成情况
- ✅ 已完成的任务
- ❌ 未完成的任务
- 🔄 进行中的任务

### 后续建议
对后续工作的建议和注意事项
```

## 操作汇总更新

每次操作后，需要在 `操作汇总.md` 中添加简要记录：
- 使用列表形式
- 按日期组织
- 重要操作可添加子列表说明

## 待办事项管理

在 `待办事项.md` 中：
- 新任务添加到相应优先级分类下
- 完成的任务移动到"已完成任务"区域
- 标注完成日期
- 保留历史记录

## 特殊操作记录

以下操作必须记录：
1. 数据库结构变更
2. 新增中间件或重要功能
3. 配置文件修改
4. 依赖包添加或更新
5. API 接口变更
6. 重要 bug 修复

## 代码示例记录

当记录涉及代码时：
- 使用代码块标注语言类型
- 只记录关键代码片段
- 说明代码的作用和位置

## 注意事项

1. 避免记录敏感信息（密码、密钥等）
2. 使用中文记录，保持语言一致性
3. 定期整理和归档旧记录
4. 重要操作可添加截图或日志片段