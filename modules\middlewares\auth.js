const Config = require('config');
const pathToRegexp = require('path-to-regexp');
const moment = require('moment');
const Logger = require('../utils/logger');
const rediser = require('../utils/rediser');
const ResponseWrapper = require('./response_wrapper');
const isMobileByUa = require('../utils/utils').isMobileByUa;
const ObjectId = require('mongodb').ObjectId;

// let VISIT_FREQ = Config.get('VISIT_FREQ');
// let VISIT_INTERFACE_LIMIT = Config.get('VISIT_INTERFACE_LIMIT');
// let VISIT_DEFAULT_LIMIT = Config.get('VISIT_DEFAULT_LIMIT');

//初始化 METHOD_PATHREG
// let METHOD_PATHREG = {};
// (function () {
//     let apis = VISIT_INTERFACE_LIMIT;

//     for (let k in apis) {
//         if (apis.hasOwnProperty(k)) {
//             let arr = apis[k].split(' ');
//             let method = arr[0].toUpperCase();
//             let reg = pathToRegexp(arr[1], []);

//             if (METHOD_PATHREG.hasOwnProperty(method)) {
//                 METHOD_PATHREG[method].push({
//                     'reg': reg,
//                     'api_name': k,
//                 });
//             } else {
//                 METHOD_PATHREG[method] = [{
//                     'reg': reg,
//                     'api_name': k,
//                 }];
//             }
//         }
//     }
// }());

// const getApiName = (data) => {
//     let method = data.method.toUpperCase();
//     let path = data.path;
//     let pathregs = METHOD_PATHREG[method];

//     for (let i in pathregs) {
//         if (pathregs.hasOwnProperty(i)) {
//             let desc = pathregs[i];
//             let m = desc.reg.exec(path);
//             if (m) {
//                 if (desc.api_name === 'exampaper_download_num' || desc.api_name === 'que_download_num') {
//                     if (data.body.resource_type === 'exampaper') {
//                         return 'exampaper_download_num';
//                     } else {
//                         return 'que_download_num'
//                     }
//                 }
//                 return desc.api_name;
//             }
//         }
//     }
//     return null;
// };

// async function value(redis, key) {
//     try {
//         let str = await redis.get(key);
//         let result = JSON.parse(str);
//         return result;
//     } catch (err) {
//         return null;
//     }
// }

// function durationMS(unit) {
//     return duration(unit) * 1000;
// }

// function duration(unit) {
//     if (unit === 'month') {
//         let day_num = moment().daysInMonth();
//         return day_num * 86400
//     }
//     if (unit === 'day') {
//         return 86400;
//     }
//     if (unit === 'hour') {
//         return 3600;
//     }
//     // default min
//     return 60;
// }

// async function key(redis, apiName, userId, unit, now) {
//     let result = {
//         time: new Date().toLocaleString(),
//         timestamp: parseInt(now / durationMS(unit)),
//         apis: {}
//     };
//     try {
//         let key = `visit:freq:${userId}:${unit}`;
//         let user = await value(redis, key);
//         if (!user) {
//             user = result;
//         }
//         if (user.timestamp < parseInt(now / durationMS(unit))) {
//             user = result;
//         }
//         if (!!user.apis[apiName] === false) {
//             user.apis[apiName] = 0;
//         }
//         user.apis[apiName] += 1;
//         return user;
//     } catch (err) {
//         result.apis[apiName] = 1;
//         return result;
//     }
// }

// const visitFreq = async (data, cb) => {
//     const mongodber = require('../utils/mongodber');
//     // const db = mongodber.use('tiku');
//     try {
//         // let schoolId = data.user.schoolId;
//         let userId = data.user.id;
//         let apiName = getApiName(data);
//         if (!apiName) {
//             return cb('');
//         }
//         let memberInfo = VISIT_DEFAULT_LIMIT;
//         // let schoolInfo = await db.collection('school_info').findOne({ _id: schoolId });
//         // if (schoolInfo && schoolInfo.member_id) {
//         //     let member = await db.collection('member').findOne({ _id: ObjectId(schoolInfo.member_id) });
//         //     if (member) {
//         //         memberInfo = member
//         //     }
//         // }

//         let now = Date.now();
//         let month = await key(rediser.redis, apiName, userId, 'month', now);
//         if (typeof memberInfo[apiName].month === 'number' && memberInfo[apiName].month > 0 && month.apis[apiName] > memberInfo[apiName].month) {
//             return cb('您的账号已达单月数量上限');
//         }
//         let day = await key(rediser.redis, apiName, userId, 'day', now);
//         if (typeof memberInfo[apiName].day === 'number' && memberInfo[apiName].day > 0 && day.apis[apiName] > memberInfo[apiName].day) {
//             return cb('您的账号已达单日数量上限');
//         }
//         rediser.redis.setex(`visit:freq:${userId}:month`, 2 * duration('month'), JSON.stringify(month));
//         rediser.redis.setex(`visit:freq:${userId}:day`, 2 * duration('day'), JSON.stringify(day));
//         return cb('');
//     } catch (err) {
//         Logger.error(err);
//         return cb(false);
//     }
// };

// const authValidator = (req, cb) => {
//     try {
//         //校验器是否开启
//         if (!VISIT_FREQ.AVAILABLE) {
//             return cb('');
//         }

//         if (isMobileByUa(req.headers['user-agent'])) {
//             req.query.device = 'mobile';
//             return cb('');
//         }
//         //开始进行访控校验
//         visitFreq(req, (visit) => {
//             cb(visit);
//         });
//     } catch (err) {
//         Logger.error(err);
//         return cb('您的访问异常');
//     }
// };

// const freqMiddleware = (req, res, next) => {
//     let responseWrapper = new ResponseWrapper(req, res);
//     let user = req.user;
//     let catchKey = `visit_freq_${user.id}`;
//     rediser.get(catchKey, (err, result) => {
//         if (err) {
//             return next();
//         }
//         if (result.freq === true) {
//             return responseWrapper.error('EXCEED_FRQ_ERROR');
//         }
//         return next();
//     });
// }

const stopFreqRequest = (req, res, next) => {
    if (res.freq !== '' && res.freq !== undefined) {
        return res.json({
            code: 6,
            msg: res.freq,
            data: {}
        })
    }
    return next();
}

module.exports = {
    // authValidator: authValidator,
    // freqMiddleware: freqMiddleware,
    stopFreqRequest: stopFreqRequest,
};