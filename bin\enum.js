// 下载资源状态
exports.DownloadStatus = {
    DONIG: 'donig',                                     // 待完成
    DONE: 'done',                                     // 完成
};

// 商品类型
exports.GoodsType = {
    MEMBER: 'member', // 会员
    DOWNLOAD: 'download', // 下载
};

// 支付手段
exports.PayThrough = {
    WECHAT_PAY_QR: 'wechat_pay_qr',                          // 微信支付 二维码
    WECHAT_PAY_H5: 'wechat_pay_h5',
    WECHAT_PAY_APP: 'wechat_pay_app',
};

// 订单状态
exports.OrderStatus = {
    DOING: 'doing',
    SUCCESS: 'success',
    DONE: 'done',
    REFUND: 'refund',
};

// 下载券状态
exports.TicketItemStatus = {
    AVAILABLE: 'available', // 可用
    USED: 'used', // 消耗
    EXPIRE: 'expire', // 过期
    REFUND: 'refund',           // 订单退费
};

// 商品状态
exports.GoodsStatus = {
    NORMAL: 'normal', // 正常
    DELETED: 'deleted', // 删除
};

// 订单状态
exports.TransactionStatus = {
    PROCESSING: 'processing',   // 处理中
    SUCCESS: 'success',         // 支付成功
    DONE: 'done',               // 发货完毕
    FAILED: 'failed',           // 支付失败
    CLOSED: 'closed',           // 交易关闭
    CANCEL: 'cancel',           // 取消充值
    APPLY_REFUND: 'apply_refund',// 申请退费
    REFUND: 'refund',           // 订单退费
};

// 资源类型
exports.ResourceType = {
    EXAMPAPER: 'exampaper',
    ASSEMBLE_EXAMPAPER: 'assemble_exampaper',
    QUESTION: 'question',
    EDU_ASSISTANT_FILE: 'edu_assistant_file',
    EDU_ASSISTANT_TOOL: 'edu_assistant_tool',
    RESOURCE_ALBUM: 'resource_album',
};

// 阅卷学校应用类别
exports.YjSchoolCategory = {
    BASIC: 10, // 基础版阅卷/分析,
    UPGRADE: 11, // 走班版阅卷/分析,
    JOINT_EXAMINATION: 20, // 联考(统考)版阅卷/分析,
};

// 阅卷学校应用状态
exports.YjSchoolStatus = {
    未使用: 0, // 未使用,
    使用中: 1, // 使用中,
    已过期: 2, // 已过期,
    已禁用: 9, // 已禁用
};

// 阅卷学校应用版本类型
exports.YjSchoolVersionType = {
    体验版: 1, // 针对学校
    专业版: 2, // 针对学校
    旗舰版: 3, // 针对学校
    A版: 21,  // 联考A
    B版: 22,  // 联考B
    C版: 23,  // 联考C
    D版: 24,  // 联考D
    S版: 29,  // 联考S
    地市版: 231,   // 统考市版
    区县版: 232,   // 统考区县版
};

// 设备类型
exports.DeviceType = {
    ANDROID: 'android',         // android 设备
    IOS_PHONE: 'ios_phone',     // iOS PHONE
    IOS_PAD: 'ios_pad',         // iOS PAD
    WEB: 'web',                 // WEB浏览器
    NOT_KNOWN: 'not_known',     // 未知
};

// 生涯会员类型
exports.SyMemberType = {
    普通会员: 0,
    尊享会员: 1,
    专业会员: 2,
    至尊会员: 3,
    超级会员: 99,
};

// 用户来源
exports.UserSource = {
    TIKU: 'tiku',
    HFS: 'hfs',
    SY: 'sy',
    YJ: 'yj',
    XD: 'xd',
    ZY: 'zy',
    HFS_APP_JS: 'hfs_app_js', // 好分数APP教师端
    UNIFY: 'unify', // 统一登录
};

// 生涯目前只有尊享会员、至尊会员和超级会员有题库权限
// 阅卷目前只有专业版、旗舰版有题库权限
// 会员类型
exports.MemberType = {
    TIKU_PERSONAL: 'tiku_personal',                  // 题库个人版
    TIKU_PROFESSION: 'tiku_profession',              // 学校专业版
    TIKU_COUNTY: 'tiku_county',                      // 教育局区县版
    TIKU_CITY: 'tiku_city',                          // 教育局地市版
    HFS_360: 'hfs_360',
    // SY_BASIC: 'sy_basic',                           // 专业会员
    SY_SUPREME: 'sy_supreme',                       // 尊享会员
    SY_PREMIUM: 'sy_premium',                       // 至尊会员
    SY_ADMIN: 'sy_admin',                           // 超级会员（内部使用）
    // YJ_TRIAL: 'yj_trial',                           // 体验版
    YJ_PROFESSION: 'yj_profession',                 // 专业版
    YJ_ULTIMATE: 'yj_ultimate',                     // 旗舰版
};

exports.PersonalVipType = {
    TIKU_PERSONAL: 'tiku_personal',                  // 题库个人版
    HFS_360: 'hfs_360',
    SY_SUPREME: 'sy_supreme',                       // 尊享会员
    SY_PREMIUM: 'sy_premium',                       // 至尊会员
    SY_ADMIN: 'sy_admin',                           // 超级会员（内部使用）
}

exports.SchoolVipType = {
    TIKU_PROFESSION: 'tiku_profession',              // 学校专业版
    TIKU_COUNTY: 'tiku_county',                      // 教育局区县版
    TIKU_CITY: 'tiku_city',                          // 教育局地市版
    YJ_PROFESSION: 'yj_profession',                  // 专业版
    YJ_ULTIMATE: 'yj_ultimate',                      // 旗舰版
    SCHOOL: 'school',                      // 旗舰版
    SCHOOL_PLUS: 'school_plus',                      // 旗舰版
}

// 阅卷学校应用版本类型 转换 会员类型
exports.YjSchoolVersionTypeToMemberType = {
    // [exports.YjSchoolVersionType.体验版]: exports.MemberType.YJ_TRIAL,
    [exports.YjSchoolVersionType.专业版]: exports.MemberType.YJ_PROFESSION,
    [exports.YjSchoolVersionType.旗舰版]: exports.MemberType.YJ_ULTIMATE,
};

// 生涯会员类型 转换 会员类型
exports.SyMemberTypeToMemberType = {
    [exports.SyMemberType.尊享会员]: exports.MemberType.SY_SUPREME,
    [exports.SyMemberType.至尊会员]: exports.MemberType.SY_PREMIUM,
    [exports.SyMemberType.超级会员]: exports.MemberType.SY_ADMIN,
};

// boss crm 签约版本 转换 会员类型
exports.CrmVersionToMemberType = {
    '专业版': exports.MemberType.TIKU_PROFESSION,
    '按账号售卖': exports.MemberType.TIKU_PROFESSION,
    '区县版': exports.MemberType.TIKU_COUNTY,
    '市局版': exports.MemberType.TIKU_CITY,
};

exports.NoticeCenterStatus = {
    ON: 'on', // 上架
    OFF: 'off', // 下架
    INVALID: 'invalid' // 失效
};

exports.GradePhaseMap = {
    '一年级': '小学',
    '二年级': '小学',
    '三年级': '小学',
    '四年级': '小学',
    '五年级': '小学',
    '六年级': '小学',
    '七年级': '初中',
    '八年级': '初中',
    '九年级': '初中',
    '一年级上': '小学',
    '二年级上': '小学',
    '三年级上': '小学',
    '四年级上': '小学',
    '五年级上': '小学',
    '六年级上': '小学',
    '七年级上': '初中',
    '八年级上': '初中',
    '九年级上': '初中',
    '一年级下': '小学',
    '二年级下': '小学',
    '三年级下': '小学',
    '四年级下': '小学',
    '五年级下': '小学',
    '六年级下': '小学',
    '七年级下': '初中',
    '八年级下': '初中',
    '九年级下': '初中',
    '初一': '初中',
    '初二': '初中',
    '初三': '初中',
    '初四': '初中',
    '初一上': '初中',
    '初二上': '初中',
    '初三上': '初中',
    '初四上': '初中',
    '初一下': '初中',
    '初二下': '初中',
    '初三下': '初中',
    '初四下': '初中',
    '高一': '高中',
    '高二': '高中',
    '高三': '高中',
    '高一上': '高中',
    '高二上': '高中',
    '高三上': '高中',
    '高一下': '高中',
    '高二下': '高中',
    '高三下': '高中',
    '小学': '小学',
    '初中': '初中',
    '高中': '高中',
};

exports.UserType = {
    STUDENT: 'student',     //学生
    PARENT: 'parent',       //家长
    TEACHER: 'teacher',     //教师
};

exports.UserTypeToText = {
    [exports.UserType.STUDENT]: '学生',
    [exports.UserType.PARENT]: '家长',
    [exports.UserType.TEACHER]: '教师',
};

exports.TextToUserType = {
    '学生': exports.UserType.STUDENT,
    '家长': exports.UserType.PARENT,
    '教师': exports.UserType.TEACHER,
};

// 科目
exports.Subject = {
    ALL: 'all',
    CHINESE: '语文',
    MATHEMATICS: '数学',
    ENGLISH: '英语',
    PHYSICS: '物理',
    CHEMISTRY: '化学',
    BIOLOGY: '生物',
    POLITICS: '政治',
    HISTORY: '历史',
    GEOGRAPHY: '地理',
};

exports.ShortMessageBDYMSTemplate = {
    'sms-tmpl-QPqQGb64796': '55',//注册账号
};

// 消息类型
exports.MessageSendType = {
    SHORT_MSG: 'short_msg', // 短信
    QX: 'qx' // 企业微信
};

// 消息状态
exports.MessageStatus = {
    PREPARED: 'prepared', // 待发送
    SUCCESS: 'success', // 发送成功
    FAILED: 'failed', // 发送失败
    EXPIRED: 'expired', // 过期
    CANCELLED: 'cancelled' // 已取消
};

// 消息发送场景
exports.MessageSituation = {
    REGIST_USER_V_CODE: 'registUser', // 注册用户
};

// 埋点类型
exports.EventType = {
    // 导流注册
    ZC_TIKU: 'zc_tiku',               // '题库注册'
    DL_TIKU: 'dl_tiku',               // '题库登录注册'
    DL_YJ: 'dl_yj',                   // '阅卷用户导流'
    DL_HFS: 'dl_hfs',                 // '好分数用户导流'
    DL_YYZX: 'dl_yyzx',                // '应用中心用户导流'
    // 首页
    SY_HOME_PAGE: 'sy_home_page',            // '首页打开次数'
    SY_ZRSJ_CLICK: 'sy_zrsj_click',            // '最热试卷整体点击量'
    SY_ZXSJ_CLICK: 'sy_zxsj_click',            // '最新试卷整体点击量'
    SY_ZJ_CLICK: 'sy_zj_click',            // '推荐专辑 试卷/更多 点击量'
    // 教材选题
    JCXT_HOME_PAGE: 'jcxt_home_page',            // '教材选题页面打开次数'
    JCXT_CKJX_CLICK: 'jcxt_ckjx_click',            // '教材选题查看解析点击次数'
    JCXT_JRSTL_CLICK: 'jcxt_jrstl_click',            // '教材选题点击加入试题篮次数'
    JCXT_SC_CLICK: 'jcxt_sc_click',            // '教材选题点击收藏次数'
    // 知识点选题
    ZSDXT_HOME_PAGE: 'zsdxt_home_page',            // '知识点选题页面打开次数'
    ZSDXT_CKJX_CLICK: 'zsdxt_ckjx_click',            // '知识点选题查看解析点击次数'
    ZSDXT_JRSTL_CLICK: 'zsdxt_jrstl_click',            // '知识点选题点击加入试题篮次数'
    ZSDXT_SC_CLICK: 'zsdxt_sc_click',            // '知识点选题点击收藏次数'
    ZSDXT_ZSDJJ_TIME: 'zsdxt_zsdjj_time',            // '点击知识点交集知识点'
    ZSDXT_ZSDBJ_TIME: 'zsdxt_zsdbj_time',            // '点击知识点并集知识点'
    ZSDXT_DX_CLICK: 'zsdxt_dx_click',            // '知识点选题点击多选'
    ZSDXT_ZSDBJ_CLICK: 'zsdxt_zsdbj_click',            // '知识点选题点击知识点并集'
    // 试卷选题
    SJXT_HOME_PAGE: 'sjxt_home_page',            // '试卷选题页面打开次数'
    SJXT_MBZJ_CLICK: 'sjxt_mbzj_click',            // '模板组卷功能点击次数'
    SJXT_CKXMB_CLICK: 'sjxt_ckxmb_click',            // '查看细目表功能点击次数'
    SJXT_SJFX_CLICK: 'sjxt_sjfx_click',            // '试卷分析功能点击次数'
    SJXT_SC_CLICK: 'sjxt_sc_click',            // '收藏功能点击次数'
    // 智能组卷
    ZNZJ_HOME_PAGE: 'znzj_home_page',            // '智能组卷页面打开次数'
    ZNZJ_ZSD_CLICK: 'znzj_zsd_click',            // '按知识点智能组卷次数'
    ZNZJ_ZJ_CLICK: 'znzj_zj_click',            // '按章节智能组卷次数'
    ZNZJ_SCSJ_CLICK: 'znzj_scsj_click',            // '生成试卷点击次数'
    // 细目表组卷
    XMBZJ_HOME_CLICK: 'xmbzj_home_click',            // '细目表组卷页面打开次数'
    XMBZJ_MYXMB_CLICK: 'xmbzj_myxmb_click',            // '我的细目表页签点击次数'
    XMBZJ_MORE_CLICK: 'xmbzj_more_click',            // '【查看更多】按键点击次数'
    XMBZJ_XZMB_CLICK: 'xmbzj_xzmb_click',            // '下载细目表模板按键点击次数'
    XMBZJ_CREAT_CLICK: 'xmbzj_creat_click',            // '创建细目表按键点击次数'
    XMBZJ_UPLOAD_CLICK: 'xmbzj_upload_click',            // '上传细目表按键点击次数'
    XMBZJ_USE_CLICK: 'xmbzj_use_click',            // '使用细目表按键点击次数'
    XMBZJ_SC_CLICK: 'xmbzj_sc_click',            // '收藏细目表按键点击次数'
    XMBZJ_DOWNLOAD_CLICK: 'xmbzj_download_click',            // '下载细目表按键点击次数'
    XMBZJ_SCSJ_CLICK: 'xmbzj_scsj_click',            // '生成试卷按键点击次数'
    // 试题
    QUESTION_DOWNLOAD_TIMES: 'question_download_times',            // '所有试题下载次数'
    QUESTION_SC_TIMES: 'question_sc_times',            // '所有试题收藏次数'
    // 试卷
    EXAMPAPER_DOWNLOAD_TIMES: 'exampaper_download_times',            // '所有试卷下载次数'
    EXAMPAPER_SC_TIMES: 'exampaper_sc_times',            // '所有试卷收藏次数'
    EXAMPAPER_CLICK_TIMES: 'exampaper_click_times',            // '所有试卷的点击次数'
    // 登录
    LAND_WEB_TIMES: 'land_web_times',            // '直接登录'
    LAND_HFSJS_TIMES: 'land_hfsjs_times',            // '好分数教师端登陆'
    // 搜索
    SEARCH_QUESTION_TIMES: 'search_question_times',            // '试题搜索次数'
    SEARCH_EXAMPAPER_TIMES: 'search_exampaper_times',            // '试卷搜索次数'
    SEARCH_KNOWLEDGE_CLICK: 'search_knowledge_click',            // '试题搜索点击知识点次数'
    // 组卷中心
    ZJZX_SAVE_CLICK: 'zjzx_save_click',            // '组卷中心-保存试卷'
    ZJZX_RESAVE_CLICK: 'zjzx_resave_click',            // '组卷中心-另存为新卷'
    ZJZX_DOWNCARD01_CLICK: 'zjzx_downcard01_click',            // '组卷中心-下载答题卡'
    ZJZX_DOWNCARD02_CLICK: 'zjzx_downcard02_click',            // '组卷中心-下载答题卡-保存并下载'
    ZJZX_DOWNPAPER_CLICK: 'zjzx_downpaper_click',            // '组卷中心-下载试卷'
    ZJZX_PAPERANALYZE_CLICK: 'zjzx_paperanalyze_click',            // '组卷中心-试卷分析'
    // 组卷记录
    ZJJL_PREVIEW_CLICK: 'zjjl_preview_click',            // '组卷记录-预览 '
    ZJJL_DOWNLOAD_CLICK: 'zjjl_download_click',            // '组卷记录-下载 '
    ZJJL_EDIT_CLICK: 'zjjl_edit_click',            // '组卷记录-编辑'
    ZJJL_DOWNCARD_CLICK: 'zjjl_downcard_click',            // '组卷记录-下载答题卡'
    ZJJL_DELETE_CLICK: 'zjjl_delete_click',            // '组卷记录-删除'
    ZJJL_PREVIEW_PAPERANALYZE_CLICK: 'zjjl_preview_paperanalyze_click',            // 组卷记录-预览-试卷分析'
    ZJJL_PREVIEW_DOWNPAPER_CLICK: 'zjjl_preview_downpaper_click',            // '组卷记录-预览-下载试卷'
    ZJJL_PREVIEW_EDIT_CLICK: 'zjjl_preview_edit_click',            // '组卷记录-预览-编辑试卷'
    // 下载确认
    XZQR_A4: 'xzqr_A4',            // '下载确认页-A4'
    XZQR_A3: 'xzqr_A3',            // '下载确认页-A3(双栏)'
    XZQR_B4: 'xzqr_B4',            // '下载确认页-B4((双栏))'
    XZQR_ANSWER: 'xzqr_answer',            // '下载确认页-答案'
    XZQR_POINT: 'xzqr_point',            // '下载确认页-考点'
    XZQR_ANALYZE01: 'xzqr_analyze01',            // '下载确认页-解析'
    XZQR_COMMENT: 'xzqr_comment',            // '下载确认页-点评'
    XZQR_ANALYZE02: 'xzqr_analyze02',            // '下载确认页-解答'
    XZQR_RESOURSE: 'xzqr_resourse',            // '下载确认页-试题来源'
    XZQR_TEACHER: 'xzqr_teacher',            // '下载确认页-用途-教师用卷'
    XZQR_NORMAL: 'xzqr_normal',            // '下载确认页-用途-普通用卷'
    XZQR_STUDENT: 'xzqr_student',            // '下载确认页-用途-学生用卷'
    XZQR_OK_CLICK: 'xzqr_OK_click',            // '下载确认页-确认并下载'
    // 推荐专辑
    TJZJ_SC_CLICK: 'tjzj_sc_click',            // '推荐专辑收藏次数'
    TJZJ_HOME_PAGE: 'tjzj_home_page',            // '推荐专辑页面打开次数'
    TJZJ_ZJ_CLICK: 'tjzj_zj_click',            // '推荐专辑收藏次数'
};

// 上传状态
exports.UploadExampaperStatus = {
    PROCESSING: 'processing', // 处理中
    SUCCESS: 'success', // 成功
    FAILED: 'failed', // 失败
};

// 同步阅卷类型
exports.SyncYjExamType = {
    CREATE: 'create', // 创建考试
    BIND: 'bind', // 绑定考试
};


exports.PrimaryGrade = {
    XIAO_YI: '一年级',
    XIAO_ER: '二年级',
    XIAO_SAN: '三年级',
    XIAO_SI: '四年级',
    XIAO_WU: '五年级',
    XIAO_LIU: '六年级',
};

exports.MiddleGrade = {
    CHU_YI: '初一',
    CHU_ER: '初二',
    CHU_SAN: '初三',
    CHU_SI: '初四',
};

exports.HighGrade = {
    GAO_YI: '高一',
    GAO_ER: '高二',
    GAO_SAN: '高三',
};

exports.Grade = {
    XIAO_YI: '一年级',
    XIAO_ER: '二年级',
    XIAO_SAN: '三年级',
    XIAO_SI: '四年级',
    XIAO_WU: '五年级',
    XIAO_LIU: '六年级',

    CHU_YI: '初一',
    CHU_ER: '初二',
    CHU_SAN: '初三',
    CHU_SI: '初四',

    GAO_YI: '高一',
    GAO_ER: '高二',
    GAO_SAN: '高三',
};
/**
 * 组卷题目设置模板类型
 * @type {{CUSTOM: string, SYS: string}}
 */
exports.QuestionTemplate = {
    SYS: 'sys',
    CUSTOM: 'custom'
};

exports.BasketOption = {
    'DELETE': 'delete', // 清空
    'MERGE': 'merge', // 合并
}

exports.BasketQuestionSourceType = {
    UPLOAD: 'upload' // 用户上传
}

exports.ExamPaperOtherType = '其他';

/**
 * 容易 较易 中等 较难 困难
 * @type {{EASY: number, HARD: number, VERY_EASY: number, VERY_HARD: number, NORMAL: number}}
 */
QuestionDifficultyNumber = {
    VERY_EASY: 1,
    EASY: 2,
    NORMAL: 3,
    HARD: 4,
    VERY_HARD: 5
};

exports.QuestionDifficultyName = {
    [QuestionDifficultyNumber.VERY_EASY]: '容易',
    [QuestionDifficultyNumber.EASY]: '较易',
    [QuestionDifficultyNumber.NORMAL]: '中等',
    [QuestionDifficultyNumber.HARD]: '较难',
    [QuestionDifficultyNumber.VERY_HARD]: '困难',
}
exports.SchoolVipTransformText = {
    'tiku_profession': '题库专业版',
    'tiku_county': '教育局区县版',
    'tiku_city': '教育局地市版',
    'yj_profession': '题库基础版',
    'yj_ultimate': '题库基础版',
};

exports.CouponsStatus = {
    NORMAL: 1, // 未使用
    USED: 2, // 已使用
    OUT: 3, // 已过期
};

exports.BooleanNumber = {
    YES: 1,
    NO: 0,
}

exports.BOOL = {
    YES: 1,
    NO: 0,
}

// 优惠券有效期类型
exports.CouponValidType = {
    FIX: 1, // 固定有效期
    GET: 2, // 领取后计算
};

//
exports.ZHISHIXUEDUAN = {
    '幼儿园 - 高中一贯制': ['小学', '初中', '高中'],
    '完全中学': ['初中', '高中'],
    '十二年一贯制': ['小学', '初中', '高中'],
    '九年一贯制': ['小学', '初中'],
    '小学': ['小学'],
    '初中': ['初中'],
    '高中': ['高中']
}

// 用户动态类型
exports.UserLogType = {
    u_register: 'u_register',
    u_login: 'u_login',
    u_open_vip: 'u_open_vip',
    u_assemble_exampaper: 'u_assemble_exampaper',
    u_download: 'u_download',
    u_close_vip: 'u_close_vip',
}

exports.VipRightType = {
    vip_num: 'vip_num',
    que_download_num: 'que_download_num',
    assemble_download_num: 'assemble_download_num',
    que_details_num: 'que_details_num',
    exampaper_download_num: 'exampaper_download_num',
    expire_month: 'expire_month',
    download_num: 'download_num',
}
//权益类型
exports.NewRightType = {
    // 使用次数类
    ques_download_num: 'ques_download_num',
    ques_detail_num: 'ques_detail_num',
    download_num: 'download_num', // 下载
    assemble_download_num: 'assemble_download_num',
    exampaper_download_num: 'exampaper_download_num',
    edu_file_download_num: 'edu_file_download_num',
    edu_tool_download_num: 'edu_tool_download_num',
    dtk_download_num: 'dtk_download_num',

    download_discount_num: 'download_discount_num', // 折扣
    // 数量上限
    basket_ques_limit: 'basket_ques_limit', // 试题篮上限
    edu_file_download_limit: 'edu_file_download_limit', // 备课资源上限
    // 功能
    resource_download_fun: 'resource_download_fun', // 资源下载(试卷、试题、备课资源)
    parallel_paper_fun: 'parallel_paper_fun', // 平行组卷
    micro_course_fun: 'micro_course_fun', // 示范好课
}
// 会员类型
exports.NewVipType = {
    normal: 'normal',
    personal: 'personal',
    personal_plus: 'personal_plus',
    personal_pro: 'personal_pro',
    school_basic: 'school_basic',
    school_pro: 'school_pro',
    school: 'school',
    school_plus: 'school_plus',
}

// 会员名称
exports.NewVipTypeName = {
    [exports.NewVipType.normal]: '',
    [exports.NewVipType.personal]: '普通会员卡',
    [exports.NewVipType.personal_plus]: 'Plus会员卡',
    [exports.NewVipType.personal_pro]: '尊享会员卡',
    [exports.NewVipType.school_basic]: '学校基础版',
    [exports.NewVipType.school_pro]: '学校专业版',
    [exports.NewVipType.school]: '校园版',
    [exports.NewVipType.school_plus]: '校园版Plus',
}

// 权益重置类型
exports.NewRightResetType = {
    fixed: 'fixed', // 固定
    day: 'day', // 日
    month: 'month', // 月
    year: 'year', // 年
}
/**
 * 会员权益消耗顺序
 * @type {(string)[]}
 */
exports.NewVipRightUseSort = [
    exports.NewVipType.personal_pro,
    exports.NewVipType.personal_plus,
    exports.NewVipType.personal,
    exports.NewVipType.school,
    exports.NewVipType.school_plus,
    exports.NewVipType.school_pro,
    exports.NewVipType.school_basic,
    exports.NewVipType.normal,
];

exports.NewRightTypeUseSort = [
    exports.NewRightType.assemble_download_num,
    exports.NewRightType.exampaper_download_num,
];
/**
 * 商品SKU属性
 * @type {{period: string, subject: string, time: string}}
 */
exports.NewSkuSpec = {
    period: 'period', // 学段
    subject: 'subject', // 科目
    time: 'time', // 时长
}

exports.NewSkuSpecUnit = {
    [exports.NewSkuSpec.period]: 'string',
    [exports.NewSkuSpec.subject]: 'string',
    [exports.NewSkuSpec.time]: 'number',
}

exports.NewGroupSameHandle = {
    'new': 'new', // 新
    'extend': 'extend', // 延长使用期限
}
exports.NewGroupSameType = {
    [exports.NewVipType.personal]: exports.NewGroupSameHandle.extend,
    [exports.NewVipType.personal_plus]: exports.NewGroupSameHandle.extend,
    [exports.NewVipType.personal_pro]: exports.NewGroupSameHandle.extend,
    [exports.NewVipType.school_basic]: exports.NewGroupSameHandle.extend,
    [exports.NewVipType.school_pro]: exports.NewGroupSameHandle.extend,
    [exports.NewVipType.school]: exports.NewGroupSameHandle.extend,
    [exports.NewVipType.school_plus]: exports.NewGroupSameHandle.extend,
}

exports.NewRightGroup = {
    [exports.NewVipType.personal]: exports.NewVipType.personal,
    [exports.NewVipType.personal_plus]: exports.NewVipType.personal_plus,
    [exports.NewVipType.personal_pro]: exports.NewVipType.personal_pro,
    [exports.NewVipType.school_basic]: exports.NewVipType.school_basic,
    [exports.NewVipType.school_pro]: exports.NewVipType.school_pro,
    [exports.NewRightType.download]: exports.NewRightType.download,
}

exports.NewRightSchoolGroup = {
    [exports.NewVipType.school_basic]: exports.NewVipType.school_basic,
    [exports.NewVipType.school_pro]: exports.NewVipType.school_pro,
}

exports.Period = {
    ALL: 'all',
    PRIMARY: '小学',
    MIDDLE: '初中',
    HIGH: '高中',
};
/**
 * 老权益对照新权益
 * @type {{[p: string]: *}}
 */
exports.OldNewRightMapping = {
    [exports.VipRightType.que_download_num]: exports.NewRightType.ques_download_num,
    [exports.VipRightType.que_details_num]: exports.NewRightType.ques_detail_num,
    [exports.VipRightType.exampaper_download_num]: exports.NewRightType.exampaper_download_num,
    [exports.VipRightType.assemble_download_num]: exports.NewRightType.assemble_download_num,
    [exports.VipRightType.download_num]: exports.NewRightType.download_num,
};

exports.NewOldRightMapping = {
    [exports.NewRightType.ques_download_num]: exports.VipRightType.que_download_num,
    [exports.NewRightType.ques_detail_num]: exports.VipRightType.que_details_num,
    [exports.NewRightType.exampaper_download_num]: exports.VipRightType.exampaper_download_num,
    [exports.NewRightType.assemble_download_num]: exports.VipRightType.assemble_download_num,
    [exports.NewRightType.download_num]: exports.VipRightType.download_num,
};

exports.Infinite = -1;

exports.NewVipPayArray = [
    exports.NewVipType.personal,
    exports.NewVipType.personal_plus,
    exports.NewVipType.personal_pro,
    exports.NewVipType.school_basic,
    exports.NewVipType.school_pro,
    exports.NewVipType.school,
    exports.NewVipType.school_plus,
]

/**
 * 学校会员开通方式
 * @type {{auto: number, manual: number}}
 */
exports.SchoolVipMemberSyncType = {
    auto: 1, // 自动
    manual: 2 // 手动
}

/**
 * 学校会员配置
 * @type {{[p: string]: {right_reset_type: *, group: *, vip_member_sync_type: *}|{right_reset_type: *, group: *, vip_member_sync_type: *}|{right_reset_type: *, group: *, vip_member_sync_type: *}|{right_reset_type: *, group: *, vip_member_sync_type: *}|{right_reset_type: *, group: *, vip_member_sync_type: *}}}
 */
exports.SchoolVipConfig = {
    [exports.SchoolVipType.YJ_ULTIMATE] : {
        new_vip_type: exports.NewVipType.school_basic, // 新版本
        group: exports.NewVipType.school_basic, // 新版本
        vip_member_sync_type: exports.SchoolVipMemberSyncType.auto, // 会员帐号同步方式
        right_reset_type: exports.NewRightResetType.month, // 权益刷新类型
        period: exports.Period.ALL, // 学段
        subject: exports.Subject.ALL, // 学科
    },
    [exports.SchoolVipType.YJ_PROFESSION] : {
        new_vip_type: exports.NewVipType.school_basic, // 新版本
        group: exports.NewVipType.school_basic, // 新版本
        vip_member_sync_type: exports.SchoolVipMemberSyncType.auto, // 会员帐号同步方式
        right_reset_type: exports.NewRightResetType.month, // 权益刷新类型
        period: exports.Period.ALL, // 学段
        subject: exports.Subject.ALL, // 学科
    },
    [exports.SchoolVipType.TIKU_PROFESSION] : {
        new_vip_type: exports.NewVipType.school_pro, // 新版本
        group: exports.NewVipType.school_pro, // 新版本
        vip_member_sync_type: exports.SchoolVipMemberSyncType.manual, // 会员帐号同步方式
        right_reset_type: exports.NewRightResetType.month, // 权益刷新类型
        period: exports.Period.ALL, // 学段
        subject: exports.Subject.ALL, // 学科
    },
    [exports.SchoolVipType.SCHOOL] : {
        new_vip_type: exports.NewVipType.school, // 新版本
        group: exports.NewVipType.school, // 新版本
        vip_member_sync_type: exports.SchoolVipMemberSyncType.manual, // 会员帐号同步方式
        right_reset_type: exports.NewRightResetType.day, // 权益刷新类型
        period: exports.Period.ALL, // 学段
        subject: exports.Subject.ALL, // 学科
    },
    [exports.SchoolVipType.SCHOOL_PLUS] : {
        new_vip_type: exports.NewVipType.school_plus, // 新版本
        group: exports.NewVipType.school_plus, // 新版本
        vip_member_sync_type: exports.SchoolVipMemberSyncType.auto, // 会员帐号同步方式
        right_reset_type: exports.NewRightResetType.day, // 权益刷新类型
        period: exports.Period.ALL, // 学段
        subject: exports.Subject.ALL, // 学科
    }
}

/**
 * 学校权益
 * @type {{school_basic: ({limit: number, count: number, type: string, subject_check: number, reset_type: string}|{limit: number, count: number, type: string, subject_check: number, reset_type: string}|{limit: number, count: number, type: string, subject_check: number, reset_type: string}|{limit: number, count: number, type: string, subject_check: number, reset_type: string}|{limit: number, count: number, type: string, subject_check: number, reset_type: string})[], school: ({limit: number, count: number, type: string, subject_check: number, reset_type: string}|{limit: number, count: number, type: string, subject_check: number, reset_type: string}|{limit: number, count: number, type: string, subject_check: number, reset_type: string}|{limit: number, count: number, type: string, subject_check: number, reset_type: string}|{limit: number, count: number, type: string, subject_check: number, reset_type: string})[], school_plus: ({limit: number, count: number, type: string, subject_check: number, reset_type: string}|{limit: number, count: number, type: string, subject_check: number, reset_type: string}|{limit: number, count: number, type: string, subject_check: number, reset_type: string}|{limit: number, count: number, type: string, subject_check: number, reset_type: string}|{limit: number, count: number, type: string, subject_check: number, reset_type: string})[], school_pro: ({limit: number, count: number, type: string, subject_check: number, reset_type: string}|{limit: number, count: number, type: string, subject_check: number, reset_type: string}|{limit: number, count: number, type: string, subject_check: number, reset_type: string}|{limit: number, count: number, type: string, subject_check: number, reset_type: string}|{limit: number, count: number, type: string, subject_check: number, reset_type: string})[]}}
 */
exports.SchoolVipTypeRights = {
    school_basic: [ // 学校基础版
        {group: exports.NewVipType.school_basic, type: exports.NewRightType.ques_download_num, reset_type: exports.NewRightResetType.month, limit: 30, count: 0, subject_check: 0, period: exports.Period.ALL, subject: exports.Subject.ALL},
        {group: exports.NewVipType.school_basic, type: exports.NewRightType.ques_detail_num, reset_type: exports.NewRightResetType.month, limit: 100, count: 0, subject_check: 0, period: exports.Period.ALL, subject: exports.Subject.ALL},
        {group: exports.NewVipType.school_basic, type: exports.NewRightType.exampaper_download_num, reset_type: exports.NewRightResetType.month, limit: 10, count: 0, subject_check: 0, period: exports.Period.ALL, subject: exports.Subject.ALL},
        {group: exports.NewVipType.school_basic, type: exports.NewRightType.assemble_download_num, reset_type: exports.NewRightResetType.month, limit: 5, count: 0, subject_check: 0, period: exports.Period.ALL, subject: exports.Subject.ALL},
        {group: exports.NewVipType.school_basic, type: exports.NewRightType.basket_ques_limit, reset_type: exports.NewRightResetType.fixed, limit: 60, count: 0, subject_check: 0, period: exports.Period.ALL, subject: exports.Subject.ALL},
        {group: exports.NewVipType.school_basic, type: exports.NewRightType.edu_file_download_limit, reset_type: exports.NewRightResetType.fixed, limit: 5, count: 0, subject_check: 0, period: exports.Period.ALL, subject: exports.Subject.ALL},
        {group: exports.NewVipType.school_basic, type: exports.NewRightType.resource_download_fun, reset_type: exports.NewRightResetType.fixed, limit: -1, count: 0, subject_check: 0, period: exports.Period.ALL, subject: exports.Subject.ALL},
        {group: exports.NewVipType.school_basic, type: exports.NewRightType.parallel_paper_fun, reset_type: exports.NewRightResetType.fixed, limit: -1, count: 0, subject_check: 0, period: exports.Period.ALL, subject: exports.Subject.ALL},
        {group: exports.NewVipType.school_basic, type: exports.NewRightType.micro_course_fun, reset_type: exports.NewRightResetType.fixed, limit: -1, count: 0, subject_check: 0, period: exports.Period.ALL, subject: exports.Subject.ALL}
    ],
    school_pro: [ // 学校专业版
        {group: exports.NewVipType.school_pro, type: exports.NewRightType.ques_download_num, reset_type: exports.NewRightResetType.month, limit: 50, count: 0, subject_check: 0, period: exports.Period.ALL, subject: exports.Subject.ALL},
        {group: exports.NewVipType.school_pro, type: exports.NewRightType.ques_detail_num, reset_type: exports.NewRightResetType.month, limit: 300, count: 0, subject_check: 0, period: exports.Period.ALL, subject: exports.Subject.ALL},
        {group: exports.NewVipType.school_pro, type: exports.NewRightType.exampaper_download_num, reset_type: exports.NewRightResetType.month, limit: 15, count: 0, subject_check: 0, period: exports.Period.ALL, subject: exports.Subject.ALL},
        {group: exports.NewVipType.school_pro, type: exports.NewRightType.assemble_download_num, reset_type: exports.NewRightResetType.month, limit: 10, count: 0, subject_check: 0, period: exports.Period.ALL, subject: exports.Subject.ALL},
        {group: exports.NewVipType.school_pro, type: exports.NewRightType.basket_ques_limit, reset_type: exports.NewRightResetType.fixed, limit: 60, count: 0, subject_check: 0, period: exports.Period.ALL, subject: exports.Subject.ALL},
        {group: exports.NewVipType.school_pro, type: exports.NewRightType.edu_file_download_limit, reset_type: exports.NewRightResetType.fixed, limit: 5, count: 0, subject_check: 0, period: exports.Period.ALL, subject: exports.Subject.ALL},
        {group: exports.NewVipType.school_pro, type: exports.NewRightType.resource_download_fun, reset_type: exports.NewRightResetType.fixed, limit: -1, count: 0, subject_check: 0, period: exports.Period.ALL, subject: exports.Subject.ALL},
        {group: exports.NewVipType.school_pro, type: exports.NewRightType.parallel_paper_fun, reset_type: exports.NewRightResetType.fixed, limit: -1, count: 0, subject_check: 0, period: exports.Period.ALL, subject: exports.Subject.ALL},
        {group: exports.NewVipType.school_pro, type: exports.NewRightType.micro_course_fun, reset_type: exports.NewRightResetType.fixed, limit: -1, count: 0, subject_check: 0, period: exports.Period.ALL, subject: exports.Subject.ALL}
    ],
    school: [ // 校园版
        {group: exports.NewVipType.school, type: exports.NewRightType.download_num, reset_type: exports.NewRightResetType.day, limit: 20, count: 0, subject_check: 1, period: exports.Period.ALL, subject: exports.Subject.ALL},
        {group: exports.NewVipType.school, type: exports.NewRightType.ques_detail_num, reset_type: exports.NewRightResetType.day, limit: 1000, count: 0, subject_check: 0, period: exports.Period.ALL, subject: exports.Subject.ALL},
        {group: exports.NewVipType.school, type: exports.NewRightType.edu_file_download_limit, reset_type: exports.NewRightResetType.fixed, limit: 5, count: 0, subject_check: 0, period: exports.Period.ALL, subject: exports.Subject.ALL},
        {group: exports.NewVipType.school, type: exports.NewRightType.basket_ques_limit, reset_type: exports.NewRightResetType.fixed, limit: 60, count: 0, subject_check: 0, period: exports.Period.ALL, subject: exports.Subject.ALL},
        {group: exports.NewVipType.school, type: exports.NewRightType.resource_download_fun, reset_type: exports.NewRightResetType.fixed, limit: -1, count: 0, subject_check: 1, period: exports.Period.ALL, subject: exports.Subject.ALL},
        {group: exports.NewVipType.school, type: exports.NewRightType.parallel_paper_fun, reset_type: exports.NewRightResetType.fixed, limit: -1, count: 0, subject_check: 1, period: exports.Period.ALL, subject: exports.Subject.ALL},
        {group: exports.NewVipType.school, type: exports.NewRightType.micro_course_fun, reset_type: exports.NewRightResetType.fixed, limit: -1, count: 0, subject_check: 1, period: exports.Period.ALL, subject: exports.Subject.ALL},
        {group: exports.NewVipType.school, type: exports.NewRightType.download_discount_num, reset_type: exports.NewRightResetType.fixed, limit: -1, count: 70, subject_check: 0, period: exports.Period.ALL, subject: exports.Subject.ALL}
    ],
    school_plus: [ // 校园版Plus
        {group: exports.NewVipType.school_plus, type: exports.NewRightType.download_num, reset_type: exports.NewRightResetType.day, limit: 20, count: 0, subject_check: 1, period: exports.Period.ALL, subject: exports.Subject.ALL},
        {group: exports.NewVipType.school_plus, type: exports.NewRightType.ques_detail_num, reset_type: exports.NewRightResetType.day, limit: 1000, count: 0, subject_check: 0, period: exports.Period.ALL, subject: exports.Subject.ALL},
        {group: exports.NewVipType.school_plus, type: exports.NewRightType.edu_file_download_limit, reset_type: exports.NewRightResetType.fixed, limit: 5, count: 0, subject_check: 0, period: exports.Period.ALL, subject: exports.Subject.ALL},
        {group: exports.NewVipType.school_plus, type: exports.NewRightType.basket_ques_limit, reset_type: exports.NewRightResetType.fixed, limit: 60, count: 0, subject_check: 0, period: exports.Period.ALL, subject: exports.Subject.ALL},
        {group: exports.NewVipType.school_plus, type: exports.NewRightType.resource_download_fun, reset_type: exports.NewRightResetType.fixed, limit: -1, count: 0, subject_check: 1, period: exports.Period.ALL, subject: exports.Subject.ALL},
        {group: exports.NewVipType.school_plus, type: exports.NewRightType.parallel_paper_fun, reset_type: exports.NewRightResetType.fixed, limit: -1, count: 0, subject_check: 1, period: exports.Period.ALL, subject: exports.Subject.ALL},
        {group: exports.NewVipType.school_plus, type: exports.NewRightType.micro_course_fun, reset_type: exports.NewRightResetType.fixed, limit: -1, count: 0, subject_check: 1, period: exports.Period.ALL, subject: exports.Subject.ALL},
        {group: exports.NewVipType.school_plus, type: exports.NewRightType.download_discount_num, reset_type: exports.NewRightResetType.fixed, limit: -1, count: 70, subject_check: 0, period: exports.Period.ALL, subject: exports.Subject.ALL}
    ]
}
/**
 * 考后巩固试卷分类名称
 * @type {{"1": string, "2": string, "3": string, "4": string, "5": string, "6": string}}
 */
exports.ExamAfterCategoryName = {
    1: '基础卷',
    2: '基础卷',
    3: '基础卷',
    4: '薄弱卷',
    5: '共性卷',
    6: '教师讲义'
}

exports.TopicResourceType = {
    EXAMPAPER: 'exampaper',
    EDU_TOOL: 'edu_tool',
    EXAM_CONSOLIDATE: 'exam_consolidate'
}
// /**
//  * 1-期中真卷 2-期中模拟 3-单元分层 4-考后讲评 5-月考 6-PPT课件 7-教学工具
//  * @type {{"1": string}}
//  */
// exports.DownloadResourceCategory = {
//     1: 1,
//     2: 2,
//     3: 3,
//     4: ''
//     1: ''
//     1: ''
//     1: ''
// }
// 10: 知识点
// 11: 系统试题
// 12: 系统试卷
// 13: 备课资源
// 14: 教学工具
// 15: 文本试题(包含所有内容)，不需要单独扣除用户权益
exports.AlbumResourceType = {
    KNOWLEDGE: 10,
    QUESTION: 11,
    EXAMPAPER: 12,
    EDU_FILE: 13,
    EDU_TOOL: 14,
    TEXT_QUESTION: 15
}

exports.PeriodGradeMapping = [
    { yj_grade: '一年级', grade: '一年级', period: '小学'},
    { yj_grade: '二年级', grade: '二年级', period: '小学'},
    { yj_grade: '三年级', grade: '三年级', period: '小学'},
    { yj_grade: '四年级', grade: '四年级', period: '小学'},
    { yj_grade: '五年级', grade: '五年级', period: '小学'},
    { yj_grade: '六年级', grade: '六年级', period: '小学'},
    { yj_grade: '初一', grade: '七年级', period: '初中'},
    { yj_grade: '初二', grade: '八年级', period: '初中'},
    { yj_grade: '初三', grade: '九年级', period: '初中'},
    { yj_grade: '初四', grade: '九年级', period: '初中'},
    { yj_grade: '直升初一', grade: '七年级', period: '初中'},
    { yj_grade: '直升初二', grade: '八年级', period: '初中'},
    { yj_grade: '七年制初一', grade: '七年级', period: '初中'},
    { yj_grade: '七年制初二', grade: '八年级', period: '初中'},
    { yj_grade: '七年制初三', grade: '九年级', period: '初中'},
    { yj_grade: '二四制初一', grade: '七年级', period: '初中'},
    { yj_grade: '二四制初二', grade: '八年级', period: '初中'},
    { yj_grade: '高一', grade: '高一', period: '高中'},
    { yj_grade: '高二', grade: '高二', period: '高中'},
    { yj_grade: '高三', grade: '高三', period: '高中'},
    { yj_grade: '直升高一', grade: '高一', period: '高中'},
    { yj_grade: '四年制高一', grade: '高一', period: '高中'},
    { yj_grade: '四年制高二', grade: '高二', period: '高中'},
    { yj_grade: '四年制高三', grade: '高三', period: '高中'},
    { yj_grade: '四年制高四', grade: '高三', period: '高中'},
    { yj_grade: '国际部高一', grade: '高一', period: '高中'},
    { yj_grade: '国际部高二', grade: '高二', period: '高中'},
    { yj_grade: '国际部高三', grade: '高三', period: '高中'},
    { yj_grade: '国际部高四', grade: '高三', period: '高中'},
];

exports.BasketCategory = {
    BASKET: 'basket',
    HOMEWORK: 'homework',
}

exports.QuestionSource = {
    SYS: 'sys',
    UPLOAD: 'upload', // 上传
    // REF: 'ref', // 引用
    JY: 'jy', // 校验平台
    ZX: 'zx', // 智学
    ZYK: 'zyk', // 资源库
    XKW: 'xkw', // 学科网
}

exports.OpenSchema = {
    user_paper: 'user_paper', // 用户组卷
    user_basket: 'user_basket', // 试题篮
    user_question: 'user_question', // 用户试题
    tw_specification: 'tw_specification', // 细目表
    exampaper_question_template: 'exampaper_question_template', // 试卷试题模板
}

exports.PaperSourceType = {
    SYS: 'sys', // 系统
    ASSEMBLE: 'assemble', // 个人组卷
    EXAM: 'exam', // 考后巩固
    UPLOAD: 'upload', // 考后巩固
    ZYK: 'zyk', // 资源库
}

// 推荐数据日志类型
exports.RecoDataLogType = {
    QUESTION_SEARCH: 'question_search', // 试题搜索
    EXAMPAPER_SEARCH: 'exampaper_search', // 试卷搜索
}

exports.PaperStatus = {
    // 前两个状态后续要统一
    EDITABLE: 'editable',
    NOTEDITABLE: 'notEditable',
    INIT: 'init',
    EDIT: 'edit',
    DONE: 'done',
    ERROR: 'error',
}

exports.ExamStatus = {
    NOTEDITABLE: 'notEditable', // 不可编辑
    EDITABLE: 'editable', // 可编辑
}

