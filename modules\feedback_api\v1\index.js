const Joi = require('@hapi/joi');
const mongodber = require('../../utils/mongodber')
const db = mongodber.use('tiku')
const logger = require('../../utils/logger');
const enums = require('../../../bin/enum');
let ResponseWrapper = require('../../middlewares/response_wrapper');
const utils = require('../../utils/utils');

module.exports = {
    add,
    getList,
};

const JOI_POST_DATA = Joi.object({
    content: Joi.string().required(),
    images: Joi.array().items(Joi.object({
        name: Joi.string().required(),
        url: Joi.string().required()
    })).default([]).optional(),
});

async function add(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const params = await JOI_POST_DATA.validate(req.body);
        const user = await db.collection('user').findOne({_id: req.user.id});
        const ds = {
            user_id: req.user.id,
            name: user.name,
            phone: user.phone,
            is_vip: user.is_vip || false,
            content: params.content,
            images: params.images,
            status: enums.BooleanNumber.NO,
            ctime: new Date(),
            utime: new Date()
        }
        const insertResult = await db.collection('feedback').insertOne(ds);
        const result = {
            id: insertResult.insertedId.toString()
        };
        return responseWrapper.succ(result);
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const JOI_GET_LIST = Joi.object({
    offset: Joi.number().required(),
    limit: Joi.number().required(),
}).unknown(true);

async function getList(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const { offset, limit } = await JOI_GET_LIST.validate(req.query);
        const result = {
            total: 0,
            list: []
        }
        const total = await db.collection('feedback').find({user_id: req.user.id}).count();
        if (!total) return responseWrapper.succ(result);
        result.total = total;
        const list = await db.collection('feedback').find({user_id: req.user.id}).sort({ctime: -1}).skip(offset).limit(limit).toArray();
        result.list = (list || []).map(e => {
            return {
                id: e._id.toString(),
                content: e.content || '',
                images: e.images,
                ctime: e.ctime.getTime(),
                handle_time: e.handle_record && e.handle_time.getTime(),
                handle_record: (e.handle_record || []).map(r => {
                    return {
                        time: r.time,
                        status: r.status,
                        content: r.content,
                    }
                })
            }
        });
        await db.collection('feedback').updateMany({user_id: req.user.id}, {$set: {read_status: 2}}); // 阅读状态变更
        return responseWrapper.succ(result);
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}
