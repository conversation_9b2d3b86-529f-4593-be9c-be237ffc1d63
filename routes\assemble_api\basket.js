const express = require('express');
const router = express.Router();
const basket = require('../../modules/assemble_api/v2/basket');
const apikey = require('../../modules/middlewares/apikey');

router.use(apikey('KB'));


// 取试题篮中试题情况
router.get('/basket/questions/', basket.get_questions);
// 向试题篮中提交试题
router.post('/basket/questions/', basket.post_questions);
// 从试题篮中删除试题
router.delete('/basket/questions/', basket.delete_questions);

// 取试题篮
router.get('/basket/', basket.get_basket);
// 整体更新试题篮
router.put('/basket/', basket.put_basket);
// 删除试题篮
router.delete('/basket/', basket.delete_basket);

module.exports = router;
