let mongodber = require('../../utils/mongodber');
let ResponseWrapper = require('../../middlewares/response_wrapper');
let logger = require('../../utils/logger');
let enums = require('../../../bin/enum');
let db = mongodber.use('tiku');
let _ = require('underscore');
const Joi = require('@hapi/joi');
const DEFAULTNAME = '题库会员';

const getGoodsSchema = Joi.object({
    type: Joi.string().valid(...Object.values(enums.GoodsType)).default(enums.GoodsType.MEMBER).optional(),
}).unknown(true);

const getGoodsList = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let { type } = await Joi.validate(req.query, getGoodsSchema);
        let data = await db.collection('@Goods').find({ type: type, status: enums.GoodsStatus.NORMAL }).sort({ month: 1 }).toArray();

        if (!data) {
            throw new Error('没有数据');
        }
        let resObj = {
            total: data.length,
            list: data.map(item => {
                return {
                    id: item._id.toString(),
                    name: item.name,
                    type: item.type,

                    original_price: item.original_price,
                    discount_price: item.discount_price,
                    final_price: item.final_price,

                    resource_type: item.resource_type,

                    month: item.month,
                    count: item.count,
                }
            })
        };

        return responseWrapper.succ(resObj);
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

const postGoodsSchema = Joi.object({
    name: Joi.string().default(DEFAULTNAME).optional(),
    type: Joi.string().valid(...Object.values(enums.GoodsType)).required(),

    original_price: Joi.number().required(),
    discount_price: Joi.number().required(),
    final_price: Joi.number().required(),

    resource_type: Joi.string().valid(...Object.values(enums.ResourceType)).optional(),

    month: Joi.number().optional(),
    count: Joi.number().optional(),
}).without('resource_type', 'month');

const postGoods = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let { name, type, original_price, discount_price, final_price, month, count, resource_type } = await Joi.validate(req.body, postGoodsSchema);
        const now = new Date();

        await db.collection('@Goods').updateMany({
            type: type,
            status: enums.GoodsStatus.NORMAL,
            resource_type: resource_type,
            month: month
        }, {
            $set: {
                utime: now,
                status: enums.GoodsStatus.DELETED
            },
        });

        let insertData = {
            ctime: now,
            utime: now,

            name: name,
            type: type,
            status: enums.GoodsStatus.NORMAL,

            original_price: original_price,
            discount_price: discount_price,
            final_price: final_price,
        };
        if (resource_type) insertData.resource_type = resource_type;
        if (month) insertData.month = month;
        if (count) insertData.count = count;

        const goodsResult = await db.collection('@Goods').insertOne(insertData);

        return responseWrapper.succ({ id: goodsResult.insertedId.toString() });
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

module.exports = {
    getGoodsList,
    postGoods,
};