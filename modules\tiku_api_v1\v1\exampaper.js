
let mongodber = require('../../utils/mongodber');
let ResponseWrapper = require('../../middlewares/response_wrapper');
let logger = require('../../utils/logger');
let _ = require('underscore');
const { ObjectID } = require('mongodb');
const { off } = require('superagent');
const { toArray } = require('lodash');
let db = mongodber.use('tiku');

const exampaperCollection = db.collection('exampaper');

const putShareExampaper = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    let id = req.params.id;
    let shareSort = Number(req.body.share_sort);
    let pressVersion = req.body.press_version;
    let type = req.body.type;
    let userName = req.body.user_name;
    let data = {};

    if (!id || !type || !shareSort) {
        return responseWrapper.error('PARAMETERS_ERROR', 'id , type 和 share_sort 为必传字段！');
    }
    if (type === 'share' && !pressVersion) {
        return responseWrapper.error('PARAMETERS_ERROR', 'press_version为必传字段！');
    }
    if (type === 'share' && shareSort === 5 && !userName) {
        return responseWrapper.error('PARAMETERS_ERROR', 'user_name为必传字段！');
    }
    if (userName) {
        data.user_name = userName;
    }
    try {
        if (type === 'share') {
            data.share_sort = shareSort;
            data.press_version = pressVersion;
            data.view_count = 0;
            data.exampaper_download_num = 0;
        }
        if (type === 'cancel') {
            data.share_sort = 0;
        }
        let cond = {
            $set: data
        };
        let result = await exampaperCollection.updateOne({ _id: ObjectID(id) }, cond);
        return responseWrapper.succ({ id: result._id });
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

const getShareExampaper = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    let offset = Number(req.query.offset || 0);
    let limit = Number(req.query.limit || 10);
    let sort = req.query.sort;
    let period = req.query.period;
    let cond = {};
    let sortBy = {};
    if (!period) {
        return responseWrapper.error('PARAMETERS_ERROR', 'period 为必传字段！');
    }

    if (req.query.press_version) {
        cond.press_version = req.query.press_version;
    }
    if (req.query.grade) {
        cond.grade = req.query.grade;
    }
    if (req.query.subject) {
        cond.subject = req.query.subject;
    }
    if (req.query.type) {
        cond.type = req.query.type;
    }
    if (!sort) {
        return responseWrapper.error('PARAMETERS_ERROR', 'sort 为必传字段！');
    }
    cond['period'] = period;
    try {
        cond['share_sort'] = {
            $gt: 0
        };
        if (sort === 'view_count') {
            sortBy.view_count = -1;
        }

        if (sort === 'exampaper_download_num') {
            sortBy.exampaper_download_num = -1;
        }

        if (sort === 'utime') {
            sortBy.utime = -1;
        }
        if (sort === 'default') {
            sortBy = {
                share_sort: -1,
                utime: -1
            }
        }
        let result = await exampaperCollection.find(cond).sort(sortBy).limit(limit).skip(offset).toArray();
        for (const value of result) {
            if (value) {
                value.id = value._id;
                delete value._id;
            }
        }
        let total_num = await exampaperCollection.find(cond).count();
        return responseWrapper.succ({ total_num: total_num, list: result });
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
}
const getExampaperUnitTime = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const cyear = new Date().getFullYear();
        responseWrapper.succ(cyear);
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};


module.exports = {
    putShareExampaper: putShareExampaper,
    getShareExampaper: getShareExampaper,
    getExampaperUnitTime: getExampaperUnitTime,
};