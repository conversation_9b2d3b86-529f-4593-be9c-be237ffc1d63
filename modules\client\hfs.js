const _ = require('lodash');
const URL = require('url');
const config = require('config');
const axios = require('axios');
const logger = require('../utils/logger');
const SERVER = config.get('HFS_SERVER');
const qs = require('querystring');
const options = {
    headers: {
        'api-key': SERVER.apiKey
    },
    timeout: 2000
};

module.exports = {
    getExamPublishStatus,
}

/**
 * 获取试卷成绩发布状态
 * @param examId - 考试ID
 * @param paperId - 科目ID
 * @param subject - 科目
 * @returns {Promise<boolean>}
 */
async function getExamPublishStatus(examId, paperId, subject) {
    const exam = await getExamStatus(examId);
    if (!_.size(exam)) return false;
    const paperSign = +(paperId.split('-')[0]);
    let paper = exam.find(e => e.paperId === paperSign);
    if (_.isEmpty(paper)) {
        paper = exam.find(e => e.subject.includes(subject));
    }
    if (_.isEmpty(paper) || paper['isPublished'] === '否') return false;
    return true;
}

async function getExamStatus(examId) {
    let url = URL.format({
        protocol: SERVER.protocol,
        hostname: SERVER.hostname,
        pathname: `/v1/exams/${examId}/status`,
        port: SERVER.port
    });
    try {
        let result = await axios.get(url, options);
        if (!result.data || result.data.code !== 0) {
            logger.error(`好分数获取考试状态失败,url:[${url}]`);
            return null;
        }
        return _.get(result, 'data.data', null);
    } catch (e) {
        logger.error(e);
        return null;
    }
}
