const moment = require('moment');
const _ = require('lodash');
const mongodber = require('../../utils/mongodber');
const db = mongodber.use('tiku');
const ObjectId = require('mongodb').ObjectId;
const enums = require('../../../bin/enum');
const schema = require('../../../bin/schema');
const notice_message = require('../../notice_message_api');

const default_rights = [
    {group: enums.NewVipType.normal, type: enums.NewRightType.basket_ques_limit, reset_type: enums.NewRightResetType.fixed, limit: 30, count: 0},
    {group: enums.NewVipType.normal, type: enums.NewRightType.ques_detail_num, reset_type: enums.NewRightResetType.day, limit: 10, count: 0},
];

module.exports = {
    get_vips,
    use_right,
    reset_rights,
    init_rights,
    get_right_limit,
    get_right_value_min,
    add_sku_right,
    sync_school_right,
    get_fun_right_status,
    sub_sku_right,
    get_use_right,
}

async function init_rights(user_id) {
    const has_rights = await _get_rights_by_group(user_id, enums.NewVipType.normal);
    if (_.size(has_rights)) return;
    const rights = [];
    for (const r of default_rights) {
        const right = _init_right(user_id);
        _.assign(right, r);
        right.sku_id = enums.NewVipType.normal;
        right.stime = moment('2024-01-01').startOf('day').toDate();
        right.etime = moment('2099-12-31').endOf('day').toDate();
        rights.push(right);
    }
    await db.collection(schema.user_right).insertMany(rights);
}

async function get_vips(user_id) {
    const cond = {
        user_id: user_id,
        status: enums.BooleanNumber.YES,
        group: {$in: Object.values(enums.NewVipType)}
    }
    let rights = await db.collection(schema.user_right).find(cond).toArray();
    rights = _filter_valid_date(rights);
    rights = _filter_pay_right(rights);
    const map = new Map();
    for (let r of rights) {
        let key = `${r.group}_${_.isArray(r.period) ? r.period.join('_') : r.period}_${r.subject}`;
        if (!map.has(key)) {
            map.set(key, {
                type: r.group,
                name: enums.NewVipTypeName[r.group],
                period: r.period,
                subject: r.subject,
                stime: r.stime.getTime(),
                etime: r.etime.getTime(),
                rights: []
            });
        }
        const group = map.get(key);
        if (moment(group.etime).isBefore(r.etime)) { // 续期问题
            group.etime = r.etime.getTime();
        }
        // 不同批次
        if (!moment().isBetween(r.stime, r.etime)) continue;
        group.rights.push({
            period: r.period,
            subject: r.subject,
            type: r.type,
            reset_type: r.reset_type,
            limit: r.limit,
            count: r.count,
            subject_check: r.subject_check || 0
        });
    }
    return [...map.values()];
}

/**
 * 获取用户有效权益
 * @param {number} user_id 用户ID
 * @param params
 * @returns {Promise<*|*[]>}
 */
async function get_rights(user_id, params = {}) {
    let { type, period, subject, exclude } = params;
    const cond = {
        user_id: user_id,
        status: enums.BooleanNumber.YES
    }
    if (type) {
        if (type === enums.NewRightType.exampaper_download_num) { // 系统试卷
            cond.type = {$in: [enums.NewRightType.download_num, enums.NewRightType.exampaper_download_num]};
        } else if (type === enums.NewRightType.assemble_download_num) { // 用户组卷
            cond.type = {$in: [enums.NewRightType.download_num, enums.NewRightType.assemble_download_num]};
        } else if (type === enums.NewRightType.dtk_download_num) { // 答题卡
            cond.type = {$in: [enums.NewRightType.download_num, enums.NewRightType.exampaper_download_num, enums.NewRightType.assemble_download_num]};
        } else if (type === enums.NewRightType.ques_download_num) { // 试题
            cond.type = {$in: [enums.NewRightType.download_num, enums.NewRightType.ques_download_num]};
        }  else if (type === enums.NewRightType.edu_file_download_num) { // 备课资源
            cond.type = {$in: [enums.NewRightType.download_num, enums.NewRightType.exampaper_download_num, enums.NewRightType.assemble_download_num]};
        }  else if (type === enums.NewRightType.edu_tool_download_num) { // 教学工具
            cond.type = {$in: [enums.NewRightType.download_num, enums.NewRightType.exampaper_download_num, enums.NewRightType.assemble_download_num]};
        }else {
            cond.type = type;
        }
    }
    if (exclude) {
        if (!Array.isArray(exclude)) {
            exclude = exclude.split(',');
        }
        cond.group = {$nin: exclude};
    }
    let list = await db.collection(schema.user_right).find(cond).toArray();
    list = _filter_between_date(list);
    if (period && subject) {
        list = _filter_subject(list, period, subject);
    }
    if (!_.isEmpty(params)) {
        list = _filter_pay_right(list);
    }
    return list;
}


/**
 * 刷新权益
 * @param user_id
 * @returns {Promise<void>}
 */
async function reset_rights(user_id) {
    const rights = await get_rights(user_id);
    if (!_.size(rights)) return;
    const ids = [];
    for (const r of rights) {
        if (r.reset_type === enums.NewRightResetType.fixed || r.limit === enums.Infinite) continue;
        if (moment().isSame(r.last_time, r.reset_type)) continue;
        ids.push(r._id);
    }
    if (_.size(ids)) {
        const now = new Date();
        await db.collection(schema.user_right).updateMany({_id: {$in: ids}}, {$set: {utime: now, count: 0}});
    }
}

async function use_right(user_id, params, right) {
    if (_.isEmpty(right)) {
        right = await get_use_right(user_id, params);
    }
    if (_.isEmpty(right)) return false;
    await _update_right_count(right);
    return true;
}

async function get_use_right(user_id, params) {
    const { type, period = enums.Period.ALL, subject = enums.Subject.ALL, target } = params; //
    let rights = await get_rights(user_id, params);
    rights = _filter_valid_count(rights);
    if (!_.size(rights)) return null;
    // 单次下载优先消耗
    if (target) { // 定向权益
        const target_right = rights.find(e => e.target && e.target.id === target.id && e.target.type === target.type);
        if (!_.isEmpty(target_right)) {
            return target_right;
        }
    }
    // 消耗顺序
    const sort_arr = enums.NewVipRightUseSort;
    const sort_map = new Map();
    for (const s of sort_arr) {
        sort_map.set(s, []);
    }

    for (const r of rights) {
        if (!sort_map.has(r.group)) continue;
        sort_map.get(r.group).push(r);
    }
    let right = null;
    for (const group of sort_map.keys()) {
        const arr = sort_map.get(group);
        if (!_.size(arr)) continue;
        //
        if ((group === enums.NewVipType.school_pro || group === enums.NewVipType.school_basic)
            && (type === enums.NewRightType.edu_file_download_num || type === enums.NewRightType.dtk_download_num)
        ) {
            for(const right_type of Object.values(enums.NewRightTypeUseSort)) {
                right = arr.find(e => e.type === right_type);
                if (!_.isEmpty(right)) break;
            }
            if (!_.isEmpty(right)) break;
        } else {
            right = arr[0];
            break;
        }
    }
    if (_.isEmpty(right)) return null;
    return right;
}

async function get_right_limit(user_id, params) {
    const { type, period = enums.Period.ALL, subject = enums.Subject.ALL } = params; //
    const types = [
        enums.NewRightType.basket_ques_limit,
        enums.NewRightType.edu_file_download_limit
    ];
    if (!types.includes(type)) return 0;
    let rights = await get_rights(user_id, params);
    let right = null;
    for (const r of rights) {
        if (r.limit === enums.Infinite) {
            right = r;
            right.limit = Number.MAX_SAFE_INTEGER;
            break;
        }
        if (!right || r.limit > right.limit) {
            right = r;
        }
    }
    return right || { limit: 0, count: 0 };
}

async function get_right_value_min(user_id, params) {
    const { type, period = enums.Period.ALL, subject = enums.Subject.ALL } = params; //
    const types = [
        enums.NewRightType.download_discount_num
    ];
    if (!types.includes(type)) return 0;
    let rights = await get_rights(user_id, params);
    let result = null;
    for (const r of rights) {
        if (!result || r.count < result) {
            result = r.count;
        }
    }
    return result || 0;
}

/**
 * 根据购买商品发放权益
 * @param user_id 用户ID
 * @param order_id 订单ID
 * @param sku_id SKU标识
 * @param target 定向资源标识
 * @returns {Promise<void>}
 */
async function add_sku_right(user_id, order_id, sku_id, target) {
    const sku = await db.collection(schema.pms_sku).findOne({_id: new ObjectId(sku_id)});
    if (_.isEmpty(sku)) return;
    const spu = await db.collection(schema.pms_spu).findOne({_id: new ObjectId(sku['spu_id'])});
    const group = spu.code;
    let time = _get_sku_spec_value(enums.NewSkuSpec.time, sku) || 1; // 没有时间的默认一个月
    let stime = moment().startOf('day').toDate();
    let etime = moment(stime).add(time, 'month').endOf('day').toDate();
    const period = _get_sku_spec_value(enums.NewSkuSpec.period, sku) || enums.Period.ALL;
    const subject = _get_sku_spec_value(enums.NewSkuSpec.subject, sku) || enums.Subject.ALL;
    if (enums.NewGroupSameType[group] === enums.NewGroupSameHandle.extend) { // 延期
        let user_rights = await db.collection(schema.user_right).find({user_id: user_id, group: group, period: period, subject: subject, status: enums.BooleanNumber.YES}).toArray();
        user_rights = _filter_valid_date(user_rights)// 排除已失效
        for (const r of user_rights) {
            if (moment(r.etime).isAfter(stime)) {
                stime = r.etime;
            }
        }
        etime = moment(stime).add(time, 'month').endOf('day').toDate();
    }
    const rights = [];
    for (const r of sku.rights) {
        const right = _init_right(user_id);
        right.order_id = order_id;
        right.sku_id = sku_id;
        right.group = group;
        right.period = period;
        right.subject = subject;
        right.subject_check = r.subject_check;
        right.type = r.type;
        right.reset_type = r.reset_type;
        right.limit = r.limit;
        right.count = r.value || 0;
        right.stime = stime;
        right.etime = etime;
        right.target = target;
        rights.push(right);
    }
    await db.collection(schema.user_right).insertMany(rights);
    //
    if (Object.values(enums.NewVipType).includes(group)) {
        const user = await db.collection(schema.user).findOne({_id: user_id});
        await notice_message.addVipMsg(user.name, spu.name);
    }
}

async function sync_school_right(user, school) {
    const user_id = user._id;
    if (_.isEmpty(school)) return;
    if (!school.expired_time || moment().isAfter(school.expired_time)) return; // 过期
    const vip_config = enums.SchoolVipConfig[school.vip_type];
    if (!vip_config || vip_config.vip_member_sync_type !== enums.SchoolVipMemberSyncType.auto) return; // 需要手动导入

    const group = vip_config.group;
    let rights = await _get_rights_by_group(user_id, group);
    rights = _filter_between_date(rights);
    if (_.size(rights)) return;
    if (school.vip_num !== -1) { // 人数限制
        const count = await _get_school_vip_count(user.sch_id, group);
        if (count >= school.vip_num) {
            return;
        }
    }
    const insert_rights = [];
    const school_rights = enums.SchoolVipTypeRights[group] || [];
    const now = new Date();
    for (const r of school_rights) {
        const tr = _.assign({}, r);
        tr.user_id = user_id;
        tr.order_id = '';
        tr.sku_id = tr.group;
        if (_.size(school.vip_period)) tr.period = school.vip_period;
        const old_type = enums.NewOldRightMapping[r.type];
        if (old_type && school.hasOwnProperty(`${old_type}`)) {
            tr.limit = school[old_type];
        }
        tr.stime = school.start_time;
        tr.etime = school.expired_time;
        tr.last_time = now;
        tr.status = enums.BooleanNumber.YES;
        tr.ctime = now;
        tr.utime = now;
        insert_rights.push(tr);
    }
    await db.collection(schema.user_right).deleteMany({user_id: user_id, group: group});
    await db.collection(schema.user_right).insertMany(insert_rights);
}

/**
 * 获取功能权益开放状态
 * @param user_id
 * @param params
 * @returns {boolean}
 */
async function get_fun_right_status(user_id, params) {
    const { type, period = enums.Period.ALL, subject = enums.Subject.ALL } = params; //
    if (!type || !_.endsWith(type, 'fun')) {
        return false;
    }
    let rights = await get_rights(user_id, params);
    if (!_.size(rights)) {
        return false;
    }
    return true;
}

/**
 * 根据订单消除权益
 * @param user_id
 * @param order_id
 * @returns {Promise<void>}
 */
async function sub_sku_right(user_id, order_id) {
    const now = new Date();
    let order_rights = await db.collection(schema.user_right).find({
        user_id: user_id,
        order_id: order_id,
        status: enums.BooleanNumber.YES
    }).toArray();
    order_rights = _filter_valid_date(order_rights); // 获取使用中或未生效的
    if (!_.size(order_rights)) return;
    const ids = order_rights.map(e => e._id.toString());
    // 判断是否需要延期的
    const group_right = order_rights[0];
    const group = group_right.group;
    if (enums.NewGroupSameType[group] === enums.NewGroupSameHandle.extend) {
        //
        let rights = await db.collection(schema.user_right).find({
            user_id: user_id,
            group: group,
            period: group_right.period,
            subject: group_right.subject,
            status: enums.BooleanNumber.YES
        }).toArray();
        rights = _filter_valid_date(rights);
        // 过滤
        const same_group_rights = rights.filter(e =>
            !ids.includes(e._id.toString())
            && moment(e.etime).isAfter(group_right.etime)
        );
        if (_.size(same_group_rights)) {
            let stime = group_right.stime; // 假设未生效
            if (moment().isBetween(group_right.stime, group_right.etime)) { // 当前生效中
                stime = moment().startOf('day').toDate();
            }
            // 按照订单ID分组
            const order_rights_map = new Map();
            for (const r of same_group_rights) {
                if (!order_rights_map.has(r.order_id)) {
                    order_rights_map.set(r.order_id, []);
                }
                const arr = order_rights_map.get(r.order_id);
                arr.push(r);
            }
            for(const key of order_rights_map.keys()) {
                const arr = order_rights_map.get(key);
                const tmp = arr[0];
                let time = tmp.etime.getTime() - tmp.stime.getTime();
                const etime = moment(stime).add(time, 'millisecond').toDate();
                await db.collection(schema.user_right).updateMany({_id: {$in: arr.map(e => e._id)}}, {$set: {stime: stime, etime: etime, utime: now}});
                stime = etime;
            }
        }
    }
    // 修改数据
    await db.collection(schema.user_right).updateMany({_id: {$in: order_rights.map(e => e._id)}}, {$set: {status: enums.BooleanNumber.NO, utime: now}});
}

function _get_sku_spec_value(key, sku) {
    let spec = (sku.specs || []).find(e => e.key === key);
    if (_.isEmpty(spec)) return '';
    if (spec.key === enums.NewSkuSpec.time) {
        return Number(spec.option.value);
    }
    return spec.option.value;
}

function _init_right(user_id) {
    return {
        user_id: user_id,
        order_id: '',
        sku_id: '',
        period: enums.Period.ALL,
        subject: enums.Subject.ALL,
        subject_check: 0,
        last_time: new Date(),
        status: enums.BooleanNumber.YES,
        ctime: new Date(),
        utime: new Date(),
    };
}


/**
 * 修改权益数量
 * @param right 权益
 * @param count 消耗次数
 * @returns string
 * @private
 */
async function _update_right_count(right, count = 1) {
    const ds = {
        last_time: new Date(),
        utime: new Date(),
    };
    if (right.limit !== enums.Infinite) {
        ds.count = right.count + count;
    }
    await db.collection(schema.user_right).updateOne({_id: right._id}, {$set: ds});
}


/**
 * 根据权益组查找权益
 * @param {number} user_id
 * @param {string} group
 * @returns {Promise<*>}
 */
async function _get_rights_by_group(user_id, group) {
    const cond = {
        user_id: user_id,
        group: group
    }
    let list = await db.collection(schema.user_right).find(cond).toArray();
    list = _filter_valid_date(list);
    return list;
}

async function _get_school_vip_count(schoolId, group) {
    const users = await db.collection(schema.user).find({sch_id: schoolId}).project({_id: 1}).toArray();
    if (!_.size(users)) return 0;
    const users_rights = await db.collection(schema.user_right).find({user_id: {$in: users.map(e => e)}, group: group}).project({_id: 1, user_id: 1}).toArray();
    if (!_.size(users_rights)) return 0;
    const set = new Set();
    users_rights.forEach(e => {
        set.add(e.user_id);
    });
    return set.size;
}

/**
 * 过滤有效学段科目信息
 * @param rights
 * @param period
 * @param subject
 * @returns {*[]}
 * @private
 */
function _filter_subject(rights = [], period, subject) {
    if (!_.size(rights)) return rights;
    return rights.filter(e => {
        if (!e.subject_check) return true;
        return (e.period === enums.Period.ALL || e.period.includes(period)) && (e.subject === enums.Subject.ALL || e.subject === subject);

    });
}

/**
 * 过滤有效次数
 * @param rights
 * @returns {*[]}
 * @private
 */
function _filter_valid_count(rights = []) {
    if (!_.size(rights)) return rights;
    return rights.filter(e => e.limit === enums.Infinite || e.limit > e.count);
}

/**
 * 过滤生效中的权益
 * @param rights
 * @returns {*[]}
 * @private
 */
function _filter_between_date(rights = []) {
    if (!_.size(rights)) return rights;
    return rights.filter(e => moment().isBetween(e.stime, e.etime));
}

/**
 * 过滤未过期的权益生效中/未生效
 * @param rights
 * @returns {*[]}
 * @private
 */
function _filter_valid_date(rights = []) {
    if (!_.size(rights)) return rights;
    return rights.filter(e => moment().isBefore(e.etime));
}

/**
 * 获取付费会员权益
 * @param rights
 * @returns {*[]}
 * @private
 */
function _filter_pay_right(rights = []) {
    const normal = rights.every(e => e.group === enums.NewVipType.normal);
    if (normal) {
        return rights;
    }
    return rights.filter(e => e.group !== enums.NewVipType.normal);
}
