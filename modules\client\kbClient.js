/**
 * 算法
 * wiki: https://wiki.iyunxiao.com/pages/viewpage.action?pageId=25232048
 */

const _ = require('lodash');
const URL = require('url');
const config = require('config');
const axios = require('axios');
const logger = require('../utils/logger');
const server = config.get('KB_API_SERVER');
const utils = require('../utils/utils');
const qs = require('querystring');

module.exports = {
    getQuestions,
    createTable,
    getExampaper,
    updateTable,
    deleteTableById,
    getNewNum,
    getBooks,
    getKnowledgeTree,
    getExampaperNoView,
    getExampapers,
    getTableById,
    getTableByRelevanceId,
    request,
    getEduFileById,
    getKnowledgeByIds,
    getEduToolById,
    getEduFileByIds,
    getEduToolByIds,
};

async function getQuestions(ids, fields_type = 'full') {
    if (!_.size(ids)) return [];
    let data ={
        question_ids: ids,
        fields_type,
    };

    // 系统试卷库
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: '/kb_api/v2/questions/',
        search: qs.stringify({
            api_key: server.appKey
        })
    });
    const result = await axios.post(url, data);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`KB获取试题信息失败: url: ${url}, status: ${result.status}, params: ${JSON.stringify(data)}`);
        throw new Error('获取试题信息失败');
    }
    return result.data;
}

async function createTable(table, userId) {
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: '/kb_api/v2/tw_specifications',
        search: qs.stringify({
            api_key: server.appKey
        })
    });
    const result = await axios.post(url, table);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`KB添加细目表失败: url: ${url}, status: ${result.status}, params: ${JSON.stringify(table)}`);
        throw new Error('添加细目表失败');
    }
    return result.data;
}

async function updateTable(table_id, table) {
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: `/kb_api/v2/tw_specifications/${table_id}`,
        search: qs.stringify({
            api_key: server.appKey
        })
    });
    const result = await axios.put(url, table);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`KB更新细目表失败: url: ${url}, status: ${result.status}, params: ${JSON.stringify(table)}`);
        throw new Error('更新细目表失败');
    }
    return result.data;
}

/**
 * 获取试卷详细
 * @param exampaper_id
 * @returns {Promise<T>}
 */
async function getExampaper(exampaper_id) {
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: `/kb_api/v2/exampapers/${exampaper_id}`,
        search: qs.stringify({
            api_key: server.appKey
        })
    });
    const result = await axios.get(url);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`KB获取试试卷息失败: url: ${url}, status: ${result.status}`);
        throw new Error('获取试卷失败');
    }
    return result.data;
}

/**
 * 获取试卷详细
 * 区别(不计入浏览量)
 * @param exampaper_id
 * @returns {Promise<T>}
 */
async function getExampaperNoView(exampaper_id) {
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: `/kb_api/v2/exampapers/no_view/${exampaper_id}`,
        search: qs.stringify({
            api_key: server.appKey
        })
    });
    const result = await axios.get(url);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`KB获取试试卷息失败: url: ${url}, status: ${result.status}`);
        throw new Error('获取试卷失败');
    }
    return result.data;
}

/**
 * 查询试卷列表
 * @param exampaperIds
 * @returns {Promise<[]>}
 */
async function getExampapers(exampaperIds) {
    const paperArray = [];
    if (!_.size(exampaperIds)) return paperArray;
    try {
        for (const ids of _.chunk(exampaperIds, 20)) {
            let url = URL.format({
                protocol: server.protocol,
                hostname: server.hostname,
                port: server.port,
                pathname: `/kb_api/v2/exampapers/${ids.join(',')}/list`,
                search: qs.stringify({
                    api_key: server.appKey
                })
            });
            const result = await axios.get(url);
            if (!_.size(result.data)) {
                continue;
            }
            result.data.forEach(e => paperArray.push(e));
        }
        return paperArray;
    } catch (e) {
        logger.error(e);
    }
}

async function getBooks(ids, fields_type= 'chapter') {
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: `/kb_api/v2/books/detail`,
        search: qs.stringify({
            api_key: server.appKey,
            ids: ids.join(','),
            fields_type: fields_type
        })
    });
    const result = await axios.get(url);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`KB获取教材失败: url: ${url}, status: ${result.status}`);
        throw new Error('获取试卷失败');
    }
    return result.data;
}


async function getKnowledgeTree(id) {
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: `kb_api/v2/knowledge_trees/${id}/`,
        search: qs.stringify({
            api_key: server.appKey
        })
    });
    const result = await axios.get(url);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`KB获取教材树失败: url: ${url}, status: ${result.status}`);
        throw new Error('获取试卷失败');
    }
    return result.data;
}


async function getNewNum() {
    try {
        let url = URL.format({
            protocol: server.protocol,
            hostname: server.hostname,
            port: server.port,
            pathname: `/kb_api/v2/resources/new/num`,
            search: qs.stringify({
                day: 7, //
                api_key: server.appKey
            })
        });
        let result = await client.axios.get(url);
        if (!result || result.status !== 200 || !result.data) {
            logger.error(`KB获取资源更新量失败: url: ${url}, status: ${result.status}`);
            throw new Error('获取资源更新量失败');
        }
        return result.data;
    } catch (e) {
        logger.error(e);
    }
}

async function getTableByRelevanceId(id, viewOnly = true) {
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: '/kb_api/v2/tw_specifications',
        search: qs.stringify({
            api_key: server.appKey,
            relevance_id: id,
            view_only: viewOnly === true ? 'true' : 'false'
        })
    });
    const result = await axios.get(url);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`KB获取教材树失败: url: ${url}, status: ${result.status}`);
        throw new Error('获取试卷失败');
    }
    return result.data;
}

async function getTableById(id, viewOnly = true) {
    let url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: `/kb_api/v2/tw_specifications/${id}/info`,
        search: qs.stringify({
            api_key: server.appKey,
            view_only: viewOnly === true ? 'true' : 'false'
        })
    });
    const result = await axios.get(url);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`KB获取细目表失败: url: ${url}, status: ${result.status}`);
        throw new Error('KB获取细目表失败');
    }
    const data = result.data;
    for (let i = 0; i < data.blocks.length; i++) {
        let block = data.blocks[i];
        for (let j = 0; j < block.questions.length; j++) {
            let question = block.questions[j];
            if ((question.score && question.score === 0) || !question.score)
                question.score = 5;
        }
    }
    return data;
}


async function deleteTableById(table_id) {
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: '/kb_api/v2/tw_specifications/' + table_id,
        search: qs.stringify({
            api_key: server.appKey,
        })
    });
    await axios.delete(url);
}

async function request(path, { pathParams, params, data, method } = {}) {
    try{
        let url = path;
        if (pathParams) {
            Object.keys(pathParams).forEach(key => {
                let value = pathParams[key];
                url = url.replace(new RegExp(`:${key}`), value);
            });
        }
        const baseURL = `${server.protocol}://${server.hostname}:${server.port}`;
        const response = await axios.request({
            method: method || 'get',
            baseURL: baseURL,
            url: url,
            ...(data ? {data: data} : {}),
            params: {
                api_key: server.appKey,
                ...(params ? params : {}),
            }
        });
        if (response.data && (response.data.code !== 0 && response.data.msg)) {
            logger.error(`KB请求失败: baseUrl: ${baseURL}, url: ${url}`);
            return null;
        }
        return response.data;
    } catch (e) {
        logger.error(e);
        throw new Error('KB');
    }
}

async function getEduFileById(id) {
    const data = await request('kb_api/v2/education_assistant_files/:id', {
        pathParams: {
            id: id
        }
    });
    return data;
}

async function getEduToolById(id) {
    const data = await request('kb_api/v2/education_assistant_tools/:id', {
        pathParams: {
            id: id
        }
    });
    return data;
}

async function getKnowledgeByIds(ids, fields = ['name']) {
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: '/kb_api/v2/knowledges/batch/',
        search: qs.stringify({
            api_key: server.appKey,
            ids: ids.join(','),
            filter_fields: fields.join(',')
        })
    });
    const result = await axios.get(url);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`KB获取知识点失败: url: ${url}, status: ${result.status}`);
        throw new Error('获取试卷失败');
    }
    return result.data;
}

async function getEduFileByIds(ids) {
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: `/kb_api/v2/education_assistant_files/${ids.join(',')}/list`,
        search: qs.stringify({
            api_key: server.appKey,
        })
    });
    const result = await axios.get(url);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`KB获取备课资源失败: url: ${url}, status: ${result.status}`);
        throw new Error('获取试卷失败');
    }
    return result.data;
}

async function getEduToolByIds(ids) {
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: `/kb_api/v2/education_assistant_tools/${ids.join(',')}/list`,
        search: qs.stringify({
            api_key: server.appKey,
        })
    });
    const result = await axios.get(url);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`KB获取教学工具失败: url: ${url}, status: ${result.status}`);
        throw new Error('获取试卷失败');
    }
    return result.data;
}
