#!/bin/bash
WEBPACKAGES=web_packages
rm -rf ${WEBPACKAGES}
BRANCH=${1}
ENV=${2}
PAGE_NAME=${3}
git clone -b ${BRANCH} ********************:dnr-platform/tiku_web.git ${WEBPACKAGES}
cd ${WEBPACKAGES}
npm config set sass_binary_site https://registry.npmmirror.com/node-sass/
npm install --registry https://registry.npmmirror.com
NODE_ENV=${ENV} npm run build
mv ./dist/index.html ./dist/${PAGE_NAME}.html
cd ..
if [ ! -d ./dist ]; then
    mkdir ./dist;
fi
cp -r ${WEBPACKAGES}/dist/* ./dist/
rm -rf ${WEBPACKAGES}
