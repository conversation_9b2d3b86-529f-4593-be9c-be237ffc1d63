const moment = require('moment');
const _ = require('lodash');
const mongodber = require('../../utils/mongodber');
const db = mongodber.use('tiku');
const enums = require('../../../bin/enum');
const schema = require('../../../bin/schema');

const DEFAULT_EXPIRE_MONTH = 3;

module.exports = {
    sync_school_vip,
    copy_vip_info,
    add_user_vip,
    add_school_vip,
    get_vip_user,
    get_school_vip_info,
}

/**
 * 同步基础版会员
 * @param school
 * @param userInfo
 * @param updateUser
 * @private
 */
function sync_school_vip(school, updateUser) {
    if (_.isEmpty(school)) return; // 没有学校信息
    // 会员类型
    if (!school.vip_type || ![enums.MemberType.YJ_ULTIMATE, enums.MemberType.YJ_PROFESSION].includes(school.vip_type)) return;
    // 会员日期
    if (!school.expired_time || moment().isAfter(school.expired_time)) return;

    // 同步学校会员信息
    const vip_rights = [];
    for (const key of Object.keys(enums.VipRightType)) {
        const obj = {};
        obj[enums.VipRightType[key]] = school[enums.VipRightType[key]] || 0;
        vip_rights.push(obj);
    }
    updateUser.vip_rights = vip_rights;
    updateUser.is_vip = moment().isBetween(school.start_time, school.expired_time);
    updateUser.start_time = school.start_time;
    updateUser.expired_time = school.expired_time;
    updateUser.vip_type = school.vip_type;
}

function copy_vip_info(from, to) {
    if (_.isEmpty(from) || _.isEmpty(to)) return;
    to.isVip = from.is_vip || false;
    to.vipType = from.vip_type;
    to.startTime = from.start_time && from.start_time.getTime();
    to.expiredTime = from.expired_time && from.expired_time.getTime();
    if (!Object.values(enums.MemberType).includes(to.vipType)) {
        to.vipType = enums.MemberType.TIKU_PERSONAL;
    }
}

function add_user_vip(user, userInfo) {
    user.isVip = false;
    if (_.isEmpty(userInfo)) return;
    user.isVip = moment().isBetween(userInfo.start_time, userInfo.expired_time);
    if (!user.isVip) return;
    if (Object.values(enums.MemberType).includes(userInfo.vip_type)) {
        user.vipType = userInfo.vip_type;
    } else {
        user.vipType = enums.MemberType.TIKU_PERSONAL;
    }
    user.startTime = userInfo.start_time && userInfo.start_time.getTime();
    user.expiredTime = userInfo.expired_time && userInfo.expired_time.getTime();
    user.expireMonth = DEFAULT_EXPIRE_MONTH;
}

const basic_types = [
    enums.MemberType.YJ_PROFESSION,
    enums.MemberType.YJ_ULTIMATE
]

function add_school_vip(user, school) {
    if (_.isEmpty(school)) return;
    const isVip = moment().isBetween(school.start_time, school.expired_time);
    if (!isVip) return;
    if (!basic_types.includes(school.vip_type)) { // 非基础版
        const teachers = _.get(school, 'teachers',  []);
        if (!teachers.includes(user.id)) {
            return;
        }
    }
    user.isVip = true;
    user.vipType = school.vip_type;
    user.startTime = school.start_time && school.start_time.getTime();
    user.expiredTime = school.expired_time && school.expired_time.getTime();
    user.expireMonth = school.expire_month || DEFAULT_EXPIRE_MONTH;
}

async function get_vip_user(id) {
    const db_user = await db.collection('user').findOne({_id: id});
    const user = {
        id: db_user._id,
        userId: db_user._id,
        ucId: db_user.uc_id,
        name: db_user.name,
        schoolId: db_user.sch_id,
        schoolName: db_user.sch_name,
        province: db_user.province,
        city: db_user.city,
        role: db_user.role,
        grade: db_user.grade,
        avatar: db_user.avatar,
        need_bind: db_user.need_bind,
        qxid: db_user.qxid,
        // isVip: false
    };
    // add_user_vip(user, db_user);
    // if (!db_user.sch_id || (db_user.is_vip && user.vipType === enums.MemberType.TIKU_PERSONAL)) return user;
    // const school = await db.collection('school_info').findOne({_id: db_user.sch_id});
    // add_school_vip(user, school);
    return user;
}

async function get_school_vip_info(school) {
    const vip = {}
    if (_.isEmpty(school)) return vip;
    // 会员类型
    if (!school.vip_type || ![enums.MemberType.YJ_ULTIMATE, enums.MemberType.YJ_PROFESSION].includes(school.vip_type)) return vip;
    // 会员日期
    if (!school.expired_time || moment().isAfter(school.expired_time)) return vip;
    // 同步学校会员信息
    const vip_rights = [];
    for (const key of Object.keys(enums.VipRightType)) {
        const obj = {};
        obj[enums.VipRightType[key]] = school[enums.VipRightType[key]] || 0;
        vip_rights.push(obj);
    }
    vip.vip_rights = vip_rights;
    vip.is_vip = moment().isBetween(school.start_time, school.expired_time);
    vip.start_time = school.start_time;
    vip.expired_time = school.expired_time;
    vip.vip_type = school.vip_type;
    return vip;
}
