let logger = require('../utils/logger');

const RETURN_CODE = {
	'OK': 00,                   // 处理成功
	'URL_ERROR': 01,            // api错误
	'AUTH_ERROR': 02,           // app_key, app_seceret认证信息错误
	'PARAMETERS_ERROR': 03,     // 上送参数错误
	'HANDLE_ERROR': 04,         // 业务处理错误
	'NULL_ERROR': 05,           // 空数据
	'EXCEED_FRQ_ERROR': 06,     // 访问频率过快
	'ILLEGAL_USER': 07,         // 被封的用户
	'NEED_VIP_ERROR': 08,       // 非会员
	'OVER_LIMIT_ERROR': 09,     // 超过限制
	'TIMES_NOT_ENOUGH_ERROR': 10,     // 超过限制
	'COOKIE_INVALID': 11,     // cookie过期
};

const RETURN_MSG = {
	'OK': 'OK',                                 // 处理成功
	'URL_ERROR': 'api not found',               // api错误
	'AUTH_ERROR': 'authentication error',   	// app_key, app_seceret认证信息错误
	'PARAMETERS_ERROR': 'parameters error',     // 上送参数错误
	'HANDLE_ERROR': 'servercie error',          // 业务处理错误
	'NULL_ERROR': 'cannot query data',          // 查询不到数据
	'EXCEED_FRQ_ERROR': 'api freq out of limit',// 访问频率过快
	'ILLEGAL_USER': 'user is untrusted',        // 被封的用户
    'NEED_VIP_ERROR': 'user must be vip',       // 用户必须为vip才可以访问
    'OVER_LIMIT_ERROR': 'api over limit ',      // 超过限制
	'TIMES_NOT_ENOUGH_ERROR': 'times not enough',                     // 次数不足
	'COOKIE_INVALID': 'cookie invalid or expired',     // cookie过期
};

function ResponseWrapper(req, res){
    this.req = req;
    this.res = res;
}

ResponseWrapper.prototype.error = function(type, desc, data){
	this.res.status(200);
	const msg = desc ? desc : RETURN_MSG[type];

	let logCtx = this.req.logCtx || {};
	const user = this.req.user || {};
	logCtx.userId = user.userId;
	logCtx.role = user.role;
	logCtx.vipType = user.vipType;
	logCtx.isVip = user.isVip;

	const now = Date.now();
	const timeSpan = (now - logCtx.start) || 0;
	let threshold = '';
	if (timeSpan >= 1000) {
		threshold = '>1s';
	}

	logger.error(`[SN_${logCtx.sn}]==code:${RETURN_CODE[type]}== ${JSON.stringify(logCtx)}==errMsg:${JSON.stringify(msg)}== ${timeSpan}ms ${threshold}`);
	return this.res.json({
		code: RETURN_CODE[type],
		msg: msg,
        data: data || null,
    });
}

ResponseWrapper.prototype.succ = function(data){
    this.res.status(200);
	let logCtx = this.req.logCtx || {};
	const user = this.req.user || {};
	logCtx.userId = user.userId;
	logCtx.role = user.role;
	logCtx.vipType = user.vipType;
	logCtx.isVip = user.isVip;

	const now = Date.now();
	const timeSpan = (now - logCtx.start) || 0;
	let threshold = '';
	if (timeSpan >= 1000) {
		threshold = '>1s';
	}

	logger.info(`[SN_${logCtx.sn}]==code:${RETURN_CODE['OK']}==msg${JSON.stringify(logCtx)}== ${timeSpan}ms ${threshold}`);
    return this.res.json({
        code: RETURN_CODE['OK'],
        msg: RETURN_MSG['OK'],
        data: data,
    });
}

ResponseWrapper.prototype.send = function(data){
    this.res.status(200);
    data.data = data.data || null;
    return this.res.json(data);
}

module.exports = ResponseWrapper;
