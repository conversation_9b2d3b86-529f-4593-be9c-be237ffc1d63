const express = require('express');
const router = express.Router();

const resources = require('../../modules/resources_api/v1/index.js');


router.get('/exampaper/list', resources.getExampaperList);
router.get('/exampaper/:id', resources.getExampaperInfo);
router.post('/exampaper/word', resources.postExampaperByWord);
router.put('/exampaper/:id', resources.putExampaperById);
router.put('/question/:id', resources.putQuestionById);
router.delete('/exampaper/:id', resources.deleteExampaperById);
router.post('/question/type/batch', resources.batchModifyQuestionType);
// 用户上传
router.get('/upload/list', resources.getUploadList);
router.put('/upload/exampaper/:id', resources.putUploadById);
router.delete('/upload/exampaper/:id', resources.deleteUploadById);

module.exports = router;
