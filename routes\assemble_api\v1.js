const express = require('express');
const router = express.Router();
// const basket = require('../../modules/assemble_api/v1/basket');
const exampaper = require('../../modules/assemble_api/v1/exampaper');
const tw_specification = require('../../modules/assemble_api/v1/tw_specification');
const apikey = require('../../modules/middlewares/apikey');
const scantron = require('../../modules/assemble_api/v1/scantron');
const question_template = require('../../modules/assemble_api/v1/question_template');
const multer  = require('multer');
const upload = multer({dest: './uploads'});

router.use(apikey('KB'));

// 已使用新版
// // 取试题篮中试题情况
// router.get('/basket/questions/', basket.get_questions);
// // 向试题篮中提交试题
// router.post('/basket/questions/', basket.post_questions);
// // 从试题篮中删除试题
// router.delete('/basket/questions/:qids', basket.delete_questions);
//
// // 取试题篮
// router.get('/basket/', basket.get_basket);
// // 整体更新试题篮
// router.put('/basket/', basket.put_basket);
// // 删除试题篮
// router.delete('/basket/', basket.delete_basket);

/* 组卷相关接口 */
// list ~ :exampaper_id, 如果list写在后面会出现list是ObjectID
router.get('/exampapers/list/', exampaper.list_exampapers);
router.get('/exampapers/last/', exampaper.lastExampaper);
router.post('/exampapers/', exampaper.post_exampaper);
router.get('/exampapers/:exampaper_id/', exampaper.get_exampaper_async);

// 组卷修改(上传试卷复核使用)
router.put('/exampapers/:exampaper_id/', exampaper.put_exampaper);
router.delete('/exampapers/:exampaper_id/', exampaper.delete_exampaper);
// router.post('/knowledge/question_num/', exampaper.getQuestionNumByKnowledge);

router.post('/tw_specifications', tw_specification.createTable);
router.get('/tw_specifications/list', tw_specification.getTableList);
// router.get('/tw_specifications/hot', tw_specification.getHotTable);
router.put('/tw_specifications/:table_id', tw_specification.updateTable);
// router.get('/tw_specifications/:table_id/info', tw_specification.getTableInfo);
router.get('/exampapers/:exampaper_id/tw_specification', tw_specification.getTableFromExampaper); // 查看试卷细目表
router.post('/tw_specifications/exampapers', tw_specification.createExampaperAsync); // 细目表组卷
router.post('/exampapers/:exampaper_id/exampapers', tw_specification.fakeExampaperAsync); // 平行组卷
router.delete('/tw_specifications/:table_id', tw_specification.deleteTable); // 删除细目表
router.get('/tw_specifications/xlsx', tw_specification.downloadTable); // 下载细目表
router.post('/tw_specifications/xlsx', upload.any(), tw_specification.createTableByExcel);
router.post('/xlsx', tw_specification.downloadTable);
router.post('/tw_specifications/knowledge/', tw_specification.getUnableKnowledge);

// router.post('/knowledges/exampaper', exampaper.knowledges_exampaper);
router.post('/knowledges/exampaper', exampaper.algo_knowledges_exampaper);
router.get('/exampapers/:exampaper_id/scantron', scantron.getScantron);
router.get('/dtk/gateway', scantron.getDTKGateway);

// 同步阅卷考试
router.post('/exampapers/:exampaper_id/yj', exampaper.postExampaperToYj);
router.post('/exampapers/:exampaper_id/bind-yj', exampaper.postExampaperBindYj);
router.get('/exampapers/:exampaper_id/yj/list', exampaper.getExampaperYjExamList);

// 试题模板
router.get('/question/template/list', question_template.list);
router.post('/question/template', question_template.create);
router.delete('/question/template/:template_id', question_template.remove);

module.exports = router;
