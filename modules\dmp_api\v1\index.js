const ResponseWrapper = require('../../middlewares/response_wrapper');
const mongodber = require('../../utils/mongodber');
const ObjectID = require('mongodb').ObjectID;
const config = require('config');
const URL = require('url');
const qs = require('qs');
const _ = require('underscore');
const request = require('request');
const client = require('zipkin-middleware').client;
const db = mongodber.use('tiku');
const KBAPISERV = config.get('KB_API_SERVER');


function getErratum(req, res){
	let responseWrapper = new ResponseWrapper(req, res);

	let errId = req.params.err_id;

	db.collection('resource_erratum').findOne({
		_id: ObjectID(errId)
	}, function(err, doc){

		if (err){
			return responseWrapper.error('HANDLE_ERROR', err.message);
		}

		if (!doc){
			return responseWrapper.error('NULL_ERROR');
		}

		let id = doc.id;
		db.collection('resource_erratum').find({
			id: id,
			status: 'committed'
		}, {
			_id: 1
		}).toArray(function(err, docs){
			if (err){
				return responseWrapper.error('HANDLE_ERROR', err.message);
			}
			doc.relevance = _.pluck(docs, '_id');
			return responseWrapper.succ(doc);
		});
	});
}

function _isEmpty(x){
	return x && x != '' && x != '全部';
}

function getErratumList(req, res){

	let responseWrapper = new ResponseWrapper(req, res);

	let cond = {};

	if (_isEmpty(req.query.period))
		cond.period = req.query.period;

	if (_isEmpty(req.query.subject))
		cond.subject = req.query.subject;

	if (req.query.type)
		cond.type = req.query.type;

	if (req.query.user_id)
		cond.user_id = req.query.user_id;

	if (req.query.status)
		cond.status = req.query.status;

	let offset = req.query.offset ? req.query.offset : 0;
	let limit = req.query.limit ? req.query.limit : 10;
	let cursor = db.collection('resource_erratum').find(cond);
    cursor.count(function(err, count){
        if (err){
            return responseWrapper.error('HANDLE_ERROR', err.message);
        }
        if (count === 0){
            return responseWrapper.error('NULL_ERROR');
        }
        cursor.skip(Number(offset)).limit(Number(limit)).toArray(function(err, docs){

			let questionIds = [];

			_.each(docs, function(doc){
				if (doc.type === 'question')
					questionIds.push(doc.id);

				if (doc.ctime){
					doc.ctime = doc.ctime.toLocaleDateString();
					doc.utime = doc.utime.toLocaleDateString();
				}

				if (doc.status === 'corrected') {
					doc.msg = '您的报错经过审核未被采纳，十分感谢您对我们产品的帮助与支持。';
				} else if (doc.status === 'modified') {
					doc.msg = '您的报错经过审核已确认有效，十分感谢您对我们产品的帮助与支持。';
				} else {
					doc.msg = '您的报错还未审核，十分感谢您对我们产品的帮助与支持。';
				}
			});

			let questionURL = URL.format({
				protocol: KBAPISERV.protocol,
				hostname: KBAPISERV.hostname,
				pathname: '/kb_api/v2/questions/',
				port: KBAPISERV.port,
				search: qs.stringify({
					api_key: req.apiKey
				})
			});

			let postBody = {
				question_ids: questionIds,
				fields_type: 'stem'
			};

			request.post({
				url: questionURL,
				headers: {
					'content-type': 'application/json'
				},
				body: JSON.stringify(postBody)
			}, function(error, response, _body){

				if (response.statusCode !== 200){
					return responseWrapper.error('HANDLE_ERROR');
				}

				let body = JSON.parse(_body);

				_.each(docs, function(doc){
					let id = doc.id;
					let question = _.find(body, function(data){
						return data.id === id;
					});

					delete question.comment;
					delete question.refer_exampapers;
					delete question.core_knowledges;
					delete question.knowledges;
					delete question.comment;
					delete question.difficulty;
					delete question.refer_times;
					delete question.use_type;
					delete question.comment;
					delete question.elite;

					doc.data = question;
				});

				responseWrapper.succ({
					total_num: count,
					erratums: docs
				});
			});
		});
	});
}

function updateErratum(req, res){
	let responseWrapper = new ResponseWrapper(req, res);
	let errId = req.params.err_id;
	if (!req.body){
		return responseWrapper.error('PARMENTERS_ERROR');
	}

	let status = req.body.status;

	db.collection('resource_erratum').updateOne({
		_id: ObjectID(errId)
	}, {
		$set: {
			status: status,
			utime: new Date()
		}
	}, function(err, writeResult){
		db.collection('resource_erratum').findOne({
			_id: ObjectID(errId)
		}, function(err, doc){
			let id = doc.id;
			let type = doc.type;
			db.collection('resource_erratum').find({
				id: id,
				type: type
			}).toArray(function(err, docs){
				if (err){
					return responseWrapper.succ({
						erratums: []
					});
				}
				return responseWrapper.succ({
					erratums: docs
				});
			});
		});
	});
}

const _getErratumStatusMsg = (status) => {
	let resObj = {
		status : '',
		msg : ''
	};

	switch (status) {
		case 'rejected': {
			resObj.status = '已审查，暂未采纳';
			resObj.msg = '您的报错经过审查未被采纳，十分感谢您对我们题库产品的帮助与支持。';
			break;
		}
		case 'fixed': {
			resObj.status = '已审查，确认有效';
			resObj.msg = '您的报错经过审查已被确认有效，十分感谢您对我们题库产品的帮助与支持。';
			break;
		}
		case 'committed': {
			resObj.status = '未审查';
			resObj.msg = '您的报错还未审查，十分感谢您对我们题库产品的帮助与支持。';
			break;
		}
		default: {
			resObj.status = '未审查';
			resObj.msg = '您的报错还未审查，十分感谢您对我们题库产品的帮助与支持。';
		}
	}

	return resObj;
};

const getErratumListNew = async (req, res) => {
	let responseWrapper = new ResponseWrapper(req, res);
	try {
		let KBPSERV = config.get('KBPLAT_API_SERVER');
		let queryObj = {};
		queryObj.api_key = KBPSERV.appKey;
		if (_isEmpty(req.query.period))
			queryObj.period = req.query.period;
		if (_isEmpty(req.query.subject))
			queryObj.subject = req.query.subject;
		if (req.query.type)
			queryObj.resource_type = req.query.type;
		if (req.user)
			queryObj.reporter_id = req.user.id;
		if (req.query.status)
			queryObj.status = req.query.status;
		if (req.query.offset)
			queryObj.offset = req.query.offset;
		if (req.query.limit)
			queryObj.limit = req.query.limit;
		queryObj.query_from = 'tiku';

		//从kbp获取数据
		let kbpUrl = URL.format({
			protocol: KBPSERV.protocol,
			hostname: KBPSERV.hostname,
			pathname: '/kb_api/v2/erratums',
			port: KBPSERV.port,
			search: qs.stringify(queryObj)
		});
		let kbpData = await client.axios.get(kbpUrl);
		let erratums = kbpData.data.data.erratums;
		if (!Array.isArray(erratums) || !erratums.length) {
			return responseWrapper.succ({
				total_num : kbpData.data.data.total_num,
				erratums : []
			});
		}
		let resourceArr = [];
		for (let erratum of erratums) {
			if (erratum.resource_id)
				resourceArr.push(erratum.resource_id);
		}

		//从kb获取数据
		let kbUrl = URL.format({
			protocol: KBAPISERV.protocol,
			hostname: KBAPISERV.hostname,
			port: KBAPISERV.port,
			pathname: 'kb_api/v2/questions',
			search: qs.stringify({
				api_key:KBAPISERV.appKey
			})
		});
		let kbData = await client.axios({
			url: kbUrl,
			method: 'post',
			data: {
				question_ids: resourceArr || []
			}
		});
		let questions = kbData.data;
		let resObj = {
			total_num : kbpData.data.data.total_num,
			erratums : []
		};
		for (let tmp of erratums) {
			let obj = {};
			obj._id = tmp.id;
			obj.id = tmp.resource_id;
			obj.type = tmp.resource_type;
			let date = new Date(tmp.ctime).toLocaleDateString();
			let time = new Date(tmp.ctime).toLocaleTimeString();
			let timeArr = time.split(':');
			date += ' ' + timeArr[0] + ':' + timeArr[1];
			obj.ctime = date;
			obj.user_id = tmp.reporter_id;
			let msgObj = _getErratumStatusMsg(tmp.status);
			obj.status = msgObj.status;
			obj.msg = msgObj.msg;
			obj.subject = tmp.subject;
			obj.period = tmp.period;
			obj.data = {};
			for (let question of questions) {
				if (obj.id === question.id)
					obj.data = question;
			}
			obj.comment = {};
			obj.comment.err_msg = tmp.comment.err_msg;
			obj.comment.err_fields = tmp.comment.fields;
			obj.comment.images = tmp.comment.images;
			resObj.erratums.push(obj);
		}

		resObj.erratums.sort((a, b) => (new Date(b.ctime).getTime() - new Date(a.ctime).getTime()));
		return responseWrapper.succ(resObj);
	} catch (error) {
		return responseWrapper.error('HANDLE_ERROR', error.message);
	}
};

module.exports = {
	getErratum: getErratum,
	// getErratumList: getErratumList,
	getErratumList: getErratumListNew,
	updateErratum: updateErratum
};
