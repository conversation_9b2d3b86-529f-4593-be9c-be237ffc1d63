/**
 * 请求日志记录中间件测试脚本
 * 用于验证 reco_data_log 表的数据记录功能
 */

const mongodber = require('../modules/utils/mongodber');
const { schema } = require('../modules/utils/constants');
const config = require('config');

// 测试函数
async function testRequestLogger() {
    console.log('开始测试请求日志记录功能...\n');
    
    try {
        // 初始化数据库连接
        const mongodbs = config.get('MONGODBS');
        await new Promise((resolve, reject) => {
            mongodber.init(mongodbs, (err) => {
                if (err) reject(err);
                else resolve();
            });
        });
        
        console.log('✓ 数据库连接成功');
        
        // 获取 tiku 数据库
        const db = mongodber.use('tiku');
        const collection = db.collection(schema.reco_data_log);
        
        // 查询最近的日志记录
        const recentLogs = await collection
            .find({})
            .sort({ timestamp: -1 })
            .limit(5)
            .toArray();
        
        console.log(`\n✓ 找到 ${recentLogs.length} 条最近的日志记录\n`);
        
        // 显示日志记录详情
        recentLogs.forEach((log, index) => {
            console.log(`--- 日志记录 ${index + 1} ---`);
            console.log(`时间: ${log.timestamp}`);
            console.log(`方法: ${log.method}`);
            console.log(`路由: ${log.route}`);
            console.log(`查询参数: ${JSON.stringify(log.query)}`);
            console.log(`请求体: ${JSON.stringify(log.body).substring(0, 100)}...`);
            console.log(`用户: ${log.user_name || '未登录'} (ID: ${log.user_id || 'N/A'})`);
            console.log(`IP: ${log.ip}`);
            
            if (log.response) {
                console.log(`响应状态: ${log.response.statusCode}`);
                console.log(`处理时长: ${log.response.duration}ms`);
            }
            console.log('');
        });
        
        // 统计信息
        const totalCount = await collection.countDocuments();
        const todayCount = await collection.countDocuments({
            timestamp: { $gte: new Date(new Date().setHours(0, 0, 0, 0)) }
        });
        
        console.log('--- 统计信息 ---');
        console.log(`总记录数: ${totalCount}`);
        console.log(`今日记录数: ${todayCount}`);
        
        // 测试插入一条记录
        const testLog = {
            method: 'TEST',
            route: '/test/request_logger',
            path: '/test/request_logger',
            query: { test: true },
            body: { message: '这是一条测试记录' },
            params: {},
            headers: {
                'user-agent': 'Test Script',
                'content-type': 'application/json'
            },
            ip: '127.0.0.1',
            timestamp: new Date(),
            user_id: null,
            user_name: null,
            response: {
                statusCode: 200,
                duration: 10,
                timestamp: new Date(),
                ok: true
            }
        };
        
        await collection.insertOne(testLog);
        console.log('\n✓ 成功插入测试记录');
        
        // 创建索引（如果需要）
        await collection.createIndex({ timestamp: -1 });
        await collection.createIndex({ user_id: 1 });
        await collection.createIndex({ route: 1 });
        console.log('✓ 索引创建/更新成功');
        
        console.log('\n测试完成！请求日志记录功能正常工作。');
        
    } catch (error) {
        console.error('\n✗ 测试失败:', error);
    } finally {
        // 关闭数据库连接
        process.exit(0);
    }
}

// 运行测试
testRequestLogger();