/*
 * Desc: the route of kb api
 * Author: guo<PERSON><PERSON>
 */

const router = require('express').Router();
const config = require('config');
const transmiter = require('../../modules/kb_api/v2/transmiter');
const downloader = require('../../modules/kb_api/v2/downloader');
const KBSERVCFG = config.get('KB_API_SERVER');

// kb api key middleware
router.use(function (req, res, next) {
    req.apiKey = KBSERVCFG.appKey;
    next();

});

// 请求教材信息（打包信息）
router.get('/books/', transmiter.transmit);
// 请求某教材下所有章节及其知识点集
router.get('/books/:book_id/', transmiter.transmit);
// 请求教材版本下的所有教材数据
router.get('/books/detail', transmiter.transmit);

/* 电子书相关接口 */
// 请求某学科下图书列表
router.get('/ebooks/', transmiter.transmit);
// 获取热门书单
router.get('/ebooks/popularity/', transmiter.transmit);

// 获取全局资源统计信息
router.get('/resources/profile/new/', transmiter.transmit);

// 通过试卷id查看卷子
router.get('/exampapers/:exampaper_id/', downloader.getExampaperInfo);
// 知识点/细分/对象获取试题
router.post('/exampapers/by_search/', downloader.exampaperBySearch)

// 返回试题资源的统计信息
router.get('/questions/filters/', downloader.questionsFilters)
// 知识点/细分/对象获取试题
router.post('/questions/by_search/', downloader.questionBySearch)

// 获取全量二级地区
router.get('/regions/simple/', transmiter.transmit);

// 请求知识树信息（打包信息）
router.get('/knowledge_trees/', transmiter.transmit);
// 通过id请求某知识树下所有节点及其知识点集
router.get('/knowledge_trees/:knowledge_tree_id/', transmiter.transmit);

// 查看单元测专辑
router.get('/album/list/', transmiter.transmit);

module.exports = router;
