#!/usr/bin/env node
const MongoClient = require('mongodb').MongoClient;
const program = require('commander');
const chalk = require('chalk');
const _ = require('underscore');

program
	.usage('<option> [code string]')
	.option('-s, --source', 'KB MongoDB URI')
	.option('-t, --target', 'TIKU MongoDB URI');

program.on('--help', function () {
	console.log('# Example');
	console.log();
	console.log(chalk.gray('node syncQuestionNums.js -s xxx -t xxx'));
	console.log();
})

function help () {
	program.parse(process.argv);
	if (program.args.length < 1) 
		return program.help();
}
help();

const run = async (kb, tiku) => {
    let kbclient = await MongoClient.connect(kb);
    let kbDB = kbclient.db('kb');
    let tikuclient = await MongoClient.connect(tiku);
    let tikuDB = tikuclient.db('tiku');

    let knowCur = await kbDB.collection('knowledge').find().project({
        questions: 1
    });
    await tikuDB.collection('question_num').deleteMany({});
    let hasNext = await knowCur.hasNext();
    while(hasNext) {
        let doc = await knowCur.next();
        let group = _.groupBy(doc.questions, (x) => {
            return x.type;
        });
        for (let ix in group) {
            group[ix] = _.countBy(group[ix], (x) => {
                return x.diff;
            });
        }
        doc.questions = group;
        tikuDB.collection('question_num').insertOne(doc);
        hasNext = await knowCur.hasNext();
    }
    kbclient.close();
    tikuclient.close();
}
run(program.args[0], program.args[1]);

