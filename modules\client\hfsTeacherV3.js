/**
 * 好分数教师端
 * wiki:
 */

const _ = require('lodash');
const URL = require('url');
const config = require('config');
const axios = require('axios');
const logger = require('../utils/logger');
const SERVER = config.get('HFS_TEACHER_V3_SERVER');
const qs = require('querystring');
const utils = require('../utils/utils');
const options = { headers: {}, timeout: 50000 };
const subject_utils = require('../utils/subject_utils');
const env = process.env.NODE_ENV;

module.exports = {
    getExamList,
    getPaperQuestions,
    getClassPaperStatistics,
    getTeacherInfoById,
    getTeacherInfoByToken,
    getTeacherExamList,
    getExamPaperQuestionDetail,
    getExamPaperClassFocusNeededStudents,
    getExamPaperClassComparison,
    getExamPaperClasses,
    getExamPaperStudentQuestionAnswer,
    getExamPaperQuestionAnswerPictures,
    getExamPaperQuestionAnswerPictureUrl,
};

const temp_teacher_id = '000000003273405528489984';
const temp_class_id = '000000000000000240787910';

function _is_test() {
    // if (!env || env === 'development' || env === 'test') {
    //     return true;
    // }
    return false;
}
/**
 * 考后巩固组卷
 * @param teacherId 教师ID
 * @returns {Promise<*>}
 */
async function getExamList (teacherId) {
    if (_is_test()) {
        teacherId = temp_teacher_id;
    }
    let url = URL.format({
        protocol: SERVER.protocol,
        hostname: SERVER.hostname,
        pathname: '/v3/teachers/exams',
        port: SERVER.port,
        search: qs.stringify({
            teacherId: teacherId,
            appKey: SERVER.appKey,
        })
    });

    let result = await axios.get(url, options).then(utils.handler).catch(error => {
        logger.error(error);
    });
    // if (!result.data || result.data.code !== 0) {
    //     logger.error(`好分教师端获取考试列表失败,url:[${url}]`);
    //     throw new Error('获取考试列表失败');
    // }
    // return _.get(result, 'data.data', null);
    const new_result = [];
    if (_.size(result)) {
        for (const exam of result) {
            if (!_.size(exam.classes)) continue;
            const subject = subject_utils.regularSubject(exam.subject);
            if (!subject) continue;
            exam.regular_subject = subject;
            for (const c of exam.classes) {
                if (!c.name.includes('班')) c.name = c.name + '班';
            }
            new_result.push(exam);
        }
    }
    return new_result;
}

/**
 * 获取试题详细
 * @param userId 用户ID/教师ID
 * @param examId number 考试ID
 * @param paperId string 科目ID
 * @param classId string 班级ID
 * @returns {Promise<void>}
 */
async function getPaperQuestions(userId, examId, paperId, classId) {
    if (_is_test()) {
        userId = temp_teacher_id;
        classId = temp_class_id;
    }
    let url = URL.format({
        protocol: SERVER.protocol,
        hostname: SERVER.hostname,
        pathname: '/v3/classes/question-detail',
        port: SERVER.port,
        search: qs.stringify({
            teacherId: userId,
            examId: examId,
            paperId: paperId,
            classId: classId,
            appKey: SERVER.appKey,
        })
    });
    let result = await axios.get(url, options);
    if (!result.data || result.data.code !== 0) {
        logger.error(`好分数教师端获取试题详情失败,url:[${url}]`);
        return null;
    }
    // const list = _.get(result, 'data.data', null);
    // return _question_sort(list);
    return _.get(result, 'data.data', null);
}

/**
 * 获取班级数据统计
 * @param userId 用户ID/教师ID
 * @param examId 考试ID
 * @param paperId 科目ID
 * @param classId 班级ID
 * @returns {Promise<unknown>}
 */
async function getClassPaperStatistics(userId, examId, paperId, classId) {
    if (_is_test()) {
        userId = temp_teacher_id;
        classId = temp_class_id;
    }
    let url = URL.format({
        protocol: SERVER.protocol,
        hostname: SERVER.hostname,
        pathname: '/v3/classes/exams/papers/statistics',
        port: SERVER.port,
        search: qs.stringify({
            teacherId: userId,
            examId: examId,
            paperId: paperId,
            classId: classId,
            scoreType: 0,
            appKey: SERVER.appKey,
        })
    });
    let result = await axios.get(url, options).then(utils.handler);
    return result;
}

async function getTeacherInfoById(id) {
    let url = URL.format({
        protocol: SERVER.protocol,
        hostname: SERVER.hostname,
        pathname: '/v3/teachers/info/tiku',
        port: SERVER.port,
        search: qs.stringify({
            userId: id,
            appKey: SERVER.appKey,
        })
    });
    let result = await axios.get(url, options).then(utils.handler);
    return result;
}


async function getTeacherInfoByToken(token) {
    let url = URL.format({
        protocol: SERVER.protocol,
        hostname: SERVER.hostname,
        pathname: '/v3/teachers/info/hfs-token',
        port: SERVER.port,
        search: qs.stringify({
            hfsToken: token,
            appKey: SERVER.appKey,
        })
    });
    let result = await axios.get(url, options).then(utils.handler);
    return result;
}


async function getTeacherExamList(userId) {
    if (_is_test()) {
        userId = temp_teacher_id;
    }
    let url = URL.format({
        protocol: SERVER.protocol,
        hostname: SERVER.hostname,
        pathname: '/v3/teachers/exam/list',
        port: SERVER.port,
        search: qs.stringify({
            teacherId: userId,
            appKey: SERVER.appKey,
        })
    });
    let result = await axios.get(url, options);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`KB获取试题信息失败: url: ${url}`);
        return null;
    }
    return _.get(result, 'data.data', null);
}

async function getExamPaperQuestionDetail(userId, examId, paperId) {
    if (_is_test()) {
        userId = temp_teacher_id;
    }
    let url = URL.format({
        protocol: SERVER.protocol,
        hostname: SERVER.hostname,
        pathname: '/v3/teachers/exam/paper/question-detail',
        port: SERVER.port,
        search: qs.stringify({
            teacherId: userId,
            examId: examId,
            paperId: paperId,
            appKey: SERVER.appKey,
        })
    });
    let result = await axios.get(url, options);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`KB获取试题信息失败: url: ${url}`);
        return null;
    }
    const question_info = _.get(result, 'data.data', null);
    // if (question_info) {
    //     if (_.size(question_info.class_info)) { // 班级排序
    //         _class_sort(question_info.class_info);
    //         for (const class_info of question_info.class_info) {
    //             class_info.question_score = _question_sort(class_info.question_score);
    //         }
    //     }
    //     // 试题排序
    //     question_info.questions = _question_sort(question_info.questions);
    // }
    return question_info;
}

function _question_sort(list) {
    if (!_.size(list)) return list;
    let level = 0;
    for (const q of list) {
        const key_arr = (q.key || '').split('.');
        if (_.size(key_arr) > level) {
            level = _.size(key_arr);
        }
    }
    if (level === 0) return list;
    for (const q of list) {
        const key_arr = (q.key || '').split('.');
        for (let i = 1; i <= level; i++) {
            try {
                q[`key_sort_${i}`] = key_arr.length > i ? Number(key_arr[i]) : 999;
            } catch (e) {
                q[`key_sort_${i}`] = 999;
            }
        }
    }
    const arr = Array.from({length: level}, (item, index) => index);
    list =  _.orderBy(list, arr.map(e => `key_sort_${e + 1}`), arr.map(e => 'asc'));
    for (const q of list) {
        arr.forEach(e => {
            delete q[`key_sort_${e + 1}`];
        });
    }
    return list;
}

function _class_sort(list) {
    if (!_.size(list)) return;
    list.sort((a, b) => {
        const numA = utils.matchNumber(a.name || a.class_name);
        const numb = utils.matchNumber(b.name || b.class_name);
        return numA - numb;
    })
}


async function getExamPaperClassFocusNeededStudents(userId, examId, paperId, classId, subject) {
    if (_is_test()) {
        userId = temp_teacher_id;
    }
    let url = URL.format({
        protocol: SERVER.protocol,
        hostname: SERVER.hostname,
        pathname: '/v3/classes/exam/paper/subjects/focus-needed-students',
        port: SERVER.port,
        search: qs.stringify({
            teacherId: userId,
            examId: examId,
            paperId: paperId,
            classId: classId,
            subject: subject,
            scoreType: 0,
            appKey: SERVER.appKey,
        })
    });
    let result = await axios.get(url, options);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`KB获取试题信息失败: url: ${url}`);
        return null;
    }
    return _.get(result, 'data.data', null);
}


async function getExamPaperClassComparison(userId, examId, paperId, classId) {
    if (_is_test()) {
        userId = temp_teacher_id;
    }
    let url = URL.format({
        protocol: SERVER.protocol,
        hostname: SERVER.hostname,
        pathname: '/v3/classes/exams/papers/comparison',
        port: SERVER.port,
        search: qs.stringify({
            teacherId: userId,
            examId: examId,
            paperId: paperId,
            classId: classId,
            scoreType: 0,
            appKey: SERVER.appKey,
        })
    });
    let result = await axios.get(url, options);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`教师端获取失败: url: ${url}`);
        return null;
    }
    return _.get(result, 'data.data', null);
}

async function getExamPaperClasses(userId, examId, paperId) {
    if (_is_test()) {
        userId = temp_teacher_id;
    }
    let url = URL.format({
        protocol: SERVER.protocol,
        hostname: SERVER.hostname,
        pathname: '/v3/classes/exam/paper/classes',
        port: SERVER.port,
        search: qs.stringify({
            teacherId: userId,
            examId: examId,
            paperId: paperId,
            scoreType: 0,
            appKey: SERVER.appKey,
        })
    });
    let result = await axios.get(url, options);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`教师端获取失败: url: ${url}`);
        return null;
    }
    const list = _.get(result, 'data.data', null);
    if (_.size(list)) {
        _class_sort(list);
    }
    return list;
}

async function getExamPaperStudentQuestionAnswer(userId, examId, paperId, classId, studentId, questionId) {
    if (_is_test()) {
        userId = temp_teacher_id;
    }
    let url = URL.format({
        protocol: SERVER.protocol,
        hostname: SERVER.hostname,
        pathname: '/v3/students/exams/papers/questions/brief',
        port: SERVER.port,
        search: qs.stringify({
            teacherId: userId,
            examId: examId,
            paperId: paperId,
            classId: classId,
            studentId: studentId,
            questionId: questionId,
            appKey: SERVER.appKey,
        })
    });
    let result = await axios.get(url, options);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`教师端获取失败: url: ${url}`);
        return null;
    }
    return _.get(result, 'data.data', null);
}

/**
 * 获取答题图片
 * @param userId
 * @param examId
 * @param paperId
 * @param questionId
 * @param classIds
 * @returns {Promise<null|GetFieldType<AxiosResponse<any>, string>>}
 */
async function getExamPaperQuestionAnswerPictures(userId, examId, paperId, questionId, classIds) {
    if (_is_test()) {
        userId = temp_teacher_id;
    }
    let url = URL.format({
        protocol: SERVER.protocol,
        hostname: SERVER.hostname,
        pathname: '/v3/classes/exam/paper/question/answer/pictures',
        port: SERVER.port,
        search: qs.stringify({
            teacherId: userId,
            examId: examId,
            paperId: paperId,
            questionId: questionId,
            classIds: classIds,
            appKey: SERVER.appKey,
        })
    });
    let result = await axios.get(url, options);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`教师端获取失败: url: ${url}`);
        return null;
    }
    return _.get(result, 'data.data', null);
}

/**
 * 获取答题图片
 * @param userId
 * @param examId
 * @param paperId
 * @param questionId
 * @param classIds
 * @returns {Promise<null|GetFieldType<AxiosResponse<any>, string>>}
 */
async function getExamPaperQuestionAnswerPictureUrl(examId, paperId, pictures) {
    let url = URL.format({
        protocol: SERVER.protocol,
        hostname: SERVER.hostname,
        pathname: '/v3/image/answer/picture/url',
        port: SERVER.port,
        search: qs.stringify({
            appKey: SERVER.appKey,
        })
    });
    const data = {
        examId,
        paperId,
        pictures
    }
    let result = await axios.post(url, data, options);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`教师端获取失败: url: ${url}`);
        return null;
    }
    return _.get(result, 'data.data', null);
}

