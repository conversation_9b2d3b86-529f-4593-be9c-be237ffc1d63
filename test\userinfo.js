const config = require('config');
const cookies = require('./config.js').config;
const expect = require('chai').expect;
const superagent = require('superagent');
const url = require('url');
const TIKUSERVER = config.get('TIKU_SERVER');
const port = process.env.NODE_PORT || 80;
const MongoClient = require('mongodb').MongoClient;
const host = url.format({
    protocol: TIKUSERVER.protocol,
    hostname: TIKUSERVER.hostname,
    port: port,
});

var dbUrl = config.get('MONGODBS.tiku');

describe('修改用户配置信息', function() {

	var url = [host, '/user_api/v1/usertraces'].join('');
	it('正例测试', function(done) {
		MongoClient.connect(dbUrl, function(err, db) {

			if (err){
				logger.error(err);
				process.exit(-1);
			}

			superagent	
			.put(url)
			.set('Content-Type', 'application/json')
			.set('Cookie', cookies.teacherCookie)
			.send(`
				{
					"period": "初中",
					"subject": "数学",
					"press_version": "人教A版",
					"grade": "必修1"
				}
			`)
			.end(function(err, res){
				expect(err).to.be.a('null');
				expect(res.status).to.be.equal(200);
				try{
					var body = JSON.parse(res.text);
					expect(body).to.have.keys('code', 'msg', 'data');
					expect(body.code).to.be.equal(0);
					expect(body.msg).to.be.equal('OK');
					expect(body.data).to.be.an('object');

					db.collection('user').findOne({_id: 7018}, {trace: 1}, function(err, doc){
						expect(doc.trace.period).to.be.equal('初中');
						expect(doc.trace.subject).to.be.equal('数学');
						expect(doc.trace.press_version).to.be.equal('人教A版');
						expect(doc.trace.grade).to.be.equal('必修1');
						done();
					});
				}catch(e){
					expect(e).to.be.a('null');
					done();
				}
			});
		});
	});
});

describe('获取用户配置信息', function(){
		
	var url = [host, '/user_api/v1/usertraces'].join('');
	it('正例测试', function(done) {
		superagent
		.get(url)
		.set('Cookie', cookies.teacherCookie)
		.end(function(err, res){
			expect(err).to.be.a('null');
			expect(res.status).to.be.equal(200);
			try{
				var body = JSON.parse(res.text);
				expect(body).to.have.keys('code', 'msg', 'data');
				expect(body.code).to.be.equal(0);
				expect(body.msg).to.be.equal('OK');
				expect(body.data).to.be.an('object');
				done();
			}catch(e){
				expect(e).to.be.a('null');
				done();
			}
		});
	});
})
