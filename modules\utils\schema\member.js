
const level_name = { 'type': 'string' };
const que_download_num = { 'type': 'object' };
const exampaper_download_num = { 'type': 'object' };
const intelligence_volume_num = { 'type': 'object' };
const specification_volume_num = { 'type': 'object' };
const volume_exampaper_num = { 'type': 'object' };
const que_details_num = { 'type': 'object' };
const exampaper_details_num = { 'type': 'object' };



// const exampaper_analysis_num = { 'type': 'object' };
// const volume_num = { 'type': 'object' };
// const recommend_album_view_num = { 'type': 'object' };
// const recommend_album_download_num = { 'type': 'object' };

const putMemberData = {
    type: 'object',
    additionalProperties: false,
    required: ['level_name', 'que_download_num', 'que_details_num', 'intelligence_volume_num',
        'specification_volume_num', 'volume_exampaper_num', 'exampaper_details_num', 'exampaper_download_num'],
    properties: {
        level_name: level_name,
        que_download_num: que_download_num,
        exampaper_download_num: exampaper_download_num,
        que_details_num: que_details_num,
        intelligence_volume_num: intelligence_volume_num,
        specification_volume_num: specification_volume_num,
        volume_exampaper_num: volume_exampaper_num,
        exampaper_details_num: exampaper_details_num
    }
};


module.exports = {
    putMemberData,
};
