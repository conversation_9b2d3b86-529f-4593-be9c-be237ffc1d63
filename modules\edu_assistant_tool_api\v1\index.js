
const _ = require('lodash');
const Joi = require('@hapi/joi');
const axios = require('axios');
const mongodber = require('../../utils/mongodber');
const db = mongodber.use('tiku');
const moment = require('moment');
const ResponseWrapper = require('../../middlewares/response_wrapper');
const logger = require('../../utils/logger');
const enums = require('../../../bin/enum');
const client = require('../../client/index');
const user_right_service = require('../../user_api/v2/user_right_service');
const user_download_service = require('../../user_api/v2/user_download_service');


module.exports = {
    getCategory,
    getList,
    getDetail,
    getByIds,
    addDownloadTimes,
    download,
}

async function getCategory(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const result = await client.kb.request('kb_api/v2/education_assistant_tools/category');
        return responseWrapper.succ(result);
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const JOI_GET_LIST = Joi.object({
    category_id: Joi.number().optional(),
    offset: Joi.number().required(),
    limit: Joi.number().required(),
}).unknown(true);


async function getList(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const params = await JOI_GET_LIST.validate(req.query);
        const kb_res = await client.kb.request('kb_api/v2/education_assistant_tools/list', {
            params: params
        });
        const result = {
            total: _.get(kb_res, 'total_num', 0),
            list: []
        };
        if (result.total) {
            for (const d of kb_res.records) {
                delete d.user_id;
                delete d.user_name;
                delete d.host;
                delete d.url;
                result.list.push(d);
            }
        }
        return responseWrapper.succ(result);
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const FILE_ACTION = {
    view_times: 'view_times',
    download_times: 'download_times'
}

const JOI_GET_DETAIL = Joi.object({
    id: Joi.number().required(),
}).unknown(true);

async function getDetail(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const params = await JOI_GET_DETAIL.validate(req.params);
        const data = await getById(params.id);
        if (_.isEmpty(data)) {
            return responseWrapper.error('NULL_ERROR', '数据不存在');
        }
        const result = {
            id: data.id,
            name: data.name,
            host: data.host,
            url: data.url,
            download_times: data.download_times,
            view_times: data.view_times,
            category_id: data.category_id,
            category_name: data.category_name,
            category_path_name: data.category_path_name,
            suffix: data.suffix,
            size: data.size,
            ctime: data.ctime,
            utime: data.utime
        }
        // 增加浏览次数
        await _add_file_times(params.id, FILE_ACTION.view_times);
        return responseWrapper.succ(result);
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

async function getById(id) {
    const data = await client.kb.request('kb_api/v2/education_assistant_tools/:id', {
        pathParams: {
            id: id
        }
    });
    return data;
}


async function _add_file_times(id, action) {
    // 增加浏览次数
    await client.kb.request('kb_api/v2/education_assistant_tools/times', {
        method: 'put',
        data: {
            id: id,
            action: action
        }
    });
}



async function getByIds(ids) {
    // 增加浏览次数
    return await client.kb.request('kb_api/v2/education_assistant_tools/:ids/list', {
        ids: ids.join(',')
    });
}

const JOI_POST_DOWNLOAD_TIMES = Joi.object({
    id: Joi.number().required(),
}).unknown(true);

async function addDownloadTimes(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const params = await JOI_POST_DOWNLOAD_TIMES.validate(req.params);
        // 增加下载次数
        await _add_file_times(params.id, FILE_ACTION.download_times);
        return responseWrapper.succ({id: params.id});
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

async function download(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const params = await JOI_POST_DOWNLOAD_TIMES.validate(req.params);
        // 获取详细
        const data = await getById(params.id);
        if (_.isEmpty(data)) {
            return responseWrapper.error('HANDLE_ERROR', '数据不存在');
        }
        // 校验下载功能
        const fun_params = {
            type: enums.NewRightType.resource_download_fun,
            // period: data.period,
            // subject: data.subject,
            target: {
                id: data.id,
                type: enums.ResourceType.EDU_ASSISTANT_TOOL
            }
        };
        const fun_status = await user_right_service.get_fun_right_status(req.user.id, fun_params);
        if (!fun_status) {
            return responseWrapper.error('NEED_VIP_ERROR', '开通会员后下载');
        }
        // 下载数量上限
        // const limit_right = await user_right_service.get_right_limit(req.user.id, {
        //     type: enums.NewRightType.edu_tool_download_num,
        //     // period: data.period,
        //     // subject: data.subject
        // });
        const limit_day_count = 5;
        const download_count = await db.collection('user_download').find({
            user_id: req.user.id,
            resource_type: enums.ResourceType.EDU_ASSISTANT_TOOL,
            ctime: {$gte: moment().startOf('day').toDate()}
        }).count();
        if (download_count >= limit_day_count) {
            return responseWrapper.error('TIMES_NOT_ENOUGH_ERROR', `每日最多可下载${limit_day_count}份`);
        }
        // 权益消耗
        const right_params =_.assign({}, fun_params, {type: enums.NewRightType.edu_tool_download_num});
        const right = await user_right_service.get_use_right(req.user.id, right_params);
        if (!_.isEmpty(right) && (right.group === enums.NewVipType.school || right.group === enums.NewVipType.school_plus)) {
            const school_download_status = await user_download_service.check_school_download_limit(user.schoolId);
            if (!school_download_status) {
                return responseWrapper.error('TIMES_NOT_ENOUGH_ERROR', `学校每日最多可下载1000份资源`);
            }
        }
        const status = await user_right_service.use_right(req.user.id, right_params, right);
        if (!status) {
            return responseWrapper.error('OVER_LIMIT_ERROR', '下载次数不足');
        }
        // 下载记录
        const download = {
            user_id: req.user.id,
            sch_id: req.user.schoolId,
            resource_type: enums.ResourceType.EDU_ASSISTANT_TOOL,
            resource_id: params.id,
            resource_name: data.name,
            resource_info: {
                size: data.size,
                suffix: data.suffix,
            },
            download_times: 1,
            ctime: new Date(),
            utime: new Date(),

        };
        await db.collection('user_download').insertOne(download);
        // 增加下载次数
        await _add_file_times(params.id, FILE_ACTION.download_times);
        // 读取
        const file_url = `${data.host}${data.url}`;
        const response = await axios.get(file_url, { responseType: 'stream' });
        if (!response || response.status !== 200 || !response.data) {
            return responseWrapper.error('HANDLE_ERROR', '下载文件出错');
        }
        response.data.pipe(res);
        res.setHeader('Content-disposition', `attachment; filename=${encodeURI(`${data.name}.${data.suffix}`)}`);
        res.setHeader('Content-type', 'application/octet-stream');
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

