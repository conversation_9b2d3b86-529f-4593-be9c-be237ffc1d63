const config = require('config');
const express = require('express');
const router = express.Router();
const exam_area = require('../../modules/assemble_api/v1/exam_area');
const downloader = require('../../modules/kb_api/v2/downloader');

const KBSERVCFG = config.get('KB_API_SERVER');

// kb api key middleware
router.use(function (req, res, next) {
    req.apiKey = KBSERVCFG.appKey;
    next();

});
router.get('/exam_area/info', exam_area.get_exam_area);
router.get('/question/:question_id/reco', downloader.getQuestionReco);

module.exports = router;
