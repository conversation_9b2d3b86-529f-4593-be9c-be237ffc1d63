let mongodber = require('../../utils/mongodber');
let ResponseWrapper = require('../../middlewares/response_wrapper');
let logger = require('../../utils/logger');
let db = mongodber.use('tiku');
let _ = require('underscore');


const getGradeProfile = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let data = await db.collection('profile').findOne({_id: 'grade'});
        if (!data) {
            throw new Error('没有数据');
        }
        if (req.query.period) {
            data.periods = _.filter(data.periods, (x) => {
                return x.name === req.query.period;
            });
        }
        return responseWrapper.succ(data.periods);
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

module.exports = {
    getGradeProfile,
};