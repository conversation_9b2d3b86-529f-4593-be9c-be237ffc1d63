const _ = require('underscore');

const deleteQuestionBlocks = (volumes) => {
    try {
        _.each(volumes,(volume) => {_.each(volume.blocks, block => {_.each(block.questions, question => {
            question.blocks = {
                stems: question.blocks.stems,
                answers: question.blocks.answers,
                knowledges: question.blocks.knowledges,
                core_knowledges: question.blocks.core_knowledges,
            }
        })})})
    } catch (err) {
        
    }
}

module.exports = {
    deleteQuestionBlocks,
};
