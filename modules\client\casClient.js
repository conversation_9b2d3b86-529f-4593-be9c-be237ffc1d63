
const URL = require('url');
const config = require('config');
const axios = require('axios');
const server = config.get('casServer');
const qs = require('querystring');
const utils = require('../utils/utils')
const logger = require('../utils/logger');

module.exports = {
    validateUser,
}

async function validateUser(user, password) {
    // const url = URL.format({
    //     protocol: server.protocol,
    //     hostname: server.hostname,
    //     port: server.port,
    //     pathname: `/validateUser`
    // });
    try {
        const url = `${server}/validateUser`;
        const params = {
            loginName: user,
            password: password
        }
        const data = await axios.post(url, params).then(utils.casHandler);
        return data;
    } catch (e) {
        logger.error(`validateUser error`);
        logger.error(e);
        return null;
    }

}
