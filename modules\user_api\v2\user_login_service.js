/**
 * 用户登录V2
 */
const config = require('config');
const moment = require('moment');
const Joi = require('@hapi/joi');
const _ = require('lodash');
const jsonwebtoken = require('jsonwebtoken');

const ResponseWrapper = require('../../middlewares/response_wrapper');
const mongodber = require('../../utils/mongodber');
const db = mongodber.use('tiku');
const redisCache = require('../../utils/redis_cache');
const rediser = require('../../utils/rediser');
const logger = require('../../utils/logger');
const utils = require('../../utils/utils');
const aes = require('../../utils/aes');
const jwt = require('../../utils/jwt');

const enums = require('../../../bin/enum');
const client = require('../../client');

const user_vip_service = require('../v1/user_vip_service');
const user_log_service = require('../v1/user_log_service');
const user_utils = require('../user_utils');
const user_right_service = require('../v2/user_right_service');

const TIKUSERVER = config.get('TIKU_SERVER');

module.exports = {
    loginCheck,
    loginBySchool,
    login,
    loginByToken,
    homeworkLogin,
    loginByHfsAppTeacher,
}
const JOI_POST_LOGIN_CHECK = Joi.object({
    phone: Joi.string().required(),
    password: Joi.string().required()
});

async function loginCheck(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let { phone, password } = await Joi.validate(req.body, JOI_POST_LOGIN_CHECK);
        const ssoData = await client.yj.getUserListByCheck(phone, password);
        if (_.isEmpty(ssoData)) {
            // 临时方案处理 再阅卷禁用的老师 可以登录c端题库
            let user_id = await _temporaryLogin(phone, password)
            if (!user_id) return responseWrapper.error('PARAMETERS_ERROR', '帐号或密码错误');
            return responseWrapper.succ([{
                loginName: phone,
                phone: phone,
                userId: user_id,
            }]);
        }
     
        return responseWrapper.succ(ssoData);
    } catch (e) {
        logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

async function _temporaryLogin(phone, password) {
    try {
        const params = {
            user: phone,
            passwd: Buffer.from(decodeURI(password), 'base64').toString('utf8'),
        };

        // 获取用户列表
        let userList = await db.collection('user').find({phone: phone}).toArray();
        if (_.isEmpty(userList)) {
            // 如果没有找到用户，直接尝试CAS认证
            const casData = await client.cas.validateUser(params.user, params.passwd);
            return casData && casData.userId ? Number(casData.userId) : null;
        }

        let userIds = userList.map(e => e._id);

        // 查询用户权限
        let rights = await db.collection('user_right').find({
            user_id: {$in: userIds},
            group: {$in: [enums.NewVipType.personal, enums.NewVipType.personal_plus, enums.NewVipType.personal_pro]},
            stime: {$lte: new Date()},
            etime: {$gte: new Date()}
        }).toArray();

        let casData = null;

        // 如果有权限记录，尝试使用对应用户的学校ID进行认证
        if (!_.isEmpty(rights)) {
            let user = userList.find(e => e._id === rights[0].user_id);
            if (user && user.sch_id) {
                casData = await client.cas.validateUser(params.user, params.passwd, user.sch_id);
            }
        }

        // 如果上面的认证失败，尝试不带学校ID的认证
        if (!casData) {
            casData = await client.cas.validateUser(params.user, params.passwd);
        }

        let tmp_user_id = casData && casData.userId;
        return tmp_user_id ? Number(tmp_user_id) : null;

    } catch (error) {
        logger.error('_temporaryLogin error:', error);
        return null;
    }
}

const JOI_POST_LOGIN_BY_SCHOOL = Joi.object({
    userId: Joi.number().required(),
    phone: Joi.string().required(),
    password: Joi.string().required(),
});

async function loginBySchool(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let { phone, password, userId } = await Joi.validate(req.body, JOI_POST_LOGIN_BY_SCHOOL);
        const ssoData = await client.yj.getUserListByCheck(phone, password);
        if (_.isEmpty(ssoData)) {
            let user_id = await _temporaryLogin(phone, password)
            if (!user_id || userId !== user_id) return responseWrapper.error('PARAMETERS_ERROR', '帐号或密码错误');
        } else {
            let tmp_user = ssoData && ssoData.find(e => e.userId === userId);
            if (!tmp_user) return responseWrapper.error('PARAMETERS_ERROR', '帐号或密码错误');
        }
        let user = await user_utils.getUserById(userId);
        if (_.isEmpty(user)) {
            user = await _add_user_by_kb_apps(userId, enums.UserSource.TIKU, phone);
        } else {
            // 校验用户信息更改
            await _check_user_info_update(user, enums.UserSource.TIKU);
        }
        await _set_cookie(res, user);
        return responseWrapper.succ('');
    } catch (e) {
        logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const JOI_POST_LOGIN = Joi.object({
    app: Joi.string().required(),
    cert_app: Joi.string().required(),
    user: Joi.string().required(),
    passwd: Joi.string().required()
});

async function login(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let cas_user_phone = '';
        let params = await Joi.validate(req.body, JOI_POST_LOGIN);
        if (params.cert_app !== 'HFS_TEACHER') return responseWrapper.error('PARAMETERS_ERROR', '帐号或密码错误');
        const ssoData = await client.kbApp.getLoginInfo(params);
        let tmp_user_id = ssoData && (ssoData.id || ssoData.userId);
        if (!tmp_user_id) {
            const casData = await client.cas.validateUser(params.user, params.passwd);
            tmp_user_id = casData && casData.userId;
            cas_user_phone = casData && casData.cellphone;
        }
        if (!tmp_user_id) return responseWrapper.error('PARAMETERS_ERROR', '帐号或密码错误');
        let user_id = Number(tmp_user_id);
        let user = await user_utils.getUserById(user_id);
        if (_.isEmpty(user)) {
            user = await _add_user_by_kb_apps(user_id, enums.UserSource.TIKU, cas_user_phone);
        } else {
            // 校验用户信息更改
            await _check_user_info_update(user, enums.UserSource.TIKU);
        }
        await _set_cookie(res, user);
        return responseWrapper.succ('');
    } catch (e) {
        logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const token_login = {
    [enums.UserSource.YJ]: _login_yj_token,
    [enums.UserSource.XD]: _login_xd_token,
    [enums.UserSource.HFS]: _login_hfs_token,
    [enums.UserSource.HFS_APP_JS]: _login_hfs_token,
    [enums.UserSource.UNIFY]: _login_by_unify_token,
}

const JOI_LOGIN_BY_TOKEN = Joi.object({
    token: Joi.string().required(),
    from: Joi.string().valid(...Object.values(enums.UserSource)).required(),
    redirect: Joi.string().optional()
});

async function loginByToken(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const { token, from, redirect } = JOI_LOGIN_BY_TOKEN.validate(req.query);
        const fun = token_login[from];
        const user = await fun(token);
        if (_.isEmpty(user)) return responseWrapper.error('PARAMETERS_ERROR', '凭据非法或失效');
        // 写入cookie
        await _set_cookie(res, user);
        // 返回结果或者重定向到页面
        if (redirect) {
            res.setHeader('content-type', 'text/html');
            res.redirect(redirect);
        } else {
            return responseWrapper.succ('');
        }
    } catch (e) {
        logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

/**
 * unify登录
 * @param token
 * @return {Promise<null|*>}
 * @private
 */
async function _login_by_unify_token(token) {
    const data = await decodeUnifyId(token);
    if (_.isEmpty(data)) return null;
    const user_id = Number(data.userId);
    let user = await user_utils.getUserById(user_id);
    if (_.isEmpty(user)) {
        user = user_utils.initUser(user_id);
        user.source = enums.UserSource.UNIFY;
        await _add_user(user);
    } else {
        await _check_user_info_update(user, '');
    }
    return user;
}

/**
 *
 * @param unify_sid
 * @returns {Promise<null|*>}
 */
async function decodeUnifyId(unify_sid) {
    if (!unify_sid) return null;
    try {
        // {
        //     loginWay: 'account',
        //     schoolId: 55553,
        //     userId: '****************',
        //     switch: false,
        //     unifyAuth: '11C13D2D6E44D77D53A027DBA8028C8D9C90F6E2',
        //     key: '****************:*************',
        //     iat: **********,
        //     exp: **********,
        //     jti: '****************'
        // }
        // app_id: 11C13D2D6E44D77D53A027DBA8028C8D9C90F6E2
        // public_key: 7A1E0DE58659352FC04DF02FAF38BA00E02E4D2D
        return jsonwebtoken.verify(unify_sid, Buffer.from(Buffer.from('7A1E0DE58659352FC04DF02FAF38BA00E02E4D2D', 'base64')));
    } catch (e) { // 过期或者错误数据
        logger.error(e);
        return null;
    }
}

async function homeworkLogin(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const qeury_user_id = req.query.userId;
        if (!qeury_user_id) return responseWrapper.error('PARAMETERS_ERROR', '用户ID不能为空!');
        const teacher = await client.hfsTeacherV3.getTeacherInfoById(qeury_user_id);
        if (_.isEmpty(teacher)) return responseWrapper.error('PARAMETERS_ERROR', '用户信息不存在!');
        const user = await _login_by_hfs_teacher(enums.UserSource.ZY, teacher);
        // 登录日志
        await user_log_service.login(user);
        const cookie_user = await user_utils.getCookieUser(user);
        cookie_user.exp = moment().endOf('day').valueOf();
        let value = aes.encript(jwt.encode(cookie_user));
        const options = _.assign({}, TIKUSERVER.sessionIdCookie.options);
        options.maxAge = moment().endOf('day').diff(new Date(), 'millisecond');
        res.cookie(TIKUSERVER.sessionIdCookie.name, value, options);
        res.cookie(TIKUSERVER.userInfo.name, JSON.stringify(cookie_user), options);
        const resObj = {
            name: TIKUSERVER.sessionIdCookie.name,
            token: aes.encript(jwt.encode(user)),
            expired_time: moment().endOf('day').valueOf()
        }
        return responseWrapper.succ(resObj);
    } catch (e) {
        logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

async function loginByHfsAppTeacher(token) {
    const data = await client.hfsTeacherV3.getTeacherInfoByToken(token);
    if (_.isEmpty(data)) return null;
    const user_id = Number(data.id);
    let user = await user_utils.getUserById(user_id);
    if (_.isEmpty(user)) {
        user = user_utils.initUser(user_id, enums.UserType.TEACHER);
        user.source = enums.UserSource.HFS_APP_JS;
        await _add_user(user);
    } else { //
        // await _check_user_info_update(user, from);
    }
    return user;
}

async function _login_yj_token(token) {
    const data = await client.kbApp.getUserInfo(token);
    if (_.isEmpty(data)) return null;
    const user_id = Number(data.id);
    let user = await user_utils.getUserById(user_id);
    if (_.isEmpty(user)) {
        user = await _add_user_by_kb_apps(user_id, enums.UserSource.YJ);
    } else {
        await _check_user_info_update(user, enums.UserSource.YJ);
    }
    return user;
}

async function _login_xd_token(token) {
    const data = await client.kbApp.getUserInfo(token);
    if (_.isEmpty(data)) return null;
    const user_id = Number(data.id);
    let user = await user_utils.getUserById(user_id);
    if (_.isEmpty(user)) {
        user = await _add_user_by_kb_apps(user_id, enums.UserSource.XD);
    } else {
        await _check_user_info_update(user, enums.UserSource.XD);
    }
    return user;
}

async function _login_hfs_token(token, from = enums.UserSource.HFS) {
    const data = await client.hfsTeacherV3.getTeacherInfoByToken(token);
    if (_.isEmpty(data)) return null;
    const user = await _login_by_hfs_teacher(from, data);
    return user;
}

async function _login_by_hfs_teacher(from, data) {
    if (_.isEmpty(data)) return null;
    const user_id = Number(data.id);
    let user = await user_utils.getUserById(user_id);
    if (_.isEmpty(user)) {
        const user = user_utils.initUser(user_id, enums.UserType.TEACHER);
        await _add_user(user);
    } else {
        await _check_user_info_update(user, from);
    }
    return user;
}

async function _add_user_by_kb_apps(user_id, from, cas_user_phone) {
    const user = user_utils.initUser(user_id);
    user.source = from;
    if (cas_user_phone) {
        user.phone = cas_user_phone
    }
    await _add_user(user);
    return user;
}

async function _add_user(user) {
    // 添加用户信息如姓名
    await _add_teacher_info(user);
    // 添加学校信息
    await _add_school_info(user);
    // 学校会员
    // const school = await _get_school_by_id(user.sch_id);
    // await user_vip_service.sync_school_vip(school, user);
    // 初始化权益
    await user_right_service.init_rights(user._id);
    user.init_right = true;
    // 插入数据库
    await user_utils.insertUser(user);
    // 学校会员
    if (user.sch_id) {
        const school = await _get_school_by_id(user.sch_id);
        await user_right_service.sync_school_right(user, school);
    }
    // 记录注册日志
    await user_log_service.register(user);
    // 返回结果
    return user;
}

async function _check_user_info_update(user, from) {
    const update = {
        utime: new Date(),
        last_time: new Date(),
    };
    if (!user.ctime) update['ctime'] = new Date(); // 信息补全
    // 教师信息
    const info = await user_utils.getTeacherInfoById(user._id);
    if (!_.isEmpty(info)) {
        if (info.name && (!user.name || user.name !== info.name)) {
            update.name = info.name;
        }
        if (info.phone && (!user.phone || user.phone !== info.phone)) {
            update.phone = info.phone;
        }
        update.trace = { period: info.period, subject: info.period, grade: info.grade };
        update.curr = user.curr || {};
        if (from !== enums.UserSource.TIKU) { // 用户名密码登录不需要
            if (update.curr.period !== info.period) _.set(update, 'curr.period', info.period);
            if (update.curr.subject !== info.subject) _.set(update, 'curr.subject', info.subject);
            if (update.curr.grade !== info.grade) _.set(update, 'curr.grade', info.grade);
        }
        const bossSchoolInfo = await redisCache.getBossSchoolInfoCache(info.schoolId);
        const new_sch_id = bossSchoolInfo && bossSchoolInfo.schoolId || 0;
        if (new_sch_id && user.sch_id !== new_sch_id) {
            update.sch_id = new_sch_id;
            update.sch_name = bossSchoolInfo.name;
            const location = (bossSchoolInfo.location || '').split('/');
            update.province = location && location[0] || '';
            update.city = location && location[1] || '';
            update.district = location && location[2] || '';
            user.sch_id = new_sch_id;
        }
    }
    // 初始化权益
    if (!user.init_right) {
        await user_right_service.init_rights(user._id);
        update.init_right = true;
    }
    // 学校会员
    if (user.sch_id) {
        const school = await _get_school_by_id(user.sch_id);
        await user_right_service.sync_school_right(user, school);
    }
    // 刷新权益
    if (!moment().isSame(user.last_time, 'day')) {
        await user_right_service.reset_rights(user._id);
    }

    // 保存信息
    await user_utils.updateUser(user._id, update);
    _.assign(user, update);
    return user;
}

async function _add_school_info(user) {
    if (!user.sch_id) return;
    const bossSchoolInfo = await redisCache.getBossSchoolInfoCache(user.sch_id);
    user.sch_id = bossSchoolInfo && bossSchoolInfo.schoolId;
    user.sch_name = bossSchoolInfo && bossSchoolInfo.name;
    const location = (bossSchoolInfo && bossSchoolInfo.location || '').split('/');
    user.province = location && location[0] || '';
    user.city = location && location[1] || '';
    user.district = location && location[2] || '';
}

async function _add_teacher_info(user) {
    // const teacher = await client.hfsTeacherV3.getTeacherInfoById(user._id);
    const info = await user_utils.getTeacherInfoById(user._id);
    if (_.isEmpty(info) || !info.userId) return;
    user._id = info.userId;
    user.name = info.name;
    user.sch_id = info.schoolId;
    user.sch_name = info.schoolName;
    user.phone = _.get(info, 'phone', '');
    user.login_name = _.get(info, 'phone', '');
    user.grade = _.get(info, 'grade', '');
    user.curr = { period: info.period, subject: info.subject, grade: info.grade };
    user.trace = { period: info.period, subject: info.subject, grade: info.grade };
}

async function _get_school_by_id(school_id) {
    return await db.collection('school_info').findOne({_id: school_id});
}

async function _set_cookie(res, user) {
    //
    // 登录日志
    await user_log_service.login(user);

    const cookie_user = await user_utils.getCookieUser(user);
    cookie_user.exp = moment().endOf('day').valueOf();
    let value = aes.encript(jwt.encode(cookie_user));
    const options = _.assign({}, TIKUSERVER.sessionIdCookie.options);
    options.maxAge = moment().endOf('day').diff(new Date(), 'millisecond');
    res.cookie(TIKUSERVER.sessionIdCookie.name, value, options);
    res.cookie(TIKUSERVER.userInfo.name, JSON.stringify(cookie_user), options);
}
