
const _ = require('lodash');
const ObjectID = require("mongodb").ObjectID;
const mongodber = require('../../utils/mongodber');
const db = mongodber.use('tiku');
const db_open = mongodber.use('tiku_open');
const ResponseWrapper = require('../../middlewares/response_wrapper');
const logger = require('../../utils/logger');
const enums = require('../../../bin/enum');
const Joi = require('@hapi/joi');
const client = require('../../client');
const constants = require('../../utils/constants');
const template = require('./../../assemble_api/v1/template');
const TYPES = template.TYPES;
const DIGIT_MAP_CHINESE = template.DIGIT_MAP_CHINESE;
const subject_utils = require('../../utils/subject_utils');
const config = require('config');
const Fontmin = require('fontmin');
const moment = require('moment');
const util = require('util');
const paper_utils = require('../../utils/paper_utils');
const question_utils = require('../../utils/question_utils');
const utils = require('../../utils/utils');
const _get_exampaper_by_id = require('../../assemble_api/v1/exampaper').get_exampaper_by_id;
const _get_exampaper_by_id_async = require('../../assemble_api/v1/exampaper').get_exampaper_by_id_async;
//
const collection_exam = db.collection(constants.schema.exam_teacher_paper);
// 用户组卷表
const collection_exampaper = db_open.collection(enums.OpenSchema.user_paper);

module.exports ={
    getList,
    getExamPaperClasses,
    getExamPaperQuestionDetail,
    getExamPaperQuestionSame,
    putExamPaperQuestionSame,
    questionSearch,
    getExamPaperInfoByCategory,
    putExamPaperQuestionByCategory,
    getExamPaperQuestionIds,
    getExamPaperTeacherHandout,
    getExamPaperTeacherComment,
    getExamPaperDownloadInfo,
    getExamPaperStudentQuestionAnswer,
    getExamPaperQuestionAnswerImage,
    putExamPaperQuestionAnswerTag,
    text2ttf,
    getResourceDownloadInfo,
    getExamPaperStatus,
    getExamPaperComparison,
}

/**
 * 获取教师考试列表
 * @param req
 * @param res
 * @returns {Promise<void>}
 */
async function getList(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const user_id = req.user.id;
        const hfsExamList = await client.hfsTeacherV3.getTeacherExamList(user_id);
        if (!_.size(hfsExamList)) return responseWrapper.succ([]);
        return responseWrapper.succ(hfsExamList);
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const JOI_GET_EXAM_PAPER_CLASSES = Joi.object({
    exam_id: Joi.string().required(),
    paper_id: Joi.string().required(),
}).unknown(true);

async function getExamPaperClasses(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const { exam_id, paper_id } = await JOI_GET_EXAM_PAPER_CLASSES.validate(req.params);
        const user_id = req.user.id;
        let list = await client.hfsTeacherV3.getExamPaperClasses(user_id, exam_id, paper_id);
        return responseWrapper.succ(list);
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const JOI_GET_EXAM_PAPER_QUESTION_DETAIL = Joi.object({
    exam_id: Joi.string().required(),
    paper_id: Joi.string().required(),
    zujuanId: Joi.string().optional().allow(''),
}).unknown(true);

async function getExamPaperQuestionDetail(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const { exam_id, paper_id, zujuanId } = await JOI_GET_EXAM_PAPER_QUESTION_DETAIL.validate(_.assign(req.params, req.query));
        const rankExam = await client.rank.getExamById(exam_id);
        if (_.isEmpty(rankExam)) return responseWrapper.error('HANDLE_ERROR', '考试不存在或者未同步!');
        if (zujuanId && rankExam.type !== 12) return responseWrapper.error('HANDLE_ERROR', '非考试作业');
        const rankPaper = rankExam['[papers]'].find(e => e.id === paper_id);
        const { period, grade, subject } = getGradeInfoByRankExamPaper(rankPaper);
        const user_id = req.user.id;
        const result = {
            paper: {
                exam_id: exam_id,
                exam_name: `${rankExam.name} （${rankPaper.name})`,
                original: enums.BooleanNumber.NO, // 是否上传原卷0：否，1：是
                published: enums.BooleanNumber.NO, // 是否发布成绩0：否，1：是
                image_url: []
            },
            questions: [],
            class_info: []
        }
        // 获取元培信息
        const yp_paper = await client.yuanpei.getPaperQuestions(paper_id);
        const paper_status = await getPaperStatus(exam_id, paper_id, yp_paper);
        result.paper.original = paper_status.original;
        result.paper.published = paper_status.published;
        if (zujuanId) {
            result.paper.original = enums.BooleanNumber.YES;
        }
        if (!result.paper.original && !result.paper.published) return responseWrapper.succ(result);

        if (!_.isEmpty(yp_paper)) {
            result.paper.image_url = _.get(yp_paper, 'paper.image_url', []);
        }
        // if (result.paper.original) {
        //     result.paper.exam_name = yp_paper.paper.name;
        //     result.paper.image_url = _.get(yp_paper, 'paper.image_url', []);
        // }
        let exam = await getUserExam(req.user.id, exam_id, paper_id);
        if (_.isEmpty(exam)) {
            exam = await initExam(req.user.id, exam_id, paper_id, yp_paper);
        } else {
            const ds = {};
            if (result.paper.original && !exam.period) {
                ds.period = period;
                ds.grade = grade;
                ds.subject = subject;
                exam = _.assign(exam, ds);
            }
            if (zujuanId && !exam.user_paper_id) {
                ds.user_paper_id = zujuanId;
                exam.user_paper_id = zujuanId;
            }
            if (!_.isEmpty(ds)) {
                ds.utime = new Date();
                await collection_exam.updateOne({_id: exam._id}, {$set: ds});
            }
        }
        const hfs_question_info = await client.hfsTeacherV3.getExamPaperQuestionDetail(user_id, exam_id, paper_id);

        if (hfs_question_info) {
            if (exam.user_paper_id) { // 用户组卷挂载原题信息
                const tiku_paper = await _get_exampaper_by_id_async(exam.user_paper_id);
                const kb_questions = getTikuPaperQuestions(tiku_paper);
                if (_.size(kb_questions)) {
                    hfs_question_info.questions.forEach((ques, i) => {
                        const kb_question = kb_questions.find(e => e.id.toString() === ques.zujuanQuestionId);
                        if (!_.isEmpty(kb_question)) {
                            ques.type = kb_question.type;
                            ques.origin_question = kb_question;
                        }
                    })
                }
            } else {
                for (const ques of hfs_question_info.questions || []) {
                    const yp_question = findYpQuestion(ques, yp_paper);
                    if (yp_question) ques.type = _get_hfs_question_type(yp_paper.paper.subject, ques, yp_question);
                }
                try {
                    const comparison_params = [];
                    for (const cls of hfs_question_info.class_info || []) {
                        comparison_params.push({
                            user_id: req.user.id,
                            exam_id,
                            paper_id,
                            class_id: cls.id
                        });
                    }
                    const comparison_res = await Promise.all(comparison_params.map(e => client.hfsTeacherV3.getExamPaperClassComparison(e.user_id, e.exam_id, e.paper_id, e.class_id)));
                    for (const i in hfs_question_info.class_info || []) {
                        const cls = hfs_question_info.class_info[i];
                        const comparison = comparison_res[i];
                        sortByRank(comparison, cls);
                    }
                } catch (e) {
                    logger.error(e);
                }
            }
        }
        _.assign(result, hfs_question_info);
        return responseWrapper.succ(result);
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

function getGradeInfoByRankExamPaper(paper) {
    const gradeInfo = enums.PeriodGradeMapping.find(e => e.yj_grade === paper.grade || e.grade === paper.grade);
    return {
        period: gradeInfo.period,
        grade: gradeInfo.grade,
        subject: paper.subject
    };
}


function getTikuPaperQuestions(tiku_paper) {
    const questions = [];
    if (_.isEmpty(tiku_paper)) return questions;
    for (const v of tiku_paper.volumes || []) {
        for (const b of v.blocks || []) {
            questions.push(...b.questions);
        }
    }
    return questions;
}


async function initExamQuestionsAndPaper(user, exam, yp_paper, paper_status) {
    if (!paper_status.original) return; // 未上传原卷
    await buildPaperRecomendQuestions(exam, yp_paper); // 相似题
    buildExamPapers(user, exam);
}

function sortByRank(score_report, class_info) {
    if (!_.size(score_report) || _.isEmpty(class_info)) return;
    const stu_map = {};
    for (const stu of score_report) {
        stu_map[stu.studentId] = { rank: _.get(stu, 'current.classRank') };
    }
    for (const ques of class_info.question_score) {
        if (ques.hasOwnProperty('options')) { // 客观题
            for (const option of ques.options || []) {
                option.students.forEach(e => {
                    const stu = stu_map[e.id];
                    if (!_.isEmpty(stu)) {
                        e.rank = stu.rank;
                    } else {
                        e.rank = Number.MAX_SAFE_INTEGER;
                    }
                })
                option.students = _.orderBy(option.students, ['rank'], ['asc']);
            }
        } else if (ques.hasOwnProperty('scores')) { // 主观题
            for (const score of ques.scores || []) {
                score.students.forEach(e => {
                    const stu = stu_map[e.id];
                    if (!_.isEmpty(stu)) {
                        e.rank = stu.rank;
                    } else {
                        e.rank = Number.MAX_SAFE_INTEGER;
                    }
                })
                score.students = _.orderBy(score.students, ['rank'], ['asc']);
            }
        }
    }
}


/**
 * 获取试卷状态
 * @param examId
 * @param paperId
 * @param ypPaper
 * @returns {{original: number, published: *}}
 */
async function getPaperStatus(examId, paperId, ypPaper) {
    const result =  {
        original: enums.BooleanNumber.NO, // 是否上传原卷0：否，1：是
        published: enums.BooleanNumber.YES, // 是否发布成绩0：否，1：是
    };
    if (_.isEmpty(ypPaper)) {
        ypPaper = await client.yuanpei.getPaperQuestions(paperId);
        if (!_.isEmpty(ypPaper)) {
            result.original = enums.BooleanNumber.YES;
        }
    } else {
        result.original = enums.BooleanNumber.YES;
    }
    // const subject = _.isEmpty(ypPaper) ? '' : _.get(ypPaper, 'paper.subject');
    // const published = await client.hfs.getExamPublishStatus(examId, paperId, subject);
    // if (published) {
    //     result.published = enums.BooleanNumber.YES;
    // }
    return result;
}





const JOI_GET_EXAM_PAPER_QUESTION_SAME = Joi.object({
    exam_id: Joi.string().required(),
    paper_id: Joi.string().required(),
    question_id: Joi.string().required(),
}).unknown(true);

async function getExamPaperQuestionSame(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const params = await JOI_GET_EXAM_PAPER_QUESTION_SAME.validate(req.params);
        const { exam_id, paper_id, question_id } = params;
        const user_id = req.user.id;
        const yp_paper = await client.yuanpei.getPaperQuestions(paper_id);
        const status = await getPaperStatus(exam_id, paper_id, yp_paper);
        const exam = await getUserExam(user_id, exam_id, paper_id);
        if (exam.user_paper_id) {
            status.original = enums.BooleanNumber.YES;
        }
        if (!status.original) {
            return responseWrapper.error('HANDLE_ERROR', '未上传原卷');
        }
        const hfs_questions = await client.hfsTeacherV3.getPaperQuestions(exam.user_id, exam.exam_id, exam.paper_id);
        if (!_.size(hfs_questions)) return responseWrapper.error('HANDLE_ERROR', '未获取到试题');
        let result = [];
        const hfs_question = hfs_questions.find(e => e.id === question_id);
        if (!hfs_question) return responseWrapper.succ(result);
        let questions = exam['questions'] || [];
        let question = questions.find(e => e.id === question_id || e.key === hfs_question.key);
        if (_.isEmpty(question)) {
            questions = await buildPaperRecomendQuestions(exam, yp_paper);
            question = questions.find(e => e.id === question_id || e.key === hfs_question.key);
        }
        if (!_.isEmpty(question)) {
            result = await client.kb.getQuestions(question.same);
            question_utils.addShowTags(result);
        }
        return responseWrapper.succ(result);
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const JOI_PUT_EXAM_PAPER_QUESTION_SAME = Joi.object({
    exam_id: Joi.string().required(),
    paper_id: Joi.string().required(),
    question_id: Joi.string().required(),
    ids: Joi.array().items(Joi.number()).min(0)
});

async function putExamPaperQuestionSame(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const { exam_id, paper_id, question_id, ids } = await JOI_PUT_EXAM_PAPER_QUESTION_SAME.validate(_.assign(req.params, req.body));
        const user_id = req.user.id;
        let result = [];
        const exam = await getUserExam(user_id, exam_id, paper_id);
        const hfs_questions = await client.hfsTeacherV3.getPaperQuestions(exam.user_id, exam.exam_id, exam.paper_id);
        if (!_.size(hfs_questions)) return responseWrapper.error('HANDLE_ERROR', '未查询到试题');
        const hfs_question = hfs_questions.find(e => e.id === question_id);
        if (hfs_question) return responseWrapper.error('HANDLE_ERROR', '未查询到试题');
        const questions = exam.questions || [];
        if (_.size(questions)) {
            const question = questions.find(e => e.id === question_id || e.key === hfs_question.key);
            if (!_.isEmpty(question)) {
                question.same = ids;
                await collection_exam.updateOne({_id: exam._id}, {$set: {questions}});
                result = await client.kb.getQuestions(ids);
            }
        }
        return responseWrapper.succ(result);
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }

}

function getRecomendQuestionsParam(exam, hfs_question, yp_question) {
    const params = {
        period: exam.period,
        subject: exam.subject,
        grade: exam.grade,
        ques_id: yp_question.id,
        ques_from: 'yuanpei',
        ques_type: _get_hfs_question_type(exam.subject, hfs_question, yp_question),
        recom_num: 2
    };
    return params;
}

async function buildPaperRecomendQuestions(exam, yp_paper) {
    const hfs_questions = await client.hfsTeacherV3.getPaperQuestions(exam.user_id, exam.exam_id, exam.paper_id);
    let kb_questions = [];
    if (exam.user_paper_id) {
        const tiku_paper = await _get_exampaper_by_id_async(exam.user_paper_id);
        kb_questions = getTikuPaperQuestions(tiku_paper);
    }
    const params = [];
    const quest_map = new Map();
    const exam_questions = exam.questions || [];
    for (const hfs_question of hfs_questions) {
        const question = exam_questions.find(e => e.id === hfs_question.id || e.key === hfs_question.key);
        if (!_.isEmpty(question)) continue;

        const param = {
            period: exam.period,
            subject: exam.subject,
            grade: exam.grade,
            // ques_id: yp_question.id, // kb question id
            // ques_from: 'yuanpei', // kb
            // ques_type: _get_hfs_question_type(exam.subject, hfs_question, yp_question),
            recom_num: 2
        };
        if (exam.user_paper_id) {
            const kb_question = kb_questions.find(e => e.id.toString() === hfs_question.zujuanQuestionId);
            if (_.isEmpty(kb_question) || quest_map.has(kb_question.id)) continue;
            param.ques_type = kb_question.type;
            param.ques_id = kb_question.id.toString();
            if (_.isNumber(kb_question.id) || kb_question.source === enums.QuestionSource.SYS) {
                param.ques_from = 'kb';
            } else {
                param.ques_from = 'other';
                if (kb_question.source !== enums.QuestionSource.XKW && _.size(kb_question.knowledges)) {
                    param.knowledges = [];
                    for (const arr of kb_question.knowledges) {
                        param.knowledges.push(...arr.map(e => _.pick(e, ['id', 'name'])));
                    }
                }
                // 试题文本
                param.ques_text = kb_question.description || '';
                const stems = _.get(kb_question, 'blocks.stems', []);
                if (_.size(stems)) {
                    for (const stem of stems) {
                        param.ques_text += stem.stem || '';
                        if (stem.options) {
                            for (const key of Object.keys(stem.options)) {
                                param.ques_text += `${key} ${stem.options[key]}`;
                            }
                        }
                    }
                }
            }
            // param.ques_id = kb_question.id.toString();
            // param.ques_from = 'kb';
            // param.ques_type = kb_question.type;
            quest_map.set(kb_question.id, []);
        } else {
            const yp_question = findYpQuestion(hfs_question, yp_paper);
            if (_.isEmpty(yp_question) || quest_map.has(yp_question.id)) continue;
            quest_map.set(yp_question.id, []);
            param.ques_id = yp_question.id;
            param.ques_from = 'yuanpei';
            param.ques_type = _get_hfs_question_type(exam.subject, hfs_question, yp_question);
        }
        params.push(param);
    }
    if (!_.size(params)) return exam_questions;
    let algo_result = [];
    try {
        for (const arr of _.chunk(params, 10)) {
            const res = await Promise.all(arr.map(e => client.algo.recommendQuestions(e)));
            algo_result.push(...res);
        }
    } catch (e) {
        algo_result = [];
        logger.error(e);
    }
    if (!_.size(algo_result)) return exam_questions;
    const result = [];
    //
    for (let i = 0; i < params.length; i++) {
        const param = params[i];
        quest_map.set(param.ques_id, (algo_result[i].questions || []).map(e => e.id));
    }

    for (const hfs_question of hfs_questions) {
        const question = exam_questions.find(e => e.key === hfs_question.key);
        if (!_.isEmpty(question)) {
            result.push(question);
        } else {
            let same = [];
            if (exam.user_paper_id) {
                const kb_question = kb_questions.find(e => e.id.toString() === hfs_question.zujuanQuestionId);
                if (!kb_question) continue;
                same = quest_map.get(kb_question.id.toString()) ||[];
            } else {
                const yp_question = findYpQuestion(hfs_question, yp_paper);
                same = quest_map.get(yp_question.id) ||[];
            }
            if (!_.size(same)) continue;
            result.push({
                key: hfs_question.key,
                same: same
            });
        }
    }
    await collection_exam.updateOne({_id: exam._id}, {$set: {questions: result}});
    exam.questions = result;
    return result;
}

const JOI_POST_EXAM_QUESTION_SEARCH = Joi.object({
    exam_id: Joi.string().required(),
    paper_id: Joi.string().required(),
    offset: Joi.number().required(),
    limit: Joi.number().required(), // 分页参数
    type: Joi.string().optional().allow('', null), // 类型-非必填
    difficulty: Joi.string().optional().allow('', null), // 难度-非必填
    exam_type: Joi.string().optional().allow('', null), // 题源-非必填
    province: Joi.string().optional().allow('', null), // 省份-非必填
    knowledges: Joi.array().items(Joi.object({
        id: Joi.number().required(),
        name: Joi.string().required(),
    })).optional(), // 知识点-非必填
    question_id: Joi.number().optional().allow('', null), // 试题ID-换一题的时候必填
});

async function questionSearch(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const params = await JOI_POST_EXAM_QUESTION_SEARCH.validate(_.assign(req.params, req.body));
        const { exam_id, paper_id, offset, limit } = params;
        const user_id = req.user.id;
        const exam = await getUserExam(user_id, exam_id, paper_id);
        const algo_params = {
            period: exam.period,
            subject: exam.subject,
            grade: exam.grade,
        };
        if (params.type) algo_params.ques_type = params.type;
        if (params.difficulty) algo_params.difficulty = params.difficulty;
        if (params.exam_type) algo_params.exam_type = params.exam_type;
        if (params.province) algo_params.province = params.province;
        if (params.knowledges) algo_params.knowledges = params.knowledges;
        if (params.question_id) {
            algo_params.ques_id = String(params.question_id);
            algo_params.ques_from = 'kb';
        }
        const algo_result = await client.algo.recommendQuestions(algo_params);
        const result = {
            total: 0,
            list: []
        };
        if (_.isEmpty(algo_result)) return responseWrapper.succ(result);
        result.total = algo_result.total;
        const ids = algo_result.questions.slice(offset, offset + limit).map(e => e.id);
        result.list = await client.kb.getQuestions(ids);
        question_utils.addShowTags(result.list);
        for (const question of result.list) {
            const blocks = question['blocks'];
            delete blocks['solutions'];
            delete blocks['answers'];
            delete blocks['explanations'];
        }
        return responseWrapper.succ(result);
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const JOI_GET_EXAM_PAPER_INFO = Joi.object({
    exam_id: Joi.string().required(),
    paper_id: Joi.string().required(),
    category: Joi.number().required(),
});

async function getExamPaperInfoByCategory(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const { exam_id, paper_id, category } = await JOI_GET_EXAM_PAPER_INFO.validate(req.params);
        const user_id = req.user.id;
        const yp_paper = await client.yuanpei.getPaperQuestions(paper_id);
        const status = await getPaperStatus(exam_id, paper_id, yp_paper);
        const exam = await getUserExam(user_id, exam_id, paper_id);
        if (exam.user_paper_id) {
            status.original = enums.BooleanNumber.YES;
        }
        if (!status.original) {
            return responseWrapper.error('HANDLE_ERROR', '未上传原卷');
        }

        let exam_papers = exam.papers || [];
        let exam_paper = exam_papers.find(e => e.category === category);
        if (_.isEmpty(exam_paper)) {
            if (category === 5) return responseWrapper.succ();
            exam_papers = await buildExamPapers(req.user, exam);
            await utils.sleep(50);
            exam_paper = exam_papers.find(e => e.category === category);
        }
        if (_.isEmpty(exam_paper)) return responseWrapper.succ();
        const result = await paper_utils.get_exampaper_detail(exam_paper.id, true);
        return responseWrapper.succ(result);
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const JOI_PUT_EXAM_PAPER_QUESTION = Joi.object({
    exam_id: Joi.string().required(),
    paper_id: Joi.string().required(),
    category: Joi.number().required(),
    action: Joi.number().required(),
    origin_id: Joi.number().optional(),
    id: Joi.number().required()
});

async function putExamPaperQuestionByCategory(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const {exam_id, paper_id, category, action, origin_id, id } = await JOI_PUT_EXAM_PAPER_QUESTION.validate(_.assign(req.params, req.body));
        const user = req.user;
        const user_id = user.id;
        const yp_paper = await client.yuanpei.getPaperQuestions(paper_id);
        const status = await getPaperStatus(exam_id, paper_id, yp_paper);
        if (!status.original) {
            return responseWrapper.error('HANDLE_ERROR', '未上传原卷');
        }
        const exam = await getUserExam(user_id, exam_id, paper_id);
        const paper = (exam.papers || []).find(e => e.category === category);
        if (!_.isEmpty(paper)) {
            const exampaper = await collection_exampaper.findOne({_id: new ObjectID(paper.id)});
            const questions = await client.kb.getQuestions([id]);
            if (action === 1) { // 添加
                paper_utils.insert_questions(exampaper, questions[0]);
            } else if (action === 2) { // 删除
                paper_utils.delete_question(exampaper, id);
            } else { // 替换
                paper_utils.replace_question(exampaper, origin_id, questions[0]);
            }
            paper_utils.render_basket_question_score(exampaper);
            // 保存数据
            const ds = {
                score: exampaper.score,
                volumes: exampaper.volumes,
                utime: new Date()
            }
            await collection_exampaper.updateOne({_id: new ObjectID(paper.id)}, {$set: ds});
        } else {
            if (category === 5 && action === 1) { // 共性

                const questions = await client.kb.getQuestions([id]);
                const paper_params = {
                    user: req.user,
                    name: _get_exampaper_name(category, yp_paper),
                    type: '考后巩固',
                    category: category,
                    questions: questions,
                    period: exam.period,
                    subject: exam.subject,
                    grade: exam.grade,
                }
                const new_paper = paper_utils.build_by_questions(paper_params);
                // paper_utils.set_display(new_paper, 0);
                new_paper.source_type = enums.PaperSourceType.EXAM;
                new_paper.source = enums.PaperSourceType.EXAM;
                new_paper.valid = enums.BooleanNumber.YES;
                new_paper.status = enums.PaperStatus.DONE;
                new_paper.exam_status = enums.ExamStatus.EDITABLE;
                const insert_result = await collection_exampaper.insertOne(new_paper);
                const papers = exam.papers || [];
                papers.push({
                    id: insert_result.insertedId.toString(),
                    type: '考后巩固',
                    category: category,
                    time: new Date(),
                });
                await collection_exam.updateOne({_id: exam._id}, {$set: {papers}});
            }
        }
        return responseWrapper.succ({id});
    } catch (e) {
        logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const CATEGORY_PAPER_NAME = {
    1: '简单卷',
    2: '中等卷',
    3: '培优卷',
    4: '薄弱点巩固卷',
    5: '共性卷',
}

function _get_exampaper_name(category, yp_paper) {
    return `${_.get(yp_paper, 'paper.name', '')} ${CATEGORY_PAPER_NAME[category]}`;
}

const JOI_GET_EXAM_PAPER_QUESTION_IDS = Joi.object({
    exam_id: Joi.string().required(),
    paper_id: Joi.string().required(),
});

async function getExamPaperQuestionIds(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const { exam_id, paper_id } = await JOI_GET_EXAM_PAPER_QUESTION_IDS.validate(req.params);
        const user_id = req.user.id;
        const result = [];
        const exam = await getUserExam(user_id, exam_id, paper_id);
        if (!_.isEmpty(exam)) {
            const papers = exam.papers || [];
            for (const paper of papers) {
                const data = {
                    id: paper.id,
                    category: paper.category,
                    question_ids: []
                }
                const exampaper = await collection_exampaper.findOne({_id: new ObjectID(paper.id)});
                for (const volume of exampaper.volumes) {
                    for (const block of volume.blocks) {
                        data.question_ids.push(...block.questions.map(e => e.id));
                    }
                }
                result.push(data);
            }
        }
        return responseWrapper.succ(result);
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}


const JOI_GET_EXAM_PAPER_TEACHER_HANDOUT = Joi.object({
    exam_id: Joi.string().required(),
    paper_id: Joi.string().required(),
});

async function getExamPaperTeacherHandout(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const { exam_id, paper_id } = await JOI_GET_EXAM_PAPER_TEACHER_HANDOUT.validate(req.params);
        const user_id = req.user.id;
        const yp_paper = await client.yuanpei.getPaperQuestions(paper_id);
        const status = await getPaperStatus(exam_id, paper_id, yp_paper);
        const result = {
            name: _.get(yp_paper, 'paper.name', ''),
            subject: _.get(yp_paper, 'paper.subject', ''),
            teacher_name: req.user.name, // 教师姓名
            school_name: req.user.schoolName, // 学校名称
            class_info: [],
            question_info: []
        };
        if (!status.published) return responseWrapper.succ(result); // 未发布成绩
        const exam = await getUserExam(user_id, exam_id, paper_id);
        // 获取原卷
        let classes = await client.hfsTeacherV3.getExamPaperClasses(user_id, exam_id, paper_id);
        let class_params = [];
        for (const cls of classes) {
            if (cls.relation !==1) continue; //
            class_params.push({
                user_id,
                exam_id,
                paper_id,
                cls
            });
        }
        if (_.size(class_params)) {
            const class_info_res = await Promise.all(class_params.map(p => buildHandoutClassInfo(p)));
            class_params.forEach(p => {
                const class_info = (class_info_res || []).find(clsInfo => p.cls.id === clsInfo.class_id);
                if (!_.isEmpty(class_info)) result.class_info.push(class_info);
            });
        }
        // 试题详细
        const qustions_detail = await client.hfsTeacherV3.getPaperQuestions(user_id, exam_id, paper_id);
        if (_.size(qustions_detail)) {
            let paper_questions = [];
            if (exam.user_paper_id) {
                const tiku_paper = await _get_exampaper_by_id_async(exam.user_paper_id);
                paper_questions = getTikuPaperQuestions(tiku_paper);
            }
            let exam_questions = exam.questions || [];
            if (!_.size(exam_questions) || _.size(exam_questions) !== _.size(qustions_detail)) {
                exam_questions = await buildPaperRecomendQuestions(exam, yp_paper);
            }
            const ids = [];
            exam_questions.forEach(eq => ids.push(...eq.same || []));
            let kb_questions = await client.kb.getQuestions([...new Set(ids)]);
            for (const i in qustions_detail) {
                const qs = qustions_detail[i];
                const exam_question = exam_questions.find(e => e.id === qs.id || e.key === qs.key);
                const tmp_question = {
                    id: qs.id,
                    name: qs.name, // 试题名称
                    // type: _get_hfs_question_type(exam.subject, qs, yp_question), // 类型
                    manfen: qs.manfen, // 满分
                    answer: qs.answer, // 答案-客观题存在
                    pictures: qs.pictures, // 试题图片
                    same: [],
                    key: qs.key,
                    optionstr: qs.optionstr,
                };
                if (exam.user_paper_id) {
                    const tk_question = paper_questions.find(e => e.id.toString() === qs.zujuanQuestionId);
                    if (!tk_question) continue;
                    tmp_question.type = tk_question.type;
                    tmp_question.origin_question = tk_question;
                } else {
                    const yp_question = findYpQuestion(qs, yp_paper);
                    tmp_question.type = _get_hfs_question_type(exam.subject, qs, yp_question);
                }
                if (!_.isEmpty(exam_question) && _.size(exam_question.same)) {
                    tmp_question.same = kb_questions.filter(e => exam_question.same.includes(e.id)); // 相似题
                }
                result.question_info.push(tmp_question);
            }
        }
        return responseWrapper.succ(result);
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

async function buildHandoutClassInfo({user_id, exam_id, paper_id, cls}) {
    const class_info = {
        class_id: cls.id, // 班级ID
        class_name: cls.name, // 班级
        score_view: {}, // 成绩概览
        focus_student: {
            backward: [],
            forward: [],
            unstable: [],
        }, // 关注学生
        question_score: [], // 试题得分明细
        score_report: [], // 成绩单
    };
    const statistics = await client.hfsTeacherV3.getClassPaperStatistics(user_id, exam_id, paper_id, cls.id);
    if (!_.isEmpty(statistics)) {
        class_info.score_view = {
            class_name: _.get(statistics, 'myClass.name'), // 班级名称
            rank: _.get(statistics, 'myClass.avgRank'), // 排名
            score: _.get(statistics, 'myClass.avg'), // 平均分
            excellent_num: _.get(statistics, 'myClass.excellentNum'), // 优秀人数
            excellent_rate: _.get(statistics, 'myClass.excellentRate'), // 优秀率
            good_num: _.get(statistics, 'myClass.goodNum'), // 良好人数
            good_rate: _.get(statistics, 'myClass.goodRate'), // 良好率
            pass_num: _.get(statistics, 'myClass.passNum'), // 及格人数
            pass_rate: _.get(statistics, 'myClass.passRate'), // 及格率
        }
    }
    // 关注学生
    // const focus_students = await client.hfsTeacherV3.getExamPaperClassFocusNeededStudents(user_id, exam_id, paper_id, cls.id, exam.subject);
    // if (!_.isEmpty(focus_students)) {
    //     class_info.focus_student.backward = (focus_students['maxBackwardStudents'] || []).map(e => {
    //         return {
    //             id: e.id,
    //             name: e.name,
    //             grade_rank: e.gradeRank, // 年级排名
    //             grade_rank_range: e.gradeRankRange, // 下降名次
    //         };
    //     });
    //     class_info.focus_student.forward = (focus_students['maxForwardStudents'] || []).map(e => {
    //         return {
    //             id: e.id,
    //             name: e.name,
    //             grade_rank: e.gradeRank, // 年级排名
    //             grade_rank_range: e.gradeRankRange, // 下降名次
    //         };
    //     });
    //     class_info.focus_student.unstable = (focus_students['mostUnstableStudents'] || []).map(e => {
    //         return {
    //             id: e.id,
    //             name: e.name,
    //             grade_rank: e.gradeRank, // 年级排名
    //             grade_rank_range: e.gradeRankRange, // 下降名次
    //         };
    //     });
    // }
    // 成绩单
    const comparison = await client.hfsTeacherV3.getExamPaperClassComparison(user_id, exam_id, paper_id, cls.id);
    if (_.size(comparison)) {
        class_info.score_report = comparison.map(e => {
            return {
                id: e.studentId,
                name: e.studentName, // 学生姓名
                xuehao: e.xuehao, // 学号
                kaohao: e.kaohao, // 考号
                current: { // 当前考试
                    score: _.get(e, 'current.score'),
                    class_rank: _.get(e, 'current.classRank'), // 班级排名
                    grade_rank: _.get(e, 'current.gradeGroupRank'), // 年级排名
                },
                previous: { // 上次考试
                    score: _.get(e, 'previous.score'),
                    class_rank: _.get(e, 'previous.classRank'), // 班级排名
                    grade_rank: _.get(e, 'previous.gradeGroupRank'), // 年级排名
                }
            };
        });
        // 重点关注学生
        let focus_students = comparison.filter(e => {
            const current_class_rank = _.get(e, 'current.classRank');
            if (!current_class_rank || current_class_rank < 0) return false;
            const previous_class_rank = _.get(e, 'previous.classRank');
            if (!previous_class_rank || previous_class_rank < 0) return false;
            return true;
        });
        if (_.size(focus_students)) {
            focus_students.forEach(e => {
                const current_class_rank = _.get(e, 'current.classRank');
                const previous_class_rank = _.get(e, 'previous.classRank');
                e.class_rank = current_class_rank;
                e.class_rank_range = previous_class_rank - current_class_rank;
            });
            let forward = focus_students.filter(e => e.class_rank_range > 0);
            forward = _.orderBy(forward, ['class_rank_range', 'class_rank'], ['desc', 'asc']);
            if (_.size(forward)) {
                forward = forward.slice(0, 5);
                for (const stu of forward) {
                    class_info.focus_student.forward.push({
                        id: stu.studentId,
                        name: stu.studentName,
                        class_rank: stu.class_rank,
                        class_rank_range: stu.class_rank_range,
                    });
                }
            }
            let backward = focus_students.filter(e => e.class_rank_range < 0);
            backward = _.orderBy(backward, ['class_rank_range', 'class_rank'], ['asc', 'desc']);
            if (_.size(backward)) {
                backward = backward.slice(0, 5);
                for (const stu of backward) {
                    class_info.focus_student.backward.push({
                        id: stu.studentId,
                        name: stu.studentName,
                        class_rank: stu.class_rank,
                        class_rank_range: stu.class_rank_range,
                    });
                }
            }
        } else {
            focus_students = comparison.filter(e => {
                const current_class_rank = _.get(e, 'current.classRank');
                if (!current_class_rank || current_class_rank < 0) return false;
                return true;
            });
            focus_students.forEach(e => e.class_rank = _.get(e, 'current.classRank'));
            let forward = _.orderBy(focus_students, ['class_rank'], ['asc']);
            if (_.size(forward)) {
                forward = forward.slice(0, 3);
                for (const stu of forward) {
                    class_info.focus_student.forward.push({
                        id: stu.studentId,
                        name: stu.studentName,
                        class_rank: stu.class_rank,
                    });
                }
            }
            let backward = _.orderBy(focus_students, ['class_rank'], ['desc']);
            if (_.size(backward)) {
                backward = backward.slice(0, 3);
                for (const stu of backward) {
                    class_info.focus_student.backward.push({
                        id: stu.studentId,
                        name: stu.studentName,
                        class_rank: stu.class_rank,
                    });
                }
            }
        }
    }

    // 得分明细
    const questions_score = await client.hfsTeacherV3.getPaperQuestions(user_id, exam_id, paper_id, cls.id);
    if (_.size(questions_score)) {
        class_info.question_score = questions_score.map(e => {
            return {
                id: e.id,
                manfen: e.manfen, // 满分
                grade_score: e.gradeScore, // 年级平均分
                grade_score_rate: e.gradeScoreRate, // 年级得分率
                score: e.avgScore, // 班级平均分
                score_rate: e.classScoreRate, // 班级得分率
                student_num: _.get(e, 'rightNum', 0) + _.get(e, 'halfRightNum', 0) + _.get(e, 'wrongNum', 0),
                // right_num: _.get(e, 'rightNum', 0), // 答题人数
                // half_right_num: _.get(e, 'halfRightNum', 0), // 答题人数
                // wrong_num: _.get(e, 'wrongNum', 0), // 答题人数
                options: e.options, // 客观题-存在
                scores: e.scores, // 主观题-存在
                key: e.key,
            }
        });
    }
    // 排序
    sortByRank(comparison, class_info);
    return class_info;
}

const JOI_GET_EXAM_PAPER_TEACHER_COMMENT = Joi.object({
    exam_id: Joi.string().required(),
    paper_id: Joi.string().required(),
    class_id: Joi.string().required(),
});


async function getExamPaperTeacherComment(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const {exam_id, paper_id, class_id} = await JOI_GET_EXAM_PAPER_TEACHER_COMMENT.validate(req.params);
        const user = req.user;
        const user_id = user.id;
        const yp_paper = await client.yuanpei.getPaperQuestions(paper_id);
        const status = await getPaperStatus(exam_id, paper_id, yp_paper);
        if (!status.original || !status.published) {
            return responseWrapper.error('HANDLE_ERROR', '改试卷缺少原卷或未发布成绩');
        }
        const exam = await getUserExam(user_id, exam_id, paper_id);
        const result = {
            teacher_name: user.name, // 教师名称
            grade: exam.grade, // 年级
            subject: exam.subject, // 科目
            class_name: '', // 班级
            student_num: 0, // 学生数量
            question_info: []
        }
        //
        const classes = await client.hfsTeacherV3.getExamPaperClasses(user_id, exam_id, paper_id);
        const cls = classes.find(e => e.id === class_id);
        result.class_name = cls.name;
        //
        const qustions_score = await client.hfsTeacherV3.getPaperQuestions(user_id, exam_id, paper_id, cls.id);
        if (_.size(qustions_score)) {
            for (const qs of qustions_score) {
                const yp_question = findYpQuestion(qs, yp_paper);
                const question = {
                    id: qs.id,
                    name: qs.name,
                    type: _get_hfs_question_type(exam.subject, qs, yp_question), // 类型
                    grade_score: qs.gradeScore || 0, // 年级平均分
                    grade_score_rate: qs.gradeScoreRate || 0, // 年级得分率
                    score: qs.avgScore || 0, // 班级平均分
                    score_rate: qs.classScoreRate || 0, // 班级得分率
                    manfen: qs.manfen,
                    answer: qs.answer, // 答案
                    pictures: qs.pictures, // 试题图片
                    options: qs.options,
                    scores: qs.scores,
                    knowledges: qs.knowledges,
                    key: qs.key,
                }
                result.question_info.push(question);
            }
        }

        return responseWrapper.succ(result);
    } catch (e) {
        logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const JOI_GET_EXAM_PAPER_DOWNLOAD_INFO = Joi.object({
    exam_id: Joi.string().required(),
    paper_id: Joi.string().required(),
});

async function getExamPaperDownloadInfo(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const { exam_id, paper_id } = await JOI_GET_EXAM_PAPER_DOWNLOAD_INFO.validate(req.params);
        const user_id = req.user.id;
        let result = {
            papers: [], // 可以下载的试卷 1-基础，2-巩固，3-提高，5-共性
            handout: 0, //讲义 0-否，1-是
            comment: 0, //讲评 0-否，1-是
        }
        const yp_data = await client.yuanpei.getPaperQuestions(paper_id);
        const status = await getPaperStatus(exam_id, paper_id, yp_data);
        const exam = await getUserExam(user_id, exam_id, paper_id, yp_data);
        if (!exam) return responseWrapper.succ(result);
        // if (status.original) {
        //     const ques_num = _.size(yp_data.questions);
        //     result.papers = [ // 可以下载的试卷 1-基础，2-巩固，3-提高，5-共性
        //         { category: 1, ques_num },
        //         { category: 2, ques_num },
        //         { category: 3, ques_num },
        //     ];
        // }
        if (status.published) {
            result.handout = 1;
            result.comment = 1;
        }
        if (status.original && _.size(exam.papers)) {
            const ids = exam.papers.map(e => new ObjectID(e.id));
            const papers = await collection_exampaper.find({_id: {$in: ids}}).toArray();
            for (const paper of exam.papers) {
                const tmp = papers.find(e => e._id.toString() === paper.id);
                const num = paper_utils.get_question_num(tmp);
                result.papers = result.papers.filter(e => e.category !== paper.category);
                result.papers.push({
                    id: paper.id,
                    name: tmp.name,
                    category: paper.category,
                    ques_num: num,
                });
            }
        }
        return responseWrapper.succ(result);
    } catch (e) {
        logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const JOI_GET_RESOURCE_DOWNLOAD_INFO = Joi.object({
    exam_id: Joi.string().required(),
    paper_id: Joi.string().required(),
}).unknown(true);

async function getResourceDownloadInfo(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const { exam_id, paper_id } = await JOI_GET_RESOURCE_DOWNLOAD_INFO.validate(req.params);
        const user_id = req.user.id;

        const yp_data = await client.yuanpei.getPaperQuestions(paper_id);
        const { original, published } = await getPaperStatus(exam_id, paper_id, yp_data);
        let result = {
            original: original,
            published: published,
            items: []
        };
        // 未发布成绩或者
        if (!original || !published) return responseWrapper.succ(result);
        let exam = await getUserExam(user_id, exam_id, paper_id, yp_data);
        if (_.isEmpty(exam)) {
            exam = await initExam(req.user.id, exam_id, paper_id, yp_data);
        }
        let exam_papers = exam.papers || [];
        if (!_.size(exam_papers)) {
            exam_papers = await buildExamPapers(req.user, exam);
            await utils.sleep(50);
        }
        if (_.size(exam_papers)) {
            const ids = exam_papers.map(e => new ObjectID(e.id));
            const papers_doc = await collection_exampaper.find({_id: {$in: ids}}).project({_id: 1, name: 1}).toArray();
            for (const paper of exam_papers) {
                const paper_doc = papers_doc.find(e => e._id.toString() === paper.id);
                result.items.push({
                    resource_type: `exam_consolidate`,
                    resource_id: `${exam.paper_id}-${paper.category}`,
                    resource_name: paper_doc.name,
                    resource_info: {
                        exam_id: exam.exam_id,
                        category: paper.category
                    }
                });
            }
            result.items.push({
                resource_type: `exam_consolidate`,
                resource_id: `${exam.paper_id}-6`,
                resource_name: '教师讲义',
                resource_info: {
                    exam_id: exam.exam_id,
                    category: 6
                }
            });
        }
        return responseWrapper.succ(result);
    } catch (e) {
        logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const JOI_GET_EXAM_PAPER_STATUS = Joi.object({
    exam_id: Joi.string().required(),
    paper_id: Joi.string().required(),
}).unknown(true);

async function getExamPaperStatus(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const { exam_id, paper_id } = await JOI_GET_EXAM_PAPER_STATUS.validate(req.params);

        const user_id = req.user.id;
        const hfsExamList = await client.hfsTeacherV3.getTeacherExamList(user_id);
        const result = {
            auth: 0,
            original: 0,
            published: 0,
        };
        const hfsPaper = (hfsExamList || []).find(e => e.paper_id === paper_id);
        if (_.isEmpty(hfsPaper))  return responseWrapper.succ(result);
        result.auth = enums.BooleanNumber.YES;
        const yp_data = await client.yuanpei.getPaperQuestions(paper_id);
        const { original, published } = await getPaperStatus(exam_id, paper_id, yp_data);
        result.original = original;
        result.published = published;
        return responseWrapper.succ(result);
    } catch (e) {
        logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const JOI_GET_EXAM_PAPER_COMPARISON = Joi.object({
    exam_id: Joi.string().required(),
    paper_id: Joi.string().required(),
    is_all: Joi.number().optional().default(0)
}).unknown(true);

async function getExamPaperComparison(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const { exam_id, paper_id, is_all } = await JOI_GET_EXAM_PAPER_COMPARISON.validate(_.assign(req.params, req.query));
        const user_id = req.user.id;
        const result = [];
        // 获取班级列表
        const classes = await client.hfsTeacherV3.getExamPaperClasses(user_id, exam_id, paper_id);
        // 获取成绩列表
        for (const cls of classes) {
            if (!is_all && cls.relation !== 1) continue;
            const class_info = {
                id: cls.id,
                name: cls.name,
                score_report: []
            }
            const comparison = await client.hfsTeacherV3.getExamPaperClassComparison(user_id, exam_id, paper_id, cls.id);
            if (_.size(comparison)) {
                class_info.score_report = comparison.map(e => {
                    return {
                        id: e.studentId,
                        name: e.studentName, // 学生姓名
                        xuehao: e.xuehao, // 学号
                        kaohao: e.kaohao, // 考号
                        current: { // 当前考试
                            score: _.get(e, 'current.score'),
                            class_rank: _.get(e, 'current.classRank'), // 班级排名
                            grade_rank: _.get(e, 'current.gradeGroupRank'), // 年级排名
                        },
                        previous: { // 上次考试
                            score: _.get(e, 'previous.score'),
                            class_rank: _.get(e, 'previous.classRank'), // 班级排名
                            grade_rank: _.get(e, 'previous.gradeGroupRank'), // 年级排名
                        }
                    };
                });
            }
            result.push(class_info);
        }
        return responseWrapper.succ(result);
    } catch (e) {
        logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

async function buildExamPapers(user, exam) {
    let params = [1, 2, 3];
    const exam_papers = exam.papers || [];
    for (const ep of exam_papers) {
        params = params.filter(e => e !== ep.category);
    }
    if (!_.size(params)) return exam_papers;
    //
    try {
        const promises = params.map(category => buildExampaperByCategory(user, exam, category));
        let papers = await Promise.all(promises);
        if (!_.size(papers)) return;
        for (const p of papers) {
            if (_.isEmpty(p)) continue;
            exam.papers.push({
                id: p._id.toString(),
                type: '考后巩固',
                category: p.category,
                time: new Date()
            });
        }
        await collection_exam.updateOne({_id: exam._id}, {$set: {papers: exam.papers, utime: new Date()}});
        return exam.papers;
    } catch (e) {
        logger.error(e);
        return [];
    }
}

async function buildExampaperByCategory(user, exam, category) {
    const yp_data = await client.yuanpei.getPaperQuestions(exam.paper_id);
    let algo_paper = null;
    if (!exam.user_paper_id) {
        const algo_params = _build_level_paper_params(user, exam, category);
        algo_paper = await client.algo.levelPaper(algo_params);
    } else { //
        const tiku_paper = await _get_exampaper_by_id_async(exam.user_paper_id);
        const algo_params = _build_create_paper_params(user, exam, category, tiku_paper);
        algo_paper = await client.algo.detailTablePaper(algo_params);
    }
    const paper = await paper_utils.build_algo_paper(user, algo_paper);
    if (!paper_utils.get_question_num(paper)) return null;
    paper.type = '考后巩固';
    paper.period = exam.period;
    paper.grade = exam.grade;
    paper.subject = exam.subject;
    paper.category = category;
    // paper_utils.set_display(paper, 0);
    paper.name = _get_exampaper_name(category, yp_data);
    const insert_result = await collection_exampaper.insertOne(paper);
    paper._id = insert_result.insertedId;
    return paper;
}

function _build_level_paper_params(user, exam, category) {
    const difficulty_text = {
        1: '简单卷',
        2: '中等卷',
        3: '培优卷',
    }
    const set = new Set();
    for (const q of exam.questions || []) {
        (q.same || []).forEach(id => set.add(id));
    }
    const params = {
        period: exam.period,                        // 必填，学段
        grade: exam.grade,                          // 必填，学段
        subject: exam.subject,                      // 必填，科目
        paper_id: exam.paper_id.split('-')[0],      // 必填，试卷ID
        difficulty: difficulty_text[category],      // 必填，简单卷，中等卷，培优卷
        filtered_ques: [...set],               // 必填，过滤试题ID，默认为[]
        school_id: +(exam.paper_id.split('-')[1]),  // 选填，学校ID
        user_id: user.id.toString(),                           // 选填，用户ID
        // class_name: str,                         // 选填，班级名
        // min_year: int,                              // 选填，最小试题年份限制
    }
    return params;
}

// 细目表组卷
function _build_create_paper_params(user, exam, category, paper) {
    const default_difficulty = category_difficulty[category];
    const params = {
        period: paper.period, // 学段
        grade: paper.grade, // 年级
        subject: paper.subject, // 学科
        type: '平行组卷',                 // 必填，平行组卷
        school_id: user.schoolId,            // 选填，学校ID
        user_id: user.id.toString(),              // 选填，用户ID
        paper_id: exam.paper_id,             // 选填，试卷ID
        blocks: [],
    }
    for (const v of paper.volumes) {
        for (const b of paper.blocks) {
            const block = {
                name: b.title,
                type: b.type,
                questions: []
            };
            params.blocks.push(block);
            for (const q of b.questions) {
                block.questions.push({
                    type: q.type,
                    difficulty: default_difficulty,
                    score: q.score,
                    period: q.period,
                    subject: q.subject,
                    knowledges: q.knowledges
                });
            }

        }
    }

    return params;
}

const category_difficulty = {
    1: '容易',
    2: '中等',
    3: '困难',
    4: '容易',
}

/**
 * 试题转换为细目表
 * 采用大题模式
 * @param user_id
 * @param exam_id
 * @param paper_id
 * @param yp_data
 * @param hfs_questions
 * @returns {Promise<{period: string, subject: *, blocks: *, grade: *, name: string}|null>}
 * @private
 */
async function _transform_to_table(user_id, exam_id, paper_id, yp_data, hfs_questions) {

    const yp_paper = _.get(yp_data, 'paper', null);
    const period = _.get(yp_paper, 'period', '');
    const grade =_.get(yp_paper, 'grade', '');
    const subject = subject_utils.regularSubject(_.get(yp_paper, 'subject', ''));

    const yp_questions = _.get(yp_data, 'questions', []);
    const blocks = [];
    for (const q of yp_questions) {
        const point = q.points[0]; // 大题模式-默认取第一个给分点
        const index = parseInt(point.id.split('-')[1]);
        const hfs_question = hfs_questions.find(e => e.questionId === index);
        if (_.isEmpty(hfs_question)) continue;
        const knowledges = [];
        for (const p of q.points) {
            const questionId = parseInt(point.id.split('-')[1]);
            const knowledge_questions = hfs_questions.filter( e=> e.questionId === questionId);
            if (_.size(knowledge_questions)) {
                for (const k_q of knowledge_questions) {
                    if (!_.size(k_q.knowledges)) continue;
                    const arr = k_q.knowledges.filter(n => !knowledges.find(k => k.id === n.id))
                    if (_.size(arr)) knowledges.push(...arr);
                }
            }
        }
        if (!_.size(knowledges)) continue; // 没有知识点的题目跳过
        const new_ques = {
            knowledges: knowledges.length <= 3 ? knowledges : knowledges.slice(0, 3), //
            type: _get_hfs_question_type(subject, hfs_question, q),
            period,
            subject,
            difficulty: '不限'
        };
        const type = new_ques.type;
        const type_t = TYPES[type] || TYPES['default'];
        new_ques.score = type_t.default_score;
        let block = blocks.find(e => e.type === type);
        if (_.isEmpty(block)) {
            block = {
                type: type,
                questions: []
            };
            const blk_pos = type_t['blk_pos'];
            let add = false;
            for (const i in blocks) {
                const type_t_ = TYPES[blocks[i].type] || TYPES['default'];
                const pos = type_t_['blk_pos'];
                if (pos > blk_pos) {
                    blocks.splice(parseInt(i), 0, block);
                    add = true;
                    break;
                }
            }
            if (!add) blocks.push(block);
        }
        // 加入试题
        block.questions.push(new_ques);
    }
    // 设置名字
    for (let i = 0; i < blocks.length; i++) {
        const block = blocks[i];
        block.name = `${DIGIT_MAP_CHINESE[i+1]}、${block.type}`;
        // delete block.type;
    }

    const table = {
        name: '',
        period,
        grade,
        subject,
        blocks
    };
    return table;
}

/**
 * 获取好分数试题类型
 * @param subject
 * @param hfs_question
 * @param yp_question
 * @returns {Promise<*>}
 * @private
 */
function _get_hfs_question_type(subject, hfs_question, yp_question) {
    const signle_subject_arr = ['语文', '英语'];
    let type = '';
    if (hfs_question.style === '客观题') {
        if (hfs_question.policy === 1) {
            type = '多选题';
        } else {
            if (hfs_question.optionstr === 'TF') {
                type = '判断题';
            } else {
                if (signle_subject_arr.includes(subject)) {
                    type = '单选题';
                } else {
                    type = '选择题';
                }
            }
        }
    } else {
        type = yp_question.type;
    }
    return type;
}


const subject_type_dict = {
    '高中':{
        '语文': {'单选题':[], '填空题':[], '默写题':[], '文言文翻译': [], '文言文阅读':[], '古诗词鉴赏':[], '现代文阅读':[], '综合读写':[]},
        '数学': {'选择题':[], '多选题':[], '填空题':[], '解答题':[]},
        '英语': {'单选题':[], '填空题':[], '语法填空':[], '完形填空':[], '阅读理解':[], '七选五':[], '短文改错':[]},
        '物理': {'选择题':[], '多选题':[], '填空题':[], '解答题':[], '实验探究题':[]},
        '化学': {'选择题':[], '多选题':[], '填空题':[], '解答题':[], '实验探究题':[]},
        '生物': {'选择题':[], '多选题':[], '填空题':[], '解答题':[]},
        '政治': {'选择题':[], '多选题':[], '判断题':[], '填空题':[], '辨析题':[], '简答题':[], '论述题':[], '材料分析题':[]},
        '历史': {'选择题':[], '多选题':[], '填空题':[], '辨析题':[], '简答题':[], '论述题':[], '材料分析题':[], '解答题':[]},
        '地理': {'选择题':[], '多选题':[], '填空题':[], '判断题':[], '解答题':[]}
    },
    '初中':{
        '语文': {'单选题':[], '填空题':[], '默写题':[], '文言文翻译': [], '文言文阅读':[], '古诗词鉴赏':[], '现代文阅读':[], '名著阅读':[], '综合读写':[]},
        '数学': {'选择题':[], '填空题':[], '解答题':[]},
        '英语': {'单选题':[], '填空题':[], '语法填空':[], '完形填空':[], '阅读理解':[], '七选五':[], '短文改错':[]},
        '物理': {'选择题':[], '多选题':[], '填空题':[], '作图题':[], '实验探究题':[], '解答题':[], '材料分析题':[]},
        '化学': {'选择题':[], '多选题':[], '填空题':[], '实验探究题':[], '解答题':[]},
        '生物': {'选择题':[], '多选题':[], '填空题':[], '判断题':[], '连线题':[], '解答题':[], '实验探究题':[]},
        '政治': {'选择题':[], '多选题':[], '填空题':[], '判断题':[], '材料分析题':[], '简答题':[], '辨析题':[], '论述题':[]},
        '历史': {'选择题':[], '多选题':[], '填空题':[], '改错题':[], '连线题':[], '辨析题':[], '简答题':[], '论述题':[], '材料分析题':[]},
        '地理': {'选择题':[], '多选题':[], '填空题':[], '判断题':[], '连线题':[], '解答题':[]}
    },
    '小学':{
        '语文': {'单选题':[], '填空题':[], '默写题':[], '文言文翻译': [], '文言文阅读':[], '古诗词鉴赏':[], '现代文阅读':[], '综合读写':[]},
        '数学': {'选择题':[], '多选题':[], '填空题':[], '解答题':[], '判断题':[], '计算题':[], '操作题':[], '应用题':[]},
        '英语': {'单选题':[], '填空题':[], '语法填空':[], '完形填空':[], '阅读理解':[], '七选五':[], '短文改错':[]},
    }
};

const JOI_GET_EXAM_PAPER_STUDENT_QUESTION_ANSWER = Joi.object({
    exam_id: Joi.string().required(),
    paper_id: Joi.string().required(),
    class_id: Joi.string().required(),
    student_id: Joi.string().required(),
    question_id: Joi.string().required(),
});

async function getExamPaperStudentQuestionAnswer(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const { exam_id, paper_id, class_id, student_id, question_id } = await JOI_GET_EXAM_PAPER_STUDENT_QUESTION_ANSWER.validate(req.params);
        const result = await client.hfsTeacherV3.getExamPaperStudentQuestionAnswer(req.user.id, exam_id, paper_id, class_id, student_id, question_id);
        return responseWrapper.succ(result);
    } catch (e) {
        logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const JOI_GET_EXAM_PAPER_QUESTION_ANSWER_IMAGE = Joi.object({
    exam_id: Joi.string().required(),
    paper_id: Joi.string().required(),
    question_id: Joi.string().required(),
    offset: Joi.number().required(),
    limit: Joi.number().required(),
    class_id: Joi.string().optional(),
    min: Joi.number().optional().default(-1),
    max: Joi.number().optional().default(-1)
}).unknown(true);

async function getExamPaperQuestionAnswerImage(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const { exam_id, paper_id, question_id, class_id, min = -1, max = -1, offset, limit } = await JOI_GET_EXAM_PAPER_QUESTION_ANSWER_IMAGE.validate(_.assign(req.params, req.query));
        const user_id = req.user.id;
        const result = {
          total: 0,
          list: []
        };
        const exam = await getUserExam(user_id, exam_id, paper_id);
        // 获取关联班级
        let classes = await client.hfsTeacherV3.getExamPaperClasses(user_id, exam_id, paper_id);
        classes = (classes || []).filter(e => e.relation === 1); // 教学班级
        if (!_.size(classes)) return responseWrapper.succ(result);
        const classIds = classes.map(e => e.id).join(',');
        const data = await client.hfsTeacherV3.getExamPaperQuestionAnswerPictures(user_id, exam_id, paper_id, question_id, classIds)
        let question = exam.questions.find(e => e.id === question_id);
        if (_.isEmpty(question)) {
            return responseWrapper.succ(result);
        }
        if (!question.hasOwnProperty('excellent')) {
            question.excellent = data.excellent_answer || [];
            question.mediocre = data.mediocre_answer || [];
            await collection_exam.updateOne({_id: exam._id}, {$set: {questions: exam.questions}})
        }
        let list = [];
        if (class_id === 'excellent') {
            list  = question.excellent || [];
        } else if (class_id === 'mediocre') {
            list = question.mediocre || [];
        } else if (class_id){
            list = (data.answer || []).filter(e => +e.class_id === +class_id);
        } else {
            list = data.answer || [];
        }
        if (min !== -1 && max !== -1) {
            list = list.filter(e => e.score >= min && e.score <= max);
        }
        result.total = _.size(list);
        if (result.total) {
            result.list = list.slice(offset, offset + limit);
        }
        result.excellent_count = _.size(question.excellent || []);
        result.mediocre_count = _.size(question.mediocre || []);
        if (_.size(result.list)) {
            for (const data of result.list) {
                delete data.origin_pictures;
            }
            if (class_id === 'excellent' || class_id === 'mediocre') {
                let pictures = result.list.map(e => e.pictures);
                pictures = await client.hfsTeacherV3.getExamPaperQuestionAnswerPictureUrl(exam_id, paper_id, pictures);
                for (const i in result.list) {
                    result.list[i].pictures = pictures[i];
                }
            } else {
                for (const data of result.list) {
                    let tagData = question.excellent.find(e => e.id === data.id);
                    if (!_.isEmpty(tagData)) {
                        data.tag = tagData.tag;
                    }
                    tagData = question.mediocre.find(e => e.id === data.id);
                    if (!_.isEmpty(tagData)) {
                        data.tag = tagData.tag;
                    }
                }
                // 排序
                result.list = _.orderBy(result.list, ['score', 'id'], ['desc', 'asc']);
            }
        }
        return responseWrapper.succ(result);
    } catch (e) {
        logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const JOI_PUT_EXAM_PAPER_QUESTION_ANSWER_TAG = Joi.object({
    exam_id: Joi.string().required(),
    paper_id: Joi.string().required(),
    question_id: Joi.string().required(),
    type: Joi.string().required(),
    student_id: Joi.string().required()
}).unknown(true);

async function putExamPaperQuestionAnswerTag(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const { exam_id, paper_id, question_id, type, student_id } = await JOI_PUT_EXAM_PAPER_QUESTION_ANSWER_TAG.validate(_.assign(req.params, req.body));
        const user_id = req.user.id;
        const exam = await getUserExam(user_id, exam_id, paper_id);
        const hfs_questions = await client.hfsTeacherV3.getPaperQuestions(exam.user_id, exam.exam_id, exam.paper_id);
        if (!_.size(hfs_questions)) return responseWrapper.error('HANDLE_ERROR', '未获取到试题');
        const hfs_question = hfs_questions.find(e => e.id === question_id);
        if (!hfs_question) return responseWrapper.error('HANDLE_ERROR', '未获取到试题');
        const question = exam.questions.find(e => e.id === question_id || e.key === hfs_question.key);
        if (!_.isEmpty(question)) {
            const obj = (question[type] || []).find(e => e.id === student_id);
            if (_.isEmpty(obj)) {
                let classes = await client.hfsTeacherV3.getExamPaperClasses(user_id, exam_id, paper_id);
                classes = (classes || []).filter(e => e.relation === 1); // 教学班级
                const classIds = classes.map(e => e.id).join(',');
                const data = await client.hfsTeacherV3.getExamPaperQuestionAnswerPictures(user_id, exam_id, paper_id, question_id, classIds)
                const newInfo = (data.answer || []).find(e => e.id === student_id);
                if (!_.isEmpty(newInfo)) {
                    newInfo.pictures = newInfo.origin_pictures;
                    newInfo.tag = type === 'excellent' ? 2 : 3;
                    delete newInfo.origin_pictures;
                    question[type] = question[type] || [];
                    question[type].push(newInfo);
                }
            } else {
                // 删除
                question[type] = question[type].filter(e => e.id !== student_id);
            }
            await collection_exam.updateOne({_id: exam._id}, {$set: {utime: new Date(), questions: exam.questions}});
        }
        return responseWrapper.succ('');
    } catch (e) {
        logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

async function text2ttf(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        let text = req.body.text;
        if (_.isEmpty(text)){
            return responseWrapper.error('PARAMETERS_ERROR', '转换文本不能为空');
        }
        const fontmin = new Fontmin().src('lib/fonts/*.ttf').use(Fontmin.glyph({text,hinting: false}));
        const files = await fontmin.runAsync();
        const file = files[0];
        const base64 = file.contents.toString('base64');
        return responseWrapper.succ(base64);
    } catch (e) {
        logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

function findYpQuestion(hfs_question, yp_paper) {
    let result = null;
    if (_.isEmpty(yp_paper)) return result;
    for (const yp_question of yp_paper.questions || []) {
        const point = (yp_question.points || []).find(e => e.id.split('-')[1] === hfs_question.questionId.toString());
        if (!_.isEmpty(point)) {
            result = yp_question;
            break;
        }
    }
    return result;
}

async function getUserExam(user_id, exam_id, paper_id) {
    return await collection_exam.findOne({user_id: user_id, exam_id: exam_id, paper_id: paper_id});
}

async function initExam(user_id, exam_id, paper_id, yp_paper) {
    const exam = {
        user_id: user_id,
        exam_id: exam_id,
        paper_id: paper_id,
        grade: _.get(yp_paper, 'paper.grade'), // 年级
        period: _.get(yp_paper, 'paper.period'), // 学段
        subject: _.get(yp_paper, 'paper.subject'), // 科目
        papers: [],
        class_papers: [],
        ctime: new Date(),
        utime: new Date(),
    };
    const insert_result = await collection_exam.insertOne(exam);
    exam._id = insert_result.insertedId;
    return exam;
}

