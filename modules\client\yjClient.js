/**
 * 算法
 * wiki: https://wiki.iyunxiao.com/pages/viewpage.action?pageId=25232048
 */

const _ = require('lodash');
const URL = require('url');
const config = require('config');
const axios = require('axios');
const logger = require('../utils/logger');
const server = config.get('YJ_API_SERVER');
const auth_server = config.get('YJ_AUTH_SERVER');
const qs = require('querystring');
const utils = require('../utils/utils')
const jwt = require('jsonwebtoken');
const util = require('util');

module.exports = {
    getUserInfo,
    getUnifyToken,
    getUserListByCheck,
};

async function getUserInfo(userId) {
    let tokenKey = Buffer.from(server.appCenterKey, 'base64');
    const token = await util.promisify(jwt.sign)({ 'removed': true }, tokenKey, {
        algorithm: 'HS512',
        jwtid: userId.toString(),
        noTimestamp: false
    });

    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: '/v353/anno/user/profile/ForAppcenter',
        search: qs.stringify({
            token: token
        })
    });
    const result = await axios.get(url);
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`阅卷获取用户信息失败: url: ${url}, status: ${result.status}`);
        return null;
    }
    return result.data && result.data.data || null;
}

async function getUnifyToken(userId) {
    const url = URL.format({
        protocol: auth_server.protocol,
        hostname: auth_server.hostname,
        port: auth_server.port,
        pathname: '/user/genUnifyToken',
    });
    const result = await axios.post(url, {userId}, {headers: {apiKey: auth_server.apiKey}});
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`阅卷获取unifyToken失败: url: ${url}, status: ${result.status}`);
        return null;
    }
    return result.data && result.data.data || null;
}


async function getUserListByCheck(phone, password) {
    const url = URL.format({
        protocol: auth_server.protocol,
        hostname: auth_server.hostname,
        port: auth_server.port,
        pathname: '/user/query/yj/userList/check',
    });
    const result = await axios.post(url, {
        phone: phone,
        password: password,
        includeSpecialUsers: true,
    });
    if (!result || result.status !== 200 || !result.data) {
        logger.error(`阅卷获取用户失败: url: ${url}, status: ${result.status}`);
        return null;
    }
    return result.data && result.data.data || null;
}