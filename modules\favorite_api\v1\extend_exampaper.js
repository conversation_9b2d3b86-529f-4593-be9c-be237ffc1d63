var http = require('http');
var config = require('config');
var HttpWrapper = require('../../utils/http_wrapper.js');
var qs = require('querystring');
const URL = require('url');
const KBSERVER = config.get('KB_API_SERVER');
const client = require('zipkin-middleware').client;

const TIMEOUT = 60 * 1000;
const RETRY_TIMES = 1;

function getKbExampaper(exam_id, callback){
    var server = config.get('KB_API_SERVER');
    var url = '/kb_api/v2/exampapers/no_view/'+ exam_id + '?api_key=' + server.appKey;
    return doHttpWrapper(url, '', 'GET', callback);
}

function doHttpWrapper(path, data, method, callback){
    
    var data = JSON.stringify(data);
    var server = config.get('KB_API_SERVER');
    var options = {
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        path: path,
        method: method,
        headers: {
            'content-type': 'application/json',
        },
        timeout: TIMEOUT,
        retryTimes: RETRY_TIMES,
    }
    var httpWrapper = new HttpWrapper()
    return httpWrapper.request(options, (err, res) => {
         if (res.statusCode == 200){
            var ret = JSON.parse(res.data);
            return callback(null, ret);
         } else {
            return callback(err, null);
         }
    })
}

async function getExampapersByIds(ids) {
    if (!ids || !ids.length) {
        return [];
    }
    //获取试卷信息 from kb
    let exampaperIds = ids.join(',');
    let kbUrl = URL.format({
        protocol: KBSERVER.protocol,		
        hostname: KBSERVER.hostname,		
        port: KBSERVER.port,		
        pathname: `/kb_api/v2/exampapers/${exampaperIds}/list`,
        search: qs.stringify({
            api_key: KBSERVER.appKey,
            request_all: true
        })
    });
    let _body = await client.axios.get(kbUrl);
    return _body.data;
}
module.exports = {
    getKbExampaper: getKbExampaper,
    getExampapersByIds: getExampapersByIds
}
