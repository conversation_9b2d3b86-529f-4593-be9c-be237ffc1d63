var config = require('config');
var request = require('request');
var qs = require('querystring');
var URL = require('url');
var KBServerCfg = config.get('KB_API_SERVER');

var mongodber = require('../../utils/mongodber');
var db = mongodber.use('tiku');

var ResponseWrapper = require('../../middlewares/response_wrapper');
var logger = require('../../utils/logger');
var period2Subjects = require('./period2Subject.json');
var normJuniorGrade = require('../../user_api/v1/utils.js').normJuniorGrade;
var mapExampaperGrade = require('../../user_api/v1/mapBookGrade.json');
var normPeriod = function(grade){
    if(['七','八','九','初'].indexOf(grade.charAt(0)) >= 0)
        return '初中';
    else if(grade.indexOf('高') >= 0)
        return '高中';
    else
        return '小学';
}

function _requestBooks(queryStr,  callback){
    var booksUrl = URL.format({
        protocol: KBServerCfg.protocol,
        hostname: KBServerCfg.hostname,
        port: KBServerCfg.port,
        pathname: 'kb_api/v2/books',
        search: queryStr,
    });

    request({
        url: booksUrl,
        timeout: 1000
    },function(err, response, data){
        if (err){
            return callback(err, null);
        }
        
        try{
            var _data = JSON.parse(data);

            if (response.statusCode !== 200){
                logger.error(['获取教材失败', booksUrl].join(':'));
                return callback('HANDLE_ERROR');
            }
            //var book = _data.book;
            return callback(null, _data);
        }catch(err){
            logger.error(err.message);
            return callback(err, null);
        }
    });
}

function getSubjects(req, res){
    var responseWrapper = new ResponseWrapper(req, res);
    try{
        req.user.grade = req.query.grade || req.user.grade ;
        if (!req.user.grade) {
            return responseWrapper.error('PARAMETERS_ERROR','无法获取年级信息');     
        }
        var period = normPeriod(req.user.grade);
        var queryStr = qs.stringify({
            api_key: req.apiKey,
            period: period,
        });
    }catch(err){
        return responseWrapper.error('PARAMETERS_ERROR');    
    }
    
    var subjectUrl = URL.format({
        protocol: KBServerCfg.protocol,
        hostname: KBServerCfg.hostname,
        port: KBServerCfg.port,
        pathname: 'kb_api/v2/book_answers/subjects',
        search: queryStr,
    });

    request({
        url: subjectUrl,
        timeout: 1000
    }, function(err, response, data){
        if (err){
            return responseWrapper.error('HANDLE_ERROR');
        }
        if (response.statusCode !== 200){
            return responseWrapper.error('HANDLE_ERROR');
        }
        try{
            var _data = JSON.parse(data);
            var openSubjects = _data.subjects;
            var retObj = [];
            var allSubjects = period2Subjects[period];

            if (allSubjects === undefined){
                return responseWrapper.error('NULL_ERROR', '学段信息有误或者尚未支持此学段');
            }
            allSubjects.forEach(function(item, index){
                let obj = {name: item};
                if (openSubjects.indexOf(item) !== -1){
                    obj.isExist = true;
                }else{
                    obj.isExist = false;
                }
                retObj.push(obj);
            });

            return  responseWrapper.succ(retObj);
        }catch(err){
            return responseWrapper.error('HANDLE_ERROR', err.message);
        
        }
    });
}
//生成每节的id    
function _genChapterID(book){
    try{
        book.chapters.forEach(function(chapter){
            chapter.chapters.forEach(function(section){
                var ids = '';
                section.chapters.forEach(function(ques){
                    if (ids === ''){
                        ids = ques.id + '';
                    } else {
                        ids = ids +',' +ques.id;    
                    }
                });
                section.id = ids;
                delete section.chapters;
            });        
        });
        return book;
    }catch (err){
        return undefined;
    }
}

//根据学科获取结构
function getBookStruct(req, res){
    var responseWrapper = new ResponseWrapper(req, res);
    try{
        var userId = req.user.id;
        var apikey = req.apiKey;
        var subject = req.params.subject;
        req.user.grade = req.query.grade ||req.user.grade ;
        if (!req.user.grade) {
            return responseWrapper.error('PARAMETERS_ERROR','无法获取年级信息');     
        }
        var period = normPeriod(req.user.grade);
    }catch(err){
        return responseWrapper.error('PARAMETERS_ERROR');
    }
    
    try{ 
        var grade = mapExampaperGrade[period][subject][req.user.grade];
    }catch(err){
        return responseWrapper.error('NULL_ERROR','你所在学段暂不学习本学科，你可以在个人信息中修改学段获取本学科资源');
    }
    
    try{
        var subject = req.params.subject;

        var queryStr = qs.stringify({
            type: 'answer',
            period: period,
            subject: subject,
            api_key: apikey
        });
        _requestBooks(queryStr, function(err, data){
            if (err){
                return responseWrapper.error('HANDLE_ERROR');
            }
            var bookIds = [];
            try {
                subjectBooksArray = data.book.children[0].children[0].children;
                subjectBooksArray.forEach(function(pressBooks){
                    pressBooks.children.forEach(function(book){
                        bookIds.push(book.id);
                    })
                });
            }catch(err){
                return responseWrapper.error('NULL_ERROR','获取科目图书打包失败');
            }
            //book = books.filter(function(item){return item.key === grade;});

            db.collection('user').findOne({_id: userId}, function(err, docUser){
                if (err){    
                    return responseWrapper.error('HANDLE_ERROR');
                } 
                if (!docUser){
                    docUser = {};
                }
                book = (function(docUser){
                    if (!docUser.hasOwnProperty('book_profile')){
                        return undefined;
                    }
                    books = docUser.book_profile.filter(function(item){
                        if (item.subject === subject
                            && item.period === period
                            && item.type === 'book'
                            ){
                            return true;
                        }
                    });

                    if (books.length === 0){
                        return undefined;
                    } else if (books.length >= 1){
                        books[0].id = parseInt(books[0].id);
                        return books[0];
                    }

                })(docUser);

                var book_id = -1;
                if (!book || bookIds.indexOf(book.id) === -1){
                    
                    for (var pressIndex = 0 ; pressIndex <subjectBooksArray.length; pressIndex++){
                        var flag = false;
                        var curArray = subjectBooksArray[pressIndex].children;
                        for (var bookIndex = 0 ; bookIndex < curArray.length ; bookIndex++){
                            if (curArray[bookIndex].name === grade){
                                flag = true;
                                book_id = curArray[bookIndex].id;
                                break;
                            }
                        }
                        if (flag){
                            break;
                        }
                    }
                    

                } else {
                    book_id = book.id;
                }
                if  (book_id !== -1){
                    var subjectUrl = URL.format({
                        protocol: KBServerCfg.protocol,
                        hostname: KBServerCfg.hostname,
                        port: KBServerCfg.port,
                        pathname: 'kb_api/v2/books/'+ book_id + '/answer',
                        search: qs.stringify({
                            api_key: apikey    
                        })
                    });

                    request({
                        url: subjectUrl,
                        timeout: 1000
                    },function(err, response, doc){
                        try {
                            if (err){ 
                                return responseWrapper.error('HANDLE_ERROR');
                            }
                            if (response.statusCode != 200){
                                return responseWrapper.error('HANDLE_ERROR');
                            }
                            var _doc = JSON.parse(doc);
                            _doc = _genChapterID(_doc);
                            return responseWrapper.succ(_doc);
                        }catch (err){
                            return responseWrapper.error('HANDLE_ERROR');
                        }
                    });

                }else {
                    return responseWrapper.error('NULL_ERROR','你所在学段暂不学习本学科，你可以在个人信息中修改学段获取本学科资源');
                }
            });
        });
    
    }catch(err){
        return responseWrapper.error('HANDLE_ERROR');
    }
}

//根据答案id获取详情
function getChapterAnswers(req, res){
    var responseWrapper = new ResponseWrapper(req, res);

    try {
        var chapter_id = req.params.chapter_id;
        var device = req.query.device;

        var chapterUrl = URL.format({
            protocol: KBServerCfg.protocol,
            hostname: KBServerCfg.hostname,
            port: KBServerCfg.port,
            pathname: 'kb_api/v2/book_chapters/'+ chapter_id +'/answer',
            search: qs.stringify({
                api_key: req.apiKey,
                device: device,
                api_key: req.apiKey
            })
        });
    }catch(err){
        responseWrapper.error('PARAMETERS_ERROR');
    }
    request({
        url: chapterUrl,
        timeout: 1000
        },function(err, response, doc){

            try{
            
                if (err){
                    return responseWrapper.error('HANDLE_ERROR');
                }

                if (response.statusCode !== 200){
                    return responseWrapper.error('HANDLE_ERROR');
                }

                var _doc = JSON.parse(doc);
                return responseWrapper.succ(_doc);
            }catch(err){
                return responseWrapper.error('HANDLE_ERROR');
            }
        });
}
//获取包含作业记录的教材打包接口
function getBooks(req, res){ 
    var responseWrapper = new ResponseWrapper(req, res);
    try{
        req.user.grade = req.query.grade || req.user.grade ;
        if (!req.user.grade) {
            return responseWrapper.error('PARAMETERS_ERROR','无法获取年级信息');     
        }
        var period = normPeriod(req.user.grade);
        var queryStr = qs.stringify({
            type: 'answer',
            period: period,
            api_key: req.apiKey
        });
    }catch (err){
        responseWrapper.error('PARAMETERS_ERROR'); 
    }
    _requestBooks(queryStr ,function(err, doc){
            if (err){
                return responseWrapper.error('HANDLE_ERROR');
            }
            return responseWrapper.succ(doc);        
    });
}

module.exports = {
    getSubjects: getSubjects,
    getBookStruct: getBookStruct,
    getChapterAnswers: getChapterAnswers,
    getBooks: getBooks,

}
