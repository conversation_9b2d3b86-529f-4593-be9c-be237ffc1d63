const _ = require('lodash');
const enums = require('../bin/enum');
const client = require('../modules/client');
const uuid = require('uuid');
const userSet = new Set();

module.exports = {
    checkToken,
    checkLimit,
    getAll,
    getTokenUserId,
}

async function checkToken(req) {
    if (req.query.token && req.query.source === enums.UserSource.HFS_APP_JS) {
        const data = await client.hfsTeacherV3.getTeacherInfoByToken(req.query.token);
        if (!_.isEmpty(data)) {
            userSet.add(Number(data.id));
        }
    }
}

async function getTokenUserId(req) {
    if (req.query.token && req.query.source === enums.UserSource.HFS_APP_JS) {
        const data = await client.hfsTeacherV3.getTeacherInfoByToken(req.query.token);
        if (!_.isEmpty(data)) {
            return data.id;
        }
    }
    return uuid.v4().split('-').join('');
}

function checkLimit(user_id) {
    return userSet.has(user_id);
}

function getAll() {
    return [...userSet];
}
