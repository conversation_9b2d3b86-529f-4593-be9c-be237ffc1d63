const moment = require('moment');
const _ = require('lodash');
const mongodber = require('../../utils/mongodber');
const db = mongodber.use('tiku');
const ObjectId = require('mongodb').ObjectId;
const enums = require('../../../bin/enum');
const schema = require('../../../bin/schema');

const SCHOOL_DOWNLOAD_LIMIT = 1000;

module.exports = {
    check_school_download_limit,
}

async function check_school_download_limit(schoolId) {
    const cond = {
        sch_id: schoolId,
        ctime: {
            $gte: moment().startOf('day').toDate()
        }
    };
    const count = await db.collection(schema.user_download).find(cond).count();
    if (count >= SCHOOL_DOWNLOAD_LIMIT) return false;
    return true;
}
