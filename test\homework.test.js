const config = require('config');
const cookies = require('./config.js').config;
const TIKUSERVER = config.get('TIKU_SERVER');
const expect = require('chai').expect;
const superagent = require('superagent');
const url = require('url');
const port = process.env.NODE_PORT;
const host = url.format({
    protocol: TIKUSERVER.protocol,
    hostname: TIKUSERVER.hostname,
    port: port,
});


//console.log(config.studentCookie);


describe('作业大师API:获取包含作业记录学科列表',function(){
   let url = [host, '/homework_api/v1/subjects'].join('');         
    
   it('正例测试',function(done){
        superagent
        .get(url)
        .set('Cookie',cookies.studentCookie)
        .end(function(err, res){
            cookies.studentCookie;
            expect(err).to.be.an('null');    
            expect(res).to.not.be.an('null');
            expect(res.status).to.be.equal(200);
            var ret = JSON.parse(res.text);
            expect(ret.code).to.be.equal(0);
            expect(ret.data).to.be.an('array');
            expect(ret.data.length).to.not.be.equal(0);
            for (let index = 0 ; index < ret.data.length ; ++index){
                expect(ret.data[index]).to.have.keys('name','isExist');
            }
            done();
        });
    });

});

describe('作业大师API:根据学科获取结构',function(){
    
    //
    //数学 物理 化学 生物 政治 历史
    var subjects= ['数学','物理','化学','生物'];//,'政治','历史'];
    for (let i = 0 ; i < subjects.length; ++i){
        let url = [host, `/homework_api/v1/subjects/${encodeURIComponent(subjects[i])}/book`].join('');
        
        it(`正例测试${i}`,function(done){
            superagent
            .get(url)
            .set('Cookie',cookies.studentCookie)
            .end(function(err, res){
                expect(err).to.be.a('null');
                expect(res).to.not.be.a('null');
                expect(res.status).to.be.equal(200);
                var ret = JSON.parse(res.text);
                expect(ret.code).to.be.equal(0);
                expect(ret.data.chapters).to.be.an('array');
                expect(ret.data.chapters[0]).to.have.keys('name','chapters');
                let chapters = ret.data.chapters[0];
                expect(chapters.chapters).to.be.an('array');
                done();
            });
        });
    }
    //英语 
    let url = [host, `/homework_api/v1/subjects/${encodeURIComponent('英语')}/book`].join('');
    debugger;
    it('反例测试',function(done){
        superagent
        .get(url)
        .set('Cookie',cookies.studentCookie)
        .end(function(err, res){
            expect(err).to.be.a('null');
            expect(res).to.not.be.a('null');
            expect(res.status).to.be.equal(200);
            let ret = JSON.parse(res.text);
            debugger;
            expect(ret.code).to.be.equal(5);
            done();
        });
    });

    
});
    

describe('作业大师API:根据答案id获取章节详情',function(){
    var url = [host, '/homework_api/v1/chapters/1872494591,1872429055'].join('');
    it('正例测试 device:pc',function(done){
        superagent
        .get(url)
        .query({device: 'pc'})
        .set('Cookie',cookies.studentCookie)
        .end(function(err, res){
            expect(err).to.be.a('null');
            expect(res).to.not.be.a('null');
            expect(res.status).to.be.equal(200);
            var ret = JSON.parse(res.text);
            expect(ret.code).to.be.equal(0);
            expect(ret.data.chapters).to.be.an('array');
            let chapter = ret.data.chapters[0];
            expect(chapter).to.have.keys('name','answers');
            done();
        });
    });

    it('正例测试 device:mobile',function(done){
        superagent
        .get(url)
        .query({device: 'mobile'})
        .set('Cookie',cookies.studentCookie)
        .end(function(err, res){
            expect(err).to.be.a('null');
            expect(res).to.not.be.a('null');
            expect(res.status).to.be.equal(200);
            var ret = JSON.parse(res.text);
            expect(ret.code).to.be.equal(0);
            expect(ret.data.chapters).to.be.an('array');
            let chapter = ret.data.chapters[0];
            expect(chapter).to.have.keys('name','answers_mobile');
            done();
        });
    });
});



describe('作业大师API:获取包含作业记录的教材打包接口',function(){
    var url = [host, '/homework_api/v1/books'].join('');
    it('正例测试',function(done){
        superagent
        .get(url)
        .set('Cookie',cookies.studentCookie)
        .end(function(err, res){
            expect(err).to.be.a('null');
            expect(res).to.not.be.a('null');
            expect(res.status).to.be.equal(200);
            var ret = JSON.parse(res.text);
            expect(ret.code).to.be.equal(0);
            expect(ret.data.book.children).to.be.an('array');
            done();
        });
    });
});

