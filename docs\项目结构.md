# 项目结构说明

## 题库服务器 (tiku_serv)

本项目是一个基于 Node.js v10.24.1 的教育平台后端 API 服务器，提供题库、试卷、作业等教育资源的管理功能。

## 目录结构

```
tiku_serv/
├── app.js                 # 应用程序入口文件
├── package.json           # 项目配置和依赖
├── .nvmrc                 # Node.js 版本配置 (10.24.1)
├── .eslintrc.js          # ESLint 代码规范配置
├── CLAUDE.md             # Claude Code 项目指导文件
│
├── bin/                   # 可执行脚本
├── build                  # 前端构建脚本
│
├── config/                # 环境配置文件
│   ├── development.json   # 开发环境配置
│   ├── test.json         # 测试环境配置
│   ├── gray.json         # 灰度环境配置
│   └── production.json   # 生产环境配置
│
├── data/                  # 数据文件
│   └── excel/            # Excel 模板文件
│
├── docs/                  # 项目文档
│   ├── 操作汇总.md       # 每日操作汇总
│   ├── 待办事项.md       # 项目待办列表
│   ├── 项目结构.md       # 本文件
│   └── 操作记录/         # 按日期组织的操作日志
│       └── 2025/
│           └── 01/
│
├── lib/                   # 库文件和资源
│   ├── excel/            # Excel 处理相关
│   └── fonts/            # 字体文件
│
├── logs/                  # 日志文件目录
│
├── modules/               # 核心业务模块
│   ├── album_api/        # 专辑/收藏管理
│   ├── assemble_api/     # 组卷功能
│   ├── client/           # 外部服务客户端
│   │   ├── cas.js       # CAS 单点登录
│   │   ├── hfs.js       # 文件服务
│   │   ├── wechat.js    # 微信集成
│   │   └── yunxiao.js   # 云小服务
│   ├── exam_api/         # 考试管理
│   ├── exampaper_api/    # 试卷管理
│   ├── homework_api/     # 作业管理
│   ├── middlewares/      # Express 中间件
│   │   ├── auth.js      # 认证中间件
│   │   ├── resWrapper.js # 响应包装器
│   │   └── verify.js    # 验证中间件
│   ├── tiku_api/         # 题库核心 API
│   ├── user_api/         # 用户管理
│   └── utils/            # 工具函数
│       ├── dbutils.js   # MongoDB 工具
│       ├── redis.js     # Redis 缓存
│       ├── logger.js    # 日志工具
│       └── common.js    # 通用工具
│
├── routes/                # 路由定义
│   ├── index.js          # 主路由文件
│   └── [module]_routes.js # 各模块路由
│
├── test/                  # 测试文件
│   └── test_*.js         # Mocha 测试用例
│
└── uploads/               # 文件上传目录
```

## 核心模块说明

### 1. 题库模块 (tiku_api)
- 题目的增删改查
- 题目分类管理
- 题目导入导出
- 题目搜索和筛选

### 2. 试卷模块 (exampaper_api)
- 试卷创建和编辑
- 试卷发布和管理
- 试卷权限控制
- 答题记录管理

### 3. 作业模块 (homework_api)
- 作业布置
- 作业提交
- 作业批改
- 成绩统计

### 4. 用户模块 (user_api)
- 用户注册登录
- 权限管理
- 角色管理
- CAS 单点登录集成

### 5. 组卷模块 (assemble_api)
- 智能组卷
- 手动组卷
- 组卷模板
- 试卷预览

## 技术栈

- **运行环境**: Node.js v10.24.1
- **Web 框架**: Express.js 4.x
- **数据库**: MongoDB (主数据库), Redis (缓存)
- **认证**: JWT, CAS
- **测试**: Mocha, Chai
- **日志**: log4js
- **文件上传**: Multer
- **其他**: 微信支付、Excel 处理等

## API 结构

- `/v1/*` - 版本 1 API（旧版本）
- `/v2/*` - 版本 2 API（当前版本）
- 公开路由无需认证
- 受保护路由需要 JWT Token 或 CAS 认证

## 开发说明

1. 使用 `npm run dev` 启动开发服务器
2. 代码规范遵循 `.eslintrc.js` 配置
3. 环境配置通过 `NODE_ENV` 切换
4. 所有响应统一格式化处理
5. 使用中文注释