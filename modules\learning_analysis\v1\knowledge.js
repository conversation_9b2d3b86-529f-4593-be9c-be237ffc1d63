const qs = require('qs');
const URL = require('url');
const axios = require('axios');
const config = require('config');
const logger = require('../../utils/logger');
const ResponseWrapper = require('../../middlewares/response_wrapper');
const { isNumber } = require('mathjs');

const getAnalysisResult = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);

    // 根据条件搜索试卷
    let server = config.get('SE_KB_SERVER');
    let body = req.body
    body['tiku'] = true
    
    // 地区限制
    let pro_names = [];
    const provinces = body.provinces || [];
    provinces.forEach(element => {
        pro_names.push(element['name'])
    });
    if (pro_names.length != 0){
        body['province'] = pro_names.join(',')
    }
    delete body['provinces']
    body['limit'] = 2000
    body['offset'] = 0

    let es_url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: 'se_kb/v2/filter/exampapers',
        search: qs.stringify({
            api_key: server.appKey,
        })
    });

    try {
        // 获取试卷ID
        let exampapers = await axios.post(es_url, body);
        let paperIds = []
        if (exampapers.data && exampapers.data.code == 0 && exampapers.data.data){
            paperIds = exampapers.data.data.datas || [];
        }
        let period = body.period || ''
        let subject = body.subject || ''
        let grade = body.grade || ''
        let type = body.type || ''
        let to_year = body.to_year || ''
        let paperName = getPaperName(period, subject, grade, type, provinces, to_year);
        let analysisResult = {
            name: paperName,
            refer_paper_num: 0,
            know_num: 0,
            knowledges: []
        }
        if (!paperIds.length) {
            return responseWrapper.succ(analysisResult);
        }
        // 获取分析结果
        let kb_server = config.get('KB_API_SERVER');
        let kb_url = URL.format({
            protocol: kb_server.protocol,
            hostname: kb_server.hostname,
            port: kb_server.port,
            pathname: '/kb_api/v2/knowledges/analysis',
            search: qs.stringify({
                api_key: kb_server.appKey,
            })
        });
        // 获取区域分析结果
        let response = await axios.post(kb_url, {"paper_ids":paperIds});
        if (response.status === 200) {
            analysisResult = response.data;
        }
        // 获取中高考分析结果
        let {knowDict, realPaperNum, moniPaperNum}  = await realPaperAnalysis(period, subject, pro_names);
        let newKnowledges = []
        let knowledges = analysisResult.knowledges || [];
        for (let k=0; k<knowledges.length; k++){
            let knowId = knowledges[k].id;
            if (!isNumber(knowId)) {
                continue
            }
            let k_know_dict = knowledges[k];
            if (knowDict.hasOwnProperty(knowId)){
                k_know_dict['real_papers'] = knowDict[knowId]['real_papers']
                k_know_dict['real_ques_type'] = knowDict[knowId]['real_ques_type']
                k_know_dict['real_avg_diff'] = knowDict[knowId]['real_avg_diff']
                k_know_dict['real_exam_rate'] = knowDict[knowId]['real_exam_rate']
                k_know_dict['real_questions'] = knowDict[knowId]['real_questions']
                k_know_dict['real_ques_num'] = knowDict[knowId]['real_ques_num']
            }
            newKnowledges.push(k_know_dict)
        }
        analysisResult['knowledges'] = newKnowledges
        analysisResult['name'] = paperName
        analysisResult['refer_real_paper_num'] = realPaperNum
        analysisResult['refer_moni_paper_num'] = moniPaperNum
        return responseWrapper.succ(analysisResult)
    } catch (err) {
        logger.error(err);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
}

const realPaperAnalysis = async (period, subject, pro_names) => {
    // 根据条件搜索试卷
    let currentYear = new Date().getFullYear()
    let server = config.get('SE_KB_SERVER');
    let realBody = {
        'offset': 0,
        'tiku': true,
        'limit': 2000,
        'period': period,
        'subject': subject,
        'to_year': `${currentYear+1},${currentYear},${currentYear-1}`
    }
    let moniBody = {
        'offset': 0,
        'tiku': true,
        'limit': 5000,
        'period': period,
        'subject': subject,
        'to_year': `${currentYear+1},${currentYear},${currentYear-1}`
    }
    moniBody['type'] = '高考模拟'
    realBody['type'] = '高考真卷'
    if (period == '初中') {
        moniBody['type'] = '中考模拟'
        realBody['type'] = '中考真卷'
        if (pro_names.length != 0){
            moniBody['province'] = pro_names.join(',')
            realBody['province'] = pro_names.join(',')
        }
    } else if (period == '小学') {
        moniBody['type'] = '小升初模拟'
        realBody['type'] = '小升初真卷'
        if (pro_names.length != 0){
            moniBody['province'] = pro_names.join(',')
            realBody['province'] = pro_names.join(',')
        }
    }

    let es_url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: 'se_kb/v2/filter/exampapers',
        search: qs.stringify({
            api_key: server.appKey,
        })
    });

    let realPaperNum = 0
    let moniPaperNum = 0
    let knowDict = {}
    try {
        let paperIds = []
        // 获取真卷
        let realExampapers = await axios.post(es_url, realBody);
        if (realExampapers.data && realExampapers.data.code == 0 && realExampapers.data.data){
            let realPaperIds = realExampapers.data.data.datas || [];
            realPaperIds.forEach(real_id => {
                paperIds.push(real_id)
            })
            realPaperNum = realPaperIds.length
        }
        // 获取模拟卷
        let moniExampapers = await axios.post(es_url, moniBody);
        if (moniExampapers.data && moniExampapers.data.code == 0 && moniExampapers.data.data){
            let moniPaperIds = moniExampapers.data.data.datas || [];
            moniPaperIds.forEach(moni_id => {
                paperIds.push(moni_id)
            })
            moniPaperNum = moniPaperIds.length
        }
        
        if (!paperIds.length) {
            return {knowDict, realPaperNum, moniPaperNum}
        }

        // 获取分析结果
        let analysisResult = {}
        let kb_server = config.get('KB_API_SERVER');
        let kb_url = URL.format({
            protocol: kb_server.protocol,
            hostname: kb_server.hostname,
            port: kb_server.port,
            pathname: '/kb_api/v2/knowledges/analysis',
            search: qs.stringify({
                api_key: kb_server.appKey,
            })
        });
        let response = await axios.post(kb_url, {"paper_ids":paperIds, "from_limit":["ai_organize_paper"]});
        if (response.status === 200) {
            analysisResult = response.data;
        }
        // 获取知识点及其分析结果
        let knowledges = analysisResult.knowledges || [];
        knowledges.forEach( know => {
            let know_id = know.id;
            knowDict[know_id] = {
                "real_papers": know.papers || [],
                "real_ques_type": know.ques_type || [],
                "real_questions": know.questions || [],
                "real_ques_num": know.ques_num || 0,
                "real_exam_rate": know.exam_rate || 0,
                "real_avg_diff": know.avg_diff || 0.5
            }
        })
        return {knowDict, realPaperNum, moniPaperNum}
    } catch (err) {
        logger.error(err);
        return {knowDict, realPaperNum, moniPaperNum}
    }

}

const getPaperName = (period, subject, grade, type, provinces, to_year) => {
    // 省份名字
    let provinceName = ''
    let provinceNames = []
    provinces.forEach(element => {
        if (provinceNames.length < 2){
            provinceNames.push(element.name)
        }
    })
    if (provinceNames.length == 1){
        provinceName = `${provinceNames[0]}地区`
    } else if (provinceNames.length == 2){
        provinceName = `${provinceNames[0]}、${provinceNames[1]}等地区`
    }
    // 年份
    to_year = to_year.replace(/,/g, '、')

    // 年级
    grade = grade.replace(/,/g, '、')

    // 类型
    type = type.replace(/,/g, '、')
    let paperName = `${provinceName}${to_year}学年${period}${grade}学期${subject}${type}考情分析`
    return paperName
}

module.exports={
    getAnalysisResult
}