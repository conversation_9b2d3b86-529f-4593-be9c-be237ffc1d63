const express = require('express');
const router = express.Router();
const payments = require('../../modules/payments_api/v1/index');
router.post('/wechat-pay/notification/qr', payments.postNotificationQrV2);
router.post('/wechat-pay/notification/refund', payments.postRefundNotification);
// H5支付通告
router.post('/wechat-pay/notification/h5', payments.postNotificationH5);
// APP支付通知
router.post('/wechat-pay/notification/app', payments.postNotificationApp);


// function route(app) {

//     // 提交支付回调 微信支付 QR
//     router.post('/wechat-pay/notification/qr', payments.postNotificationQr);
//     router.post('/wechat-pay/notification/refund', payments.postRefundNotification);

//     // notification
//     app.use('/payments_api/v1', router);

// }

module.exports = router;
