const _ = require('lodash');
const ObjectId = require("mongodb").ObjectId;
const mongodber = require('../../utils/mongodber');
const db = mongodber.use('tiku');
const ResponseWrapper = require('../../middlewares/response_wrapper');
const logger = require('../../utils/logger');
const enums = require('../../../bin/enum');
const Joi = require('@hapi/joi');
const client = require('../../client');
const constants = require('../../utils/constants');
const edu_tool = require('../../edu_assistant_tool_api/v1');

const collection_topic_page = db.collection(constants.schema.topic_page); // 专题落地页
const collection_topic = db.collection(constants.schema.topic); // 专题
const collection_topic_data = db.collection(constants.schema.topic_data); // 专题数据

module.exports = {
    getInfo,
    searchData,
}

async function getInfo(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const id = req.params.id;
        if (!id) return responseWrapper.error('PARAMETERS_ERROR', '参数错误');
        const page = await getTopicPage(id);
        let result = {};
        if (_.isEmpty(page)) return responseWrapper.succ(result);
        const topics = await getTopicByIds(page.topics);
        result = {
            id: page._id.toString(),
            template: page.template, // 页面模板
            template_data: page.template_data, // 模板数据
            ctime: page.ctime.getTime(),
            topics: topics.map(topic => {
                return {
                    id: topic._id.toString(),
                    name: topic.name,
                    title: topic.title,
                    subtitle: topic.subtitle,
                    description: topic.description,
                    search_items: topic.search_items,
                }
            })
        };
        return responseWrapper.succ(result);
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

async function searchData(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const id = req.params.id;
        const { offset = 0, limit = 10 } = req.body;
        if (req.body.offset) delete req.body.offset;
        if (req.body.limit) delete req.body.limit;
        if (!id) return responseWrapper.error('PARAMETERS_ERROR', '参数错误');
        const page = await getTopicPage(id);
        if (_.isEmpty(page)) return responseWrapper.error('NULL_ERROR', '专题数据不存在或已失效');
        const cond = {
            topic_id: {$in: page.topics},
            valid: enums.BooleanNumber.YES
        };
        const topics = await getTopicByIds(page.topics);
        const searchMap = new Map()
        for (const topic of topics) {
            for (const item of topic.search_items || []) {
                searchMap.set(item.key, item);
            }
        }
        for (const key of Object.keys(req.body || {})) {
            let value = _.get(req.body, key, '');
            if (!value || value === '全部' || !searchMap.has(key)) continue;
            const item = searchMap.get(key);
            if (item.value_type === 'tree') {
                const values = get_tree_values(value, item.value);
                cond[`resource_info.${key}`] = {$in: values};
            } else {
                if (item.value_type === 'number') {
                    value = Number(value);
                }
                cond[`resource_info.${key}`] = value;
            }

        }

        const result = {
            total: 0,
            list: []
        };
        const total = await collection_topic_data.find(cond).count();
        if (total) {
            result.total = total;
            const list = await collection_topic_data.find(cond).sort({'resource_info.to_year': -1, 'resource_info.ctime': -1}).skip(offset).limit(limit).toArray();
            const resource_papers = list.filter(e => e.resource_type === enums.TopicResourceType.EXAMPAPER); // 试卷特殊处理
            let papers = [];
            const data_list = [];
            if (_.size(resource_papers)) {
                papers = await client.kb.getExampapers(list.map(e => e.resource_id));
            }
            let tools_list = [];
            const edu_tools_list = list.filter(e => e.resource_type === enums.TopicResourceType.EDU_TOOL);
            if (_.size(edu_tools_list)) {
                tools_list = await edu_tool.getByIds(edu_tools_list.map(e => e.resource_id));
            }
            result.list = list.map(e => {
                let resource_data = e.resource_info;
                if (e.resource_type === enums.TopicResourceType.EXAMPAPER) {
                    resource_data = papers.find(p => p.id === e.resource_id) || resource_data;
                } else if (e.resource_type === enums.TopicResourceType.EDU_TOOL) {
                    resource_data = tools_list.find(p => p.id === e.resource_id) || resource_data;
                }
                return {
                    topic_id: e.topic_id,
                    resource_id: e.resource_id,
                    resource_type: e.resource_type,
                    resource_name: e.resource_name || '',
                    resource_image: e.resource_image || '',
                    resource_data: resource_data,
                    ctime: e.ctime.getTime()
                }
            });
        }
        return responseWrapper.succ(result);
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

function get_tree_values(value, tree) {
    const values = [];
    function traverse(node, is_child = false) {
        if(!node || !node.children) return;
        for (const child of node.children) {
            if (child.id === value || is_child) {
                values.push(child.id);
                traverse(child, true)
            }
        }
    }
    values.push(value);
    for (const item of tree) {
        let is_child = false;
        if (item.id === value) {
            values.push(item.id);
            is_child = true;
        }
        traverse(item, is_child);
    }
    return values;
}

async function getTopicPage(id) {
    const data = await collection_topic_page.findOne({_id: new ObjectId(id)});
    if (_.isEmpty(data) || data.valid !== 1) {
        return null;
    }
    return data;
}

async function getTopicByIds(ids) {
    if (!_.size(ids)) return [];
    return await collection_topic.find({_id: {$in: ids.map(e => new ObjectId(e))}, valid: enums.BooleanNumber.YES }).toArray();
}
