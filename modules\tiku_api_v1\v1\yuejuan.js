
let mongodber = require('../../utils/mongodber');
let ResponseWrapper = require('../../middlewares/response_wrapper');
let logger = require('../../utils/logger');
let _ = require('underscore');
let db = mongodber.use('tiku');

const schollCollection = db.collection('school_info');

function checkData(value) {
    let data = {};
    if (value.school_name) {
        data.name = value.school_name;
    }
    if (value.school_type) {
        data.type = value.school_type;
    }
    if (value.yj_ctime) {
        data.yj_ctime = value.yj_ctime;
    }
    if (value.yj_utime) {
        data.yj_utime = value.yj_utime;
    }
    return data;
}

const postSchoolInfo = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    let addData = req.body.add;
    let delData = req.body.del;
    let updateData = req.body.update;
    try {
        let data;
        if (addData && addData.length > 0) {
            for (const value of addData) {
                data = checkData(value);
                data.ctime = new Date();
                if (!value.school_id) {
                    logger.error(value);
                    return responseWrapper.error('HANDLE_ERROR', '学校id不能为空');
                }
                data._id = value.school_id;
                await schollCollection.insertOne(data);
            }
            data = {};
        }
        if (delData && delData.length > 0) {
            for (const value of delData) {
                data.yj_utime = value.yj_utime || '';
                data.valid = false;
                data.utime = new Date();
                if (!value.school_id) {
                    logger.error(value);
                    return responseWrapper.error('HANDLE_ERROR', '学校id不能为空');
                }
                await schollCollection.updateOne({ _id: value.school_id }, { '$set': data });
            }
            data = {};
        }
        if (updateData && updateData.length > 0) {
            for (const value of updateData) {
                data = checkData(value);
                data.utime = new Date();
                if (!value.school_id) {
                    logger.error(value);
                    return responseWrapper.error('HANDLE_ERROR', '学校id不能为空');
                }
                let result = await schollCollection.findOne({ _id: value.school_id });
                if (result.is_edit_type) {   // true
                    delete data.type;
                }
                await schollCollection.updateOne({ _id: value.school_id }, { '$set': data });
            }
        }
        return responseWrapper.succ({});
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

module.exports = {
    postSchoolInfo: postSchoolInfo,
};