/**
 * Created by zxb on 8/7/15.
 */
var log4js = require('log4js');
var _ = require('lodash');

var logger = log4js.getLogger('info');
var loggerError = log4js.getLogger("error");
// var axios = require('axios');
var sender = null;
var conf = null;

var DEFAULT_FORMAT = ':remote-addr - -' +
    ' ":method uri[:url] HTTP/:http-version"' +
    ' :status :content-length' +
	' :response-time';

function init(config) {
    config = _.cloneDeep(config);
    // if (config.logfaces) {
    //     conf = config.logfaces;
    //     sender = axios.create({
    //         baseURL: config.logfaces.url,
    //         timeout: config.logfaces.timeout || 5000,
    //         headers: {'Content-Type': 'application/json'},
    //         withCredentials: true
    //     });
    //     delete config.logfaces;
    // }

    log4js.configure(config);
    return log4js.connectLogger(log4js.getLogger('error'), {
        level: log4js.levels.INFO,
        format: DEFAULT_FORMAT
    });
}

function info(value) {
    logger.info(value);
    if (sender) {
        let lfsEvent = {
            app: conf.application,
            level: 'INFO',
            time: new Date(),
            message: value
        };
        sender.post('', lfsEvent)
            .catch((error) => {
                if (error.response) {
                    console.error(`log4js.logFaces-HTTP Appender error posting to ${conf.url}: ${error.response.status} - ${error.response.data}`); //eslint-disable-line
                    return;
                }
                console.error(`log4js.logFaces-HTTP Appender error: ${error.message}`); //eslint-disable-line
            });
    }
}

function error(value) {
    let str = '';
    if (typeof(value) === 'object' && !_.isEmpty(value.stack)) {
        str = value.stack;
    } else {
        str = value;
    }
    loggerError.error(str);
    if (sender) {
        let lfsEvent = {
            app: conf.application,
            level: 'ERROR',
            time: new Date(),
            message: str
        };
        sender.post('', lfsEvent)
            .catch((error) => {
                if (error.response) {
                    console.error(`log4js.logFaces-HTTP Appender error posting to ${conf.url}: ${error.response.status} - ${error.response.data}`); //eslint-disable-line
                    return;
                }
                console.error(`log4js.logFaces-HTTP Appender error: ${error.message}`); //eslint-disable-line
            });
    }
}

function warn(value) {
    if (typeof value === 'object' && value != null) {
        logger.warn(JSON.stringify(value));
    } else {
        logger.warn(value);
    }
    if (sender) {
        let lfsEvent = {
            app: conf.application,
            level: 'WARN',
            time: new Date(),
            message: value
        };
        sender.post('', lfsEvent)
            .catch((error) => {
                if (error.response) {
                    console.error(`log4js.logFaces-HTTP Appender error posting to ${conf.url}: ${error.response.status} - ${error.response.data}`); //eslint-disable-line
                    return;
                }
                console.error(`log4js.logFaces-HTTP Appender error: ${error.message}`); //eslint-disable-line
            });
    }
}


module.exports = {
    init: init,
    info: info,
    error: error,
    warn: warn
};
