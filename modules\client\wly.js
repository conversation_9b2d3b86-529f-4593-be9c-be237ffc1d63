const _ = require('lodash');
const URL = require('url');
const config = require('config');
const axios = require('axios');
const logger = require('../utils/logger');
const server = config.get('WLY_SERVER');
const qs = require('querystring');
const options = { headers: {
        'access-key': server['access-key']
    },
    timeout: 50000 };
const rediser = require('../utils/rediser');
const Payment = require('../utils/wx_pay');

const cache_key = `tiku:pay:wechat:config:${process.env.NODE_ENV}`;

module.exports = {
    getPayment,
    getWechatPayConfig,
    getPaymentByMchId,
}

async function getPayment(refund = false) {
    const array = await getWechatPayConfig();
    if (!_.size(array)) return null;
    // 发起支付
    const pay_config = array[0];
    return buildPayment(pay_config, refund);
}

async function getPaymentByMchId(mch_id, refund = false) {
    const array = await getWechatPayConfig();
    if (!_.size(array)) return null;
    // 发起支付
    const pay_config = array.find(e => e.merchantId === mch_id);
    return buildPayment(pay_config, refund);
}

function buildPayment(pay_config, refund = false) {
    if (_.isEmpty(pay_config)) return null;
    const mch_id = _.get(pay_config, 'merchantId');
    return new Payment({
        mchid: _.get(pay_config, 'merchantId'),
        appid: _.get(pay_config, 'appId'),
        private_key: _.get(pay_config, 'private_key'),
        serial_no: _.get(pay_config, 'serial_no'),
        apiv3_private_key: _.get(pay_config, 'apiKey'),
        notify_url: refund ? config.get(`payment.wechatPay.${mch_id}.refund_notify_url`) : config.get(`payment.wechatPay.${mch_id}.notify_url`),
    });
}

/**
 * 获取微信支付配置
 * @returns {Promise<null|any>}
 */
async function getWechatPayConfig() {
    const data = await rediser.redis.get(cache_key);
    if (data) {
        return JSON.parse(data);
    }
    let url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        pathname: '/pay-wechat/publicFind',
        port: server.port
    });
    try {
        let response = await axios.get(url, options);
        if (response.status !== 200) {
            logger.error(`获取微信支付配置失败：url: ${url}, status: ${response.statusText}`);
            return null;
        }
        if (!response.data || !_.size(response.data)) {
            logger.error(`获取微信支付配置失败：url: ${url}`);
            return null;
        }
        // const result = response && _.size(response.data) && response.data[0] || null;
        // if (_.isEmpty(result)) {
        //     logger.error(`获取微信支付配置失败：url: ${url}`);
        //     return result;
        // }
        const result = [];
        for (const info of response.data) {
            const private_key = await _get_config_private_key(info);
            if (!private_key) {
                return null;
            }
            info.private_key = private_key;
            result.push(info);
        }

        // const private_key = await _get_config_private_key(result);
        // if (!private_key) {
        //     return null;
        // }
        // result.private_key = private_key;
        rediser.redis.setex(cache_key, 10 * 60, JSON.stringify(result));
        return result;
    } catch (e) {
        logger.error(e);
        return null;
    }
}

async function _get_config_private_key(data) {
    const file_url = _.get(data, 'keyPem.url');
    try {
        const response = await axios.get(file_url);
        if (response && response.status !== 200) {
            logger.error(`获取支付[private_key]失败：url:${file_url}, statusText: ${response.statusText}`);
        }
        return response.data || '';
    } catch (e) {
        logger.error(e);
        return '';
    }

}
