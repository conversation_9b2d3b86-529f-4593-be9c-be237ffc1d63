/**
 * 生涯规划
 * wiki: https://wiki.iyunxiao.com/pages/viewpage.action?pageId=3509545
 */

const _ = require('lodash');
const URL = require('url');
const config = require('config');
const axios = require('axios');
const logger = require('../utils/logger');
const SERVER = config.get('SY_SERVER');
const qs = require('querystring');
const utils = require('../utils/utils');

module.exports = {
    getOptions,
    total,
    search,
    getPackageDetail,
    addVisit,
    getVideo
};

/**
 * 获取分类
 * @returns {Promise<GetFieldType<AxiosResponse<any>, string>>}
 */
async function getOptions() {
    let url = URL.format({
        protocol: SERVER.protocol,
        hostname: SERVER.hostname,
        pathname: `/api/v3/micro-course/pack/options`,
        port: SERVER.port
    });
    let result = await axios.get(url, _get_req_options());
    if (!result.data || result.data.code !== 0) {
        logger.error(`生涯获取筛选项数据失败,url:[${url}]`);
    }
    return _.get(result, 'data.data', null);
}

/**
 * 查询结果数量
 * @returns
 */
async function total(params) {
    let url = URL.format({
        protocol: SERVER.protocol,
        hostname: SERVER.hostname,
        pathname: `/api/v3/micro-course/search/total`,
        port: SERVER.port,
        search: qs.stringify(params)
    });
    let result = await axios.get(url, _get_req_options());
    if (!result.data || result.data.code !== 0) {
        logger.error(`生涯查询结果数量失败,url:[${url}]`);
    }
    return _.get(result, 'data.data', null);
}

/**
 * 查询结果 - 课程
 * @returns
 */
async function search(params) {
    let url = URL.format({
        protocol: SERVER.protocol,
        hostname: SERVER.hostname,
        pathname: `/api/v3/micro-course/search`,
        port: SERVER.port,
        search: qs.stringify(params)
    });
    let result = await axios.get(url, _get_req_options());
    if (!result.data || result.data.code !== 0) {
        logger.error(`生涯查询结果数量失败,url:[${url}]`);
    }
    return _.get(result, 'data.data', null);
}

/**
 * 课程包详情
 * @param pack_id
 * @returns
 */
async function getPackageDetail(pack_id) {
    let url = URL.format({
        protocol: SERVER.protocol,
        hostname: SERVER.hostname,
        pathname: `/api/v3/micro-course/pack`,
        port: SERVER.port,
        search: qs.stringify({
            pack_id: pack_id
        })
    });
    let result = await axios.get(url, _get_req_options());
    if (!result.data || result.data.code !== 0) {
        logger.error(`生涯查询结果数量失败,url:[${url}]`);
    }
    return _.get(result, 'data.data', null);
}

/**
 * 增加浏览量
 * @param pack_id
 * @returns
 */
async function addVisit(pack_id) {
    let url = URL.format({
        protocol: SERVER.protocol,
        hostname: SERVER.hostname,
        pathname: `/api/v3/micro-course/pack/visit`,
        port: SERVER.port,
    });
    const body = {
        pack_id
    };
    let result = await axios.post(url, body, _get_req_options());
    if (!result.data || result.data.code !== 0) {
        logger.error(`生涯查询结果数量失败,url:[${url}]`);
    }
    return _.get(result, 'data.data', null);
}

/**
 * 获取视频播放地址
 * @param section_id
 * @param isApp
 * @returns
 */
async function getVideo(section_id, isApp = 1) {
    let url = URL.format({
        protocol: SERVER.protocol,
        hostname: SERVER.hostname,
        pathname: `/api/v3/micro-course/section/video`,
        port: SERVER.port,
        search: qs.stringify({
            section_id,
            isApp,
            domain: 'sygh.yunxiao.com'
        })
    });
    let result = await axios.get(url, _get_req_options());
    if (!result.data || result.data.code !== 0) {
        logger.error(`生涯获取视频播放地址失败,url:[${url}]`);
    }
    return _.get(result, 'data.data', null);
}

function _get_req_options() {
    return {
        headers: {
            'Content-Type': 'application/json',
            'ak': SERVER.ak,
            'sk': SERVER.sk
        },
        timeout: 2000
    };
}
