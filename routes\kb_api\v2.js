/*
 * Desc: the route of kb api 
 * Author: guochanghui
 */

var router = require('express').Router();
var apikey = require('../../modules/middlewares/apikey');
var transmiter = require('../../modules/kb_api/v2/transmiter');
var downloader = require('../../modules/kb_api/v2/downloader');
//cconst freqMiddleware = require('../../modules/middlewares/auth').freqMiddleware;
const stopFreqRequest = require('../../modules/middlewares/auth').stopFreqRequest;

// kb api key middleware
router.use(apikey('KB'));

router.get('/exampapers/:exampaper_id/download', downloader.downloadExampaper);
router.post('/assemble/exampaper/download', stopFreqRequest, downloader.assembleExampaper);
router.get('/docx/:filename', downloader.downloadDocx);
// router.get('/exampapers/:exampaper_id/', downloader.getExampaperInfo);
// router.get('/questions/filters/', downloader.questionsFilters)
// router.post('/questions/by_search/', downloader.bySearch)
// router.post('/exampapers/by_search/', downloader.bySearch)

router.get('/questions/:question_id/', downloader.getQuestionsById);
router.get('/questions/:question_id/answer', downloader.getQuestionAnswerById);
router.get('/questions/:question_id/knowledge', downloader.getQuestionsKnowledgeById);


router.post('/download/info', stopFreqRequest, downloader.getDownloadInfo);
router.post('/download', downloader.getDownload);

// 模糊搜索知识点，并获取管理教材，好分数错题工具使用
router.get('/knowledge/by_search/', downloader.getKnowledgeBbySearch)

router.all('*', transmiter.transmit);

module.exports = router;
