const _ = require('lodash');
const URL = require('url');
const config = require('config');
const axios = require('axios');
const logger = require('../utils/logger');
const server = config.get('SSO_SERVER');
const utils = require('../utils/utils');
const qs = require('querystring');

module.exports = {
    getLoginInfo,
    getUserInfo,
}

/**
 * 获取用户信息
 * @param app
 * @param cert_app
 * @param user
 * @param passwd
 * @returns {Promise<null|any>}
 */
async function getLoginInfo({ app, cert_app, user, passwd }) {
    try {
        let url = URL.format({
            protocol: server.protocol,
            hostname: server.hostname,
            pathname: '/passport/v1/user/login_info/',
            port: server.port,
        });
        let options = { headers: { 'Content-Type': 'application/json', }, timeout: 50000 };
        let result = await axios.post(url, { app, cert_app, user, passwd }, options);
        if (!result || result.status !== 200 || !result.data) {
            logger.error(`KB-APPS登录失败: 
            url: ${url}, 
            status: ${result.status}, 
            statusText: ${result.statusText} params: ${JSON.stringify({ app, cert_app, user, passwd })}`);
        }
        return result.data && result.data.data || null;
    } catch (e) {
        logger.error(`getLoginInfo error`);
        logger.error(e);
        return null;
    }
}

/**
 * 获取用户信息
 * @param token 令牌信息
 * @returns {Promise<null|any>}
 */
async function getUserInfo(token) {
    try {
        const url = URL.format({
            protocol: server.protocol,
            hostname: server.hostname,
            port: server.port,
            pathname: '/passport/v1/user/info/'
        });
        let options = { headers: { 'Content-Type': 'application/json', }, timeout: 50000 };
        const result = await axios.post(url, { token: token }, options);
        if (!result || result.status !== 200 || !result.data) {
            logger.error(`KB-APPS获取用户信息失败: 
            url: ${url}, 
            status: ${result.status}, 
            statusText: ${result.statusText} params: ${token}`);
        }
        return result.data && result.data.data || null;
    } catch (e) {
        logger.error(e);
        return null;
    }
}
