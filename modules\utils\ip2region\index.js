const Searcher = require('./ip2region');
const dbPath = `${__dirname}/ip2region.xdb`;
const searcher = Searcher.newWithFileOnly(dbPath);

/**
 * 根据IP地址查询地区信息
 * { region: '中国|0|上海|上海市|联通', ioCount: 9, took: 1384.256 }
 * @param ip
 * @returns {Promise<{took: number, ioCount: number, region: null}>}
 */
async function searchRegionByIp (ip) {
    const data = await searcher.search(ip);
    return data;
}

module.exports = {
    searchRegionByIp
}


