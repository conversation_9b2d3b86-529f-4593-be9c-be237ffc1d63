const mongodber = require('../../utils/mongodber');
const ResponseWrapper = require('../../middlewares/response_wrapper');
const logger = require('../../utils/logger');
const enums = require('../../../bin/enum');
const db = mongodber.use('tiku');
const db_jzl = mongodber.use('jzl_jiaoyan');
const db_open = mongodber.use('tiku_open');
const ObjectId = require('mongodb').ObjectId;
const config = require('config');
const Joi = require('@hapi/joi');
const URL = require('url');
const axios = require('axios');
const _ = require('lodash');
const util = require('util');
const utils = require('../../utils/utils');
const extend_ques = require('../../assemble_api/v1/extend_ques');
const basket = require('../../assemble_api/v1/basket');
const paper_utils = require('../../utils/paper_utils');

const getExampaperListSchema = Joi.object({
    subject: Joi.string().optional(),
    grade: Joi.string().optional(),
    type: Joi.string().optional(),
    limit: Joi.number().default(10).optional(),
    offset: Joi.number().default(0).optional(),
}).unknown(true);

const getExampaperList = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let { subject, grade, type, limit, offset } = await Joi.validate(req.query, getExampaperListSchema);
        const period = enums.GradePhaseMap[grade];
        let cond = { user_id: req.user.id, is_delete: { $ne: true } };
        if (type) cond.type = type;
        if (subject) cond.subject = subject;
        if (period) cond.period = period;
        let total = await db.collection('upload_exampaper').count(cond);
        let data = await db.collection('upload_exampaper').find(cond, {
            ctime: 1,
            utime: 1,
            _id: 1,
            name: 1,
            type: 1,
            grade: 1,
            status: 1,
            err_msg: 1,
        }).sort({ ctime: -1 }).skip(offset).limit(limit).toArray();

        let resObj = {
            total: total,
            list: data.map(item => {
                return {
                    ctime: item.ctime && item.ctime.getTime(),
                    utime: item.utime && item.utime.getTime(),

                    id: item._id.toString(),
                    name: item.name,
                    type: item.type,
                    grade: item.grade,
                    status: item.status,
                    err_msg: item.err_msg,
                }
            })
        };

        return responseWrapper.succ(resObj);
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

const getExampaperInfoSchema = Joi.object({
    id: Joi.string().required(),
}).unknown(true);

const getExampaperInfo = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const { id } = await Joi.validate(req.params, getExampaperInfoSchema);

        const recordInfo = await db_open.collection(enums.OpenSchema.user_paper).findOne({ _id: ObjectId(id) });

        if (!recordInfo) {
            throw new Error('id错误');
        }
        if (recordInfo.user_id !== utils.formatUserId(req.user.id)) {
            throw new Error('id错误');
        }
        // delete recordInfo._id;
        await extend_ques.extend_ques_async(recordInfo, 'zx');
        // await util.promisify(extend_ques.extend_ques)(recordInfo);
        delete recordInfo._id;
        return responseWrapper.succ(recordInfo);
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};


const postExampaperByWordSchema = Joi.object({
    word_url: Joi.string().required(),
    exampaper_name: Joi.string().required(),
    subject: Joi.string().required(),
    grade: Joi.string().required(),
    type: Joi.string().required(),
}).unknown(true);

const postExampaperByWord = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let { word_url, type, exampaper_name, subject, grade } = await Joi.validate(req.body, postExampaperByWordSchema);
        const period = enums.GradePhaseMap[grade];
        let exam = {
            user_id: req.user.id,
            word_url: word_url,
            name: exampaper_name,
            period: period,
            subject: subject,
            grade: grade,
            type: type,
            subtitle: '',
            score: 0,
            duration: 0,
            paper_info: '',
            cand_info: '',
            score_info: '',
            attentions: '',
            secret_tag: '',
            gutter: 0,
            volumes: [{
                blocks: [],
                note: '',
                title: '卷I（选择题）',
            }, {
                blocks: [],
                note: '',
                title: '卷II（非选择题）',
            },],
            status: enums.UploadExampaperStatus.PROCESSING,
            // 默认只使用 分卷和大题注释
            partsList: ['volumes', 'blocks'],
            ctime: new Date(),
            utime: new Date(),
        };
        let exampaperResult = await db.collection('upload_exampaper').insertOne(exam);
        const exampaperId = exampaperResult.insertedId.toString();
        _wordUploadExampaper({ word_url, type, exampaper_name, subject, grade, period, id: exampaperId, user_id: req.user.id, });

        return responseWrapper.succ({
            id: exampaperId
        });
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

const putExampaperById = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {

        let user_id = req.user.id;
        let exampaper_id = req.params.id;
        let exampaper = req.body;

        delete exampaper['_id'];
        exampaper['user_id'] = user_id;
        exampaper['utime'] = new Date();
        if (exampaper.hasOwnProperty('ctime')) {
            delete exampaper['ctime']
        }
        if (exampaper.hasOwnProperty('word_url')) {
            delete exampaper['word_url']
        }
        if (exampaper.hasOwnProperty('status')) {
            delete exampaper['status']
        }
        //对试卷内部数据进行处理
        exampaper = _handleExampaper(exampaper);
        let filter = {
            _id: new ObjectId(exampaper_id),
            user_id: user_id,
        };

        let exampapers = await db.collection('upload_exampaper').find(filter).toArray();
        if (exampapers.length > 0) {
            await db.collection('upload_exampaper').updateOne(filter, { $set: exampaper });
        } else {
            return responseWrapper.error('NULL_ERROR');
        }
        return responseWrapper.succ({});
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

const batchModifyQuestionTypeSchema = Joi.object({
    q_ids: Joi.array().items(Joi.string().length(24)).required(),
    exam_id: Joi.string().required(),
    type: Joi.string().required(),
}).unknown(true);

const batchModifyQuestionType = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let { q_ids, type, exam_id } = await Joi.validate(req.body, batchModifyQuestionTypeSchema);

        let user_id = req.user.id;

        await db.collection('upload_questions').updateMany({
            _id: { $in: q_ids.map(i => new ObjectId(i)) },
            exampaper_id: exam_id,
        }, {
            $set: {
                type: type,
                utime: new Date(),
            }
        });

        let emam = await db.collection('upload_exampaper').findOne({
            _id: new ObjectId(exam_id),
            user_id: user_id,
        });
        if (!emam) return responseWrapper.error('NULL_ERROR');

        let questionArr = [];
        for (let volume of emam.volumes) {
            for (let block of volume.blocks) {
                for (let question of block.questions) {
                    if (q_ids.includes(question.id)) {
                        question.type = type;
                        questionArr.push(question);
                    }
                }
            }
        }
        basket._delete_questions(emam, q_ids);
        for (let ques of questionArr) {
            basket._insert_questions(emam, ques);
        }

        await db.collection('upload_exampaper').updateOne({
            _id: new ObjectId(exam_id),
        }, {
            $set: {
                volumes: emam.volumes,
                utime: new Date()
            }
        });
        await util.promisify(extend_ques.extend_ques)(emam);
        delete emam._id;

        return responseWrapper.succ(emam);
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

const putQuestionById = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {

        let userId = req.user.id;
        let questionId = req.params.id;
        let question = req.body;

        delete question['_id'];
        question['user_id'] = userId;
        question['utime'] = new Date();
        if (question.hasOwnProperty('ctime')) {
            delete question['ctime']
        }
        if (question.hasOwnProperty('from')) {
            delete question['from']
        }
        if (question.hasOwnProperty('exampaper_id')) {
            delete question['exampaper_id']
        }
        //对试卷内部数据进行处理
        let filter = {
            _id: new ObjectId(questionId),
            user_id: userId,
        };

        let questions = await db.collection('upload_questions').find(filter).toArray();
        if (questions.length > 0) {
            await db.collection('upload_questions').updateOne(filter, { $set: question });
        } else {
            return responseWrapper.error('NULL_ERROR');
        }
        return responseWrapper.succ({});
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

const deleteExampaperById = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let user_id = req.user.id;
        let exampaper_id = req.params.id;

        let filter = {
            _id: new ObjectId(exampaper_id),
            user_id: user_id,
        };
        let exampapers = await db.collection('upload_exampaper').find(filter).toArray();
        if (exampapers.length > 0 && exampapers[0].status !== enums.UploadExampaperStatus.PROCESSING) {
            await db.collection('upload_exampaper').updateOne({ _id: new ObjectId(exampaper_id) }, { $set: { is_delete: true, } });
            await db.collection('upload_questions').updateMany({ exampaper_id: exampaper_id, }, { $set: { is_delete: true, } });
        } else if (exampapers[0].status === enums.UploadExampaperStatus.PROCESSING) {
            return responseWrapper.error('HANDLE_ERROR', '解析中的上传不允许删除');
        } else {
            return responseWrapper.error('NULL_ERROR');
        }
        return responseWrapper.succ({});
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

// helper
const _wordUploadExampaper = async (data) => {
    let { word_url, exampaper_name, subject, grade, period, id, user_id } = data;
    let errMsg = '';
    try {
        let wordParserUrl = URL.format({
            protocol: config.get('WORD_PARSER_SERVER').protocol,
            hostname: config.get('WORD_PARSER_SERVER').hostname,
            pathname: '/word/tiku_word_parse',
            port: config.get('WORD_PARSER_SERVER').port,
        });
        let opstions = { timeout: 600000 };
        let wordResult = await axios.post(wordParserUrl, {
            period: period,
            subject: subject,
            exampaper_name: exampaper_name,
            word_url: word_url,
        }, opstions);

        if (wordResult.data.code !== 0) {
            errMsg = wordResult.data.msg && _.join(_.initial(_.split(wordResult.data.msg, '::')), '::') || '';
            // errMsg = wordResult.data.msg && wordResult.data.msg.slice(0,35) || '';
            throw new Error(`code:${wordResult.data.code}, msg: ${wordResult.data.msg}`);
        }

        const word = wordResult.data.data;
        let emam = {
            volumes: [{
                blocks: [],
                note: '',
                title: '卷I（选择题）',
            }, {
                blocks: [],
                note: '',
                title: '卷II（非选择题）',
            }]
        };
        for (let volume of word.volumes) {
            for (let block of volume.blocks) {
                let curQuestions = [];

                for (let question of block.questions) {
                    // let curType = question.blocks && question.blocks.types && question.blocks.types[0];
                    question.blocks = _.pick(question.blocks, ['types', 'explanations', 'solutions', 'answers', 'stems', 'knowledges'])
                    question = _.pick(question, ['subject', 'period', 'description', 'comment', 'blocks', 'knowledges', 'difficulty', 'type', 'ctime', 'utime'])
                    question._id = new ObjectId();
                    question.subject = subject;
                    question.period = period;
                    // question.type = curType;
                    question.from = 'tiku';
                    question.user_id = user_id;
                    question.exampaper_id = id;
                    curQuestions.push(question);

                    question.id = question._id.toString();
                    basket._insert_questions(emam, question);
                }
                await db.collection('upload_questions').insertMany(curQuestions);
            }
        }
        for (let volume of emam.volumes) {
            for (let block of volume.blocks) {
                const questions = block.questions;
                const n = questions.length;
                if (questions.length <= 0) {
                    continue;
                }
                const s = Number(questions[0].score) || 0;
                let ts = Number(s);
                let tag = true;
                for (var i = 1; i < n; ++i) {
                    if (questions[i].score != questions[i - 1].score) {
                        tag = false;
                    }
                    ts += Number(questions[i].score) || 0;
                }
                const ss = tag ? `，每题${s}分` : '';
                const detail = `本大题共计${n}小题${ss}，共计${ts}分`;
                const note = (block.note && block.note.length > 0) ? `，${block.note}` : '';
                block.title = `${block.type}（${detail}${note}）`;
            }
        }
        // TODO 特殊处理8月底删除
        // if (emam.volumes && emam.volumes.blocks && emam.volumes.blocks.length > 0) {
        //     emam.volumes.blocks[0].title = emam.volumes.blocks[0].type || '选择题';
        // }

        await db.collection('upload_exampaper').updateOne({ _id: new ObjectId(id) }, {
            $set: {
                status: enums.UploadExampaperStatus.SUCCESS,
                volumes: emam.volumes
            }
        });
        return;
    } catch (err) {
        await db.collection('upload_exampaper').updateOne({ _id: new ObjectId(id) }, {
            $set: {
                status: enums.UploadExampaperStatus.FAILED,
                err_msg: errMsg,
            }
        });
        console.error(err.stack);
        throw err;
    }
}

function _handleExampaper(exampaper) {
    exampaper.score = 0;
    let volumes = exampaper.volumes;
    for (let v = 0; v < volumes.length; v++) {
        let volume = volumes[v];
        let blocks = volume.blocks;
        for (let b = 0; b < blocks.length; b++) {
            let block = blocks[b];
            block.default_score = Number(block.default_score);
            let questions = block.questions;
            if (questions.length > 0) {
                let ts = 0;
                for (let i = 0; i < questions.length; i++) {
                    questions[i].score = Number(questions[i].score);
                    ts += Number(questions[i].score);
                }
                exampaper.score += Number(ts);
            }
        }
    }

    return exampaper;
}

const JOI_GET_UPLOAD_LIST = Joi.object({
    // user_id: Joi.string().required(),
    period: Joi.string().optional(),
    subject: Joi.string().optional(),
    type: Joi.string().optional().allow(''),
    grade: Joi.string().optional().allow(''),
    year: Joi.number().optional(),
    offset: Joi.number().required(),
    limit: Joi.number().required(),
    status: Joi.string().optional().allow(''),
    // exam_status: Joi.string().optional().allow(''),
    // source: Joi.string().optional().allow(''),
    // sort_by: Joi.string().optional().allow('time'),
}).unknown(true);

async function getUploadList(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const {period, subject, type, grade, year, limit, offset, status} = await Joi.validate(req.query, JOI_GET_UPLOAD_LIST);
        const user_id = req.user.id;
        const open_user_id = utils.formatUserId(user_id);
        const result = {
            total: 0,
            list: []
        };
        const cond = {
            user_id: Number(user_id).toString(),
            // source: enums.PaperSourceType.ASSEMBLE,
            deleted: enums.BooleanNumber.NO
        }
        if (period) cond.period = period;
        if (subject) cond.subject = subject;
        if (grade) cond.grade = grade;
        if (type) cond.type = type;
        if (status) cond.status = status;
        if (year) {
            if (year > 0) cond.to_year = year;
            else cond.to_year = {$lt: 2020};
        }
        const total = await db_jzl.collection('parse-task').find(cond).count();
        if (!total) return responseWrapper.succ(result);
        result.total = total;
        const list = await db_jzl.collection('parse-task').find(cond).sort({createdAt: -1}).skip(offset).limit(limit).toArray();
        const paper_ids = [];
        for (const item of list) {
            if (item.tiku_paper_id) paper_ids.push(item.tiku_paper_id);
            result.list.push({
                id: item._id.toString(),
                name: item.name,
                question_num: 0,
                grade: item.grade || '',
                period: item.period,
                subject: item.subject,
                // source: item.source,
                type: item.type,
                from_year: item.from_year,
                to_year: item.to_year,
                status: item.status,
                tiku_paper_id: item.tiku_paper_id,
                // exam_status: enums.ExamStatus.EDITABLE,
                ctime: item.createdAt.getTime(),
                utime: item.updatedAt.getTime(),
                error: item.error || '',
                from_enum: 2, // 组卷
                task_type: item.task_type
            });
        }
        if (_.size(paper_ids)) {
            const papers = await db.collection(enums.OpenSchema.user_paper).find({_id: {$in: paper_ids.map(e => new ObjectId(e))}}).toArray();
            for (const data of result.list) {
                const paper = papers.find(e => data.tiku_paper_id === e._id.toString());
                if (paper) {
                    data.question_num = paper_utils.get_question_num(paper);
                    data.exam_status = paper.exam_status;
                    data.source = paper.source;
                }
            }
        }
        return responseWrapper.succ(result);
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const JOI_POST = Joi.object({
    // id: Joi.string().optional().length(24),
    // user_id: Joi.string().optional().allow(''),
    period: Joi.string().required(),
    subject: Joi.string().required(),
    grade: Joi.string().optional().allow('').default(''),
    name: Joi.string().required(),                  // 试卷名称， 2016-2017学年度X学校4月月考卷
    type: Joi.string().optional().allow('').default(''),
    from_year: Joi.number().integer().optional(),
    to_year: Joi.number().integer().optional(),
    subtitle: Joi.string().optional().default('').allow(''),              // 副标题
    score: Joi.optional().default(0),                 // 试卷总分
    duration: Joi.optional().default(120),              // 考试时间，单位分钟
    paper_info: Joi.optional().default(''),            // 试卷信息栏，考试范围：xxx；333考试时间：100分钟；命题人：xxx
    cand_info: Joi.string().optional().default('').allow(''),             // 候选人信息栏
    score_info: Joi.string().optional().default('').allow(''),            // 得分栏
    attentions: Joi.string().optional().default('').allow(''),            // 注意事项
    secret_tag: Joi.string().optional().default('').allow(''),            // 保密标记文字
    gutter: Joi.optional().valid(0, 1).default(0),                // 1：表示有装订线； 0：表示无装订线
    template: Joi.string().optional().default('standard'),
    partsList: Joi.array().items(Joi.string()).optional(),
    volumes: Joi.array().items(Joi.object({
        title: Joi.string().optional().default('').allow(''),
        note: Joi.string().optional().default('').allow(''),
        blocks: Joi.array().items(Joi.object({
            id: Joi.number().optional(), // 学情雷达会有
            name: Joi.string().optional(), // 学情雷达会有
            title: Joi.string().optional().default('').allow(''),
            note: Joi.string().optional().default('').allow(''),
            type: Joi.string().optional().default('').allow(''),
            default_score: Joi.optional().default(0),
            questions: Joi.array().items(Joi.object({
                    id: Joi.alternatives().try(
                        Joi.number(),
                        Joi.string(),
                    ).required(),
                    score: Joi.optional().default(0)
                }).unknown(true)
            )
        }))
    })).required().min(1).max(3),
    status: Joi.string().optional().allow(''),            // 状态
    exam_status: Joi.string().optional().allow(''),            // 状态
    category: Joi.number().optional(),
    province: Joi.string().optional().allow(''), // 省份
}).unknown(true);

async function putUploadById(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const { id } = req.params;
        const user_id = req.user.id;
        const params = await JOI_POST.validate(req.body);
        const paper = await db_open.collection(enums.OpenSchema.user_paper).findOne({_id: new ObjectId(id), user_id: utils.formatUserId(user_id), valid: enums.BOOL.YES});
        if (!paper) return responseWrapper.error('HANDLE_ERROR', '试卷不存在');
        if (paper.source === enums.PaperSourceType.UPLOAD
            && paper.status === enums.PaperStatus.DONE
        ) return responseWrapper.error('HANDLE_ERROR', '不可编辑');
        params.utime = new Date();
        if (params.status === enums.PaperStatus.DONE) { // 根据状态处理试题
            await syncPaperQuestions(user_id, params);
            // 同步任务状态
            await db_jzl.collection('parse-task').updateOne(
                {user_id: Number(user_id).toString(), tiku_paper_id: id},
                {$set: {status: enums.PaperStatus.DONE}});
        }
        await db_open.collection(enums.OpenSchema.user_paper).updateOne({_id: new ObjectId(id)}, {$set: params});
        return responseWrapper.succ({id});
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}


async function syncPaperQuestions(user_id, paper) {
    if (!paper) return;
    const { to_year } = utils.getAcademicYear();
    // 同步试题
    for (const volume of paper.volumes) {
        for (const block of volume.blocks) {
            for (const index in block.questions) {
                const ques = block.questions[index];
                if (ques.source !== enums.QuestionSource.UPLOAD) {
                    continue;
                }
                const newQues = _.pick(ques, [
                    'type', 'period', 'subject', 'grade', 'difficulty', 'score', 'description', 'comment', 'year',
                    'knowledges', 'audio', 'source', 'source_id', 'blocks'
                ]);
                newQues.grade = paper.grade;
                newQues.user_id = utils.formatUserId(user_id);
                newQues.valid = enums.BOOL.YES;
                newQues.ctime = new Date();
                newQues.utime = new Date();
                if (!newQues.year) newQues.year = to_year;
                if (!newQues.refer_exampapers) newQues.refer_exampapers = [];
                if (!newQues.difficulty) newQues.difficulty = 3;
                const insert = await db_open.collection(enums.OpenSchema.user_question).insertOne(newQues);
                block.questions[index] = {
                    id: insert.insertedId.toString(),
                    source: enums.QuestionSource.ZX,
                    source_id: insert.insertedId.toString(),
                    score: ques.score,
                    type: ques.type,
                    period: ques.period,
                    subject: ques.subject,
                };
            }
        }
    }
}


async function deleteUploadById(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const { id } = req.params;
        const task = await db_jzl.collection('parse-task').findOne({_id: new ObjectId(id), user_id: req.user.id.toString(), deleted: enums.BOOL.NO});
        if (_.isEmpty(task)) return responseWrapper.error('HANDLE_ERROR', '资源不存在');
        await db_jzl.collection('parse-task').updateOne({_id: new ObjectId(id)}, {$set: {deleted: enums.BOOL.YES, updatedAt: new Date()}});
        if (task.tiku_paper_id) {
            await db_open.collection(enums.OpenSchema.user_paper).updateOne({_id: new ObjectId(task.tiku_paper_id)}, {$set: {valid: enums.BOOL.NO, utime: new Date()}});
        }
        return responseWrapper.succ({id});
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}


module.exports = {
    getExampaperList,
    getExampaperInfo,
    postExampaperByWord,
    putExampaperById,
    putQuestionById,
    batchModifyQuestionType,
    deleteExampaperById,
    getUploadList,
    putUploadById,
    deleteUploadById,
};
