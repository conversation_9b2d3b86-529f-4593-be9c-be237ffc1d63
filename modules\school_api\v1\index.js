const _ = require('lodash');
const mongodber = require('../../utils/mongodber');
const db = mongodber.use('tiku');
const ResponseWrapper = require('../../middlewares/response_wrapper');
const logger = require('../../utils/logger');
const enums = require('../../../bin/enum');
const constants = require('../../utils/constants');
const moment = require('moment');
const workday = require('chinese-workday');
const utils = require('../../utils/utils');
const rediser = require('../../utils/rediser');
//
const collection_exam = db.collection(constants.schema.exam_teacher_paper);
// 用户组卷表
const collection_exampaper= db.collection(constants.schema.exampaper);

const collection_favorite = db.collection(constants.schema.favorite);

const coll_school_info = db.collection(constants.schema.school_info);

const coll_user = db.collection(constants.schema.user);

const coll_access_spot = db.collection(constants.schema.access_spot);

const coll_user_download = db.collection(constants.schema.user_download);



module.exports = {
    getSchoolStat,
};

async function getSchoolStat(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const school_id = req.user.schoolId;
        const cache_key = `tiku:school:${school_id}:stat`;
        const cache_data = await rediser.redis.get(cache_key);
        if (cache_data) {
            return responseWrapper.succ(JSON.parse(cache_data));
        }
        const result = {
            reg_day_num: 0,
            vip_day_num: 0,
            vip_user_num: 0,
            exampaper_num: 0,
            download_num: 0,
            active_num: 0,
            new_data: {
                sys_ques_num: 0,
                sys_paper_num: 0,
                tiku_paper_num: 0,
                download_num: 0,
            },
            new_schools: []

        };
        if (!school_id) return responseWrapper.succ(result);
        const school_info = await coll_school_info.findOne({_id: school_id});
        if (_.isEmpty(school_info)) return responseWrapper.succ(result);
        result.reg_day_num = moment(new Date()).diff(school_info.ctime, 'day') || 1;

        if (school_info.vip_type && school_info.expired_time && school_info.expired_time.getTime() > Date.now()) {
            result.vip_day_num = moment(school_info.expired_time).diff(new Date(), 'day');
            if (school_info.vip_type === enums.MemberType.YJ_PROFESSION || school_info.vip_type === enums.MemberType.YJ_ULTIMATE) {
                result.vip_user_num = await coll_user.count({sch_id: school_id});
            } else {
                result.vip_user_num = _.size(school_info.teachers || []);
            }
        }
        const query_date = moment().subtract(7, 'day').toDate();
        // 组卷
        const cond = {
            sch_id: school_id,
            ctime: {
                $gte: query_date
            }
        };
        const school_exampapers = await collection_exampaper.find(cond).project({_id: 1, name: 1, exampaper_download_num: 1}).toArray();
        result.exampaper_num = _.size(school_exampapers) * 3;
        // 下载
        const user_download = await coll_user_download.find(cond).project({_id: 1, download_times: 1}).toArray();
        result.download_num = (_.sumBy(user_download, 'download_times') + _.sumBy(school_exampapers, 'exampaper_download_num')) * 2;
        // 活跃
        const land_cond = {
            school_id: school_id,
            timestamp: {
                $gte: query_date
            },
            $or: [
                { event_id: enums.EventType.LAND_HFSJS_TIMES },
                { event_id: enums.EventType.LAND_WEB_TIMES }
            ]
        }
        const land_web_times = await coll_access_spot.count(land_cond);
        result.active_num = (land_web_times || 0 ) * 2;
        // 随机试卷，试题 根据昨天
        let sys_exampaper_num = 0;
        let sys_question_num = 0;
        const isWork = workday.isWorkday(moment().subtract(1, 'day').toDate());
        if (isWork) {
            sys_exampaper_num = utils.randomRange(1400, 2100);
            sys_question_num = utils.randomRange(14000, 21000);
        } else {
            sys_exampaper_num = utils.randomRange(700, 1400);
            sys_question_num = utils.randomRange(7000, 14000);
        }

        result.new_data.sys_paper_num = sys_exampaper_num;
        result.new_data.sys_ques_num = sys_question_num;

        const tiku_exampaper = await collection_exampaper.find({ctime: {$gte: query_date}}).project({_id: 1, exampaper_download_num: 1}).toArray();
        result.new_data.tiku_paper_num = _.size(tiku_exampaper) * 17;
        const download = await coll_user_download.find({ctime: {$gte: query_date}}).project({_id: 1, download_times: 1}).toArray();
        result.new_data.download_num = (_.sumBy(tiku_exampaper, 'exampaper_download_num') + _.sumBy(download, 'download_times')) * 17;
        // 学校列表
        const schools = await coll_school_info.find({}).project({_id: 1, name: 1}).sort({ ctime: -1 }).limit(10).toArray();
        if (_.size(schools)) {
            result.new_schools = schools.map(e => {
                return {
                    id: e._id,
                    name: e.name
                }
            });
        }
        rediser.set(cache_key, result, 60 * 60);
        return responseWrapper.succ(result);
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}
