const mongodber = require('../../utils/mongodber');
const ResponseWrapper = require('../../middlewares/response_wrapper');
const logger = require('../../utils/logger');
const enums = require('../../../bin/enum');
const _ = require('lodash');
const db = mongodber.use('tiku');
const utils = require('../../utils/utils');
const client = require('../../client/index');
const Joi = require('@hapi/joi');

module.exports = {
    getHomeData,
    getOptions,
    getTotal,
    search,
    getPackDetail,
    addVisit,
    getVideo,
};

const JOI_GET_HOME_DATA = Joi.object({
    period: Joi.string().required(),
    subject: Joi.string().required(),
    grade: Joi.string().optional(),
    type: Joi.number().optional(),
}).unknown(true);

const period_grade = {
    '小学': ['小学'],
    '初中': ['初一', '初二', '初三'],
    '高中': ['高一', '高二', '高三'],
    '职中': ['高一', '高二', '高三'],
}
async function getHomeData(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const { period, subject, grade, type, limit } = await JOI_GET_HOME_DATA.validate(req.query);
        const options = await client.sy.getOptions();
        const result = {
            options: [],
            total: 0,
            items: []
        };
        const grades = [];
        if (!grade) {
            const arr = period_grade[period];
            if (_.size(arr)) grades.push(...arr);
        } else {
            grades.push(grade);
        }
        if (!_.size(grades)) return responseWrapper.succ(result);
        let subject_id = 0;
        const grade_ids = [];
        if (_.size(options)) {
            const option_set = new Set();
            for (const op of options) {
                if (!grades.includes(op.grade_name)) continue;
                grade_ids.push(op.grade)
                for (const sub of op.subjects) {
                    if (sub.subject_name !== subject) continue;
                    subject_id = sub.subject;
                    for (const t of sub.types) {
                        if (option_set.has(t.type)) continue;
                        option_set.add(t.type);
                        result.options.push({
                            type: t.type,
                            type_name: t.type_name
                        });
                    }
                }
            }
        }
        // 根据年级和科目获取课程包列表
        const packs = [];
        for (const grade_id of grade_ids) {
            const pack_params = {
                subject: subject_id,
                grade: grade_id,
                target: 1,
                pagesize: 99,
                pageIndex: 1,
            }
            if (type) {
                pack_params.type = type;
            }
            const list = await client.sy.search(pack_params);
            if (_.size(list)) {
                packs.push(...list);
            }
        }
        if (_.size(packs)) {
            result.total = _.size(packs);
            result.items = _.chunk(packs, limit)[0];
        }
        return responseWrapper.succ(result);
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

async function getOptions(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const result = await client.sy.getOptions();
        return responseWrapper.succ(result);
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
}


const JOI_GET_SEARCH_TOTAL = Joi.object({
    grade: Joi.number().optional(),
    subject: Joi.number().required(),
    type: Joi.number().optional(),
}).unknown(true);

async function getTotal(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const params = await JOI_GET_SEARCH_TOTAL.validate(req.query);
        const result = await client.sy.total(params);
        return responseWrapper.succ(result);
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
}

const JOI_GET_SEARCH = Joi.object({
    grade: Joi.number().optional(),
    subject: Joi.number().required(),
    type: Joi.number().optional(),
    pagesize: Joi.number().required(),
    pageindex: Joi.number().required(),
}).unknown(true);

async function search(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const params = await JOI_GET_SEARCH.validate(req.query);
        params.target = 1;
        const result = await client.sy.search(params);
        return responseWrapper.succ(result);
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
}

const JOI_GET_PACK_DETAIL = Joi.object({
    pack_id: Joi.string().required(),
}).unknown(true);

async function getPackDetail(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const { pack_id } = await JOI_GET_PACK_DETAIL.validate(req.query);
        const result = await client.sy.getPackageDetail(pack_id);
        return responseWrapper.succ(result);
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
}

const JOI_POST_PACK_VISIT = Joi.object({
    pack_id: Joi.string().required(),
});

async function addVisit(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const { pack_id } = await JOI_POST_PACK_VISIT.validate(req.body);
        const result = await client.sy.addVisit(pack_id);
        return responseWrapper.succ(result);
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
}

const JOI_GET_VIDEO = Joi.object({
    section_id: Joi.string().required(),
    isApp: Joi.number().optional().default(1)
}).unknown(true);

async function getVideo(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const { section_id, isApp } = await JOI_GET_VIDEO.validate(req.query);
        const result = await client.sy.getVideo(section_id, isApp);
        return responseWrapper.succ(result);
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
}
