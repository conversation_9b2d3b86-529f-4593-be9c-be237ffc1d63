const ResponseWrapper = require('../../middlewares/response_wrapper');
const logger = require('../../utils/logger');
const ObjectId = require('mongodb').ObjectId;
const baseDao = require('../../utils/baseDao');
const doRequest = require('../../utils/proxy').doRequest;
const kbServer = require('config').get('KB_API_SERVER');
//const kbServer = require('config').get('MY_SERV');
const url = require('url');
const qs = require('qs');
const mapExampaperName = require('../../utils/mapExampaperName.js');

const fillExam = async (data, type) => {
    if (!data || data.length === 0) {
        return;
    }
    let ids = [];
    data.forEach((ele) => {
        ele.id = ele._id;
        delete ele._id;
        ele.ctime = typeof(ele.ctime) === 'string' ? ele.ctime : ele.ctime.toLocaleString();
        ele.groups = ele.groups || [];
        ele.groups.forEach((item) => {
            let exam_ids = item.exampapers.map((element) => {
                return element.id;
            })
            ids = [...ids, ...exam_ids]
        });
    })
    let exam = await getExampaperFromKb(ids);
    let examMap = {};
    if (exam) {
        exam.forEach((ele) => {
            examMap[ele.id] = {
                download_times: ele.download_times || 0,
                view_times: ele.view_times || 0,  
            };
            if (type === 'detail') {
                examMap[ele.id].name = ele.name;
                examMap[ele.id].type = ele.type;
                examMap[ele.id].ques_total = 0;
                ele.blocks = ele.blocks || [];
                ele.blocks.forEach((item) => {
                    examMap[ele.id].ques_total += (item.questions.length || 0);
                })
            }
        });
    }
    data.forEach((ele) => {
        ele.exam_total = 0;
        ele.download_times = 0;
        ele.view_times = ele.view_times || 0;
        ele.groups.forEach((element) => {
            element.exampapers = element.exampapers || [];
            element.exampapers.forEach((item) => {
                if (examMap[item.id]) {
                    ele.exam_total += 1;
                    ele.download_times += examMap[item.id].download_times;
                    //ele.view_times += examMap[item.id].view_times;
                    if (type === 'detail') {
                        item.name = examMap[item.id].name;
                        item.ques_total = examMap[item.id].ques_total;
                        item.type = examMap[item.id].type;
                        item.download_times = examMap[item.id].download_times;
                        item.view_times = examMap[item.id].view_times;
                    }
                }
            });
            element.exampapers = element.exampapers.filter((ele) => {
                return (ele.type || ele.name);
            })
        })
        ele.groups = ele.groups.filter((item) => {
            return item.exampapers && item.exampapers.length
        });
        if (type !== 'detail') {
            delete ele.groups;
        };
    })
};

const getExampaperFromKb = async (ids) => {
    let doUrl = url.format({
        protocol: kbServer.protocol,
        hostname: kbServer.hostname,
        port: kbServer.port,
        pathname: '/kb_api/v2/exampapers/' + ids.join(',') + '/list',
        search: qs.stringify({
            app_key: kbServer.appKey,
            mode: 'tiku'
        })
    });

    let response = await doRequest(doUrl, 'GET');
    return response.data || [];
};

const createAlbum = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let {type, name, period, subject, is_elite, groups} = req.body;
        let user = req.user;
        is_elite = JSON.parse(is_elite);
        is_elite = typeof is_elite === 'boolean' ? is_elite : false;
        if (!name) {
            throw new Error('参数错误！缺少专辑名！');
        }
        let newAlbum = {
            _id: new ObjectId(),
            name: name || '',
            type: type || '',
            period: period || '',
            subject: subject || '',
            is_elite: is_elite,
            groups: groups || [],
            school_id: user.schoolId * 1,
            ctime: new Date(),
            user_id: user.id,
            user_name: user.name,
            is_del: false
        };
        await baseDao._addInstance('album', newAlbum);
        return responseWrapper.succ({id: newAlbum._id});
    } catch (error) {
        logger.error(error.stack);
        return responseWrapper.error('HANDLE_ERROR');
    }
};

const editAlbum = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let {type, name, is_elite, period, subject, groups} = req.body;
        let album_id = ObjectId(req.params.album_id);
        let user = req.user;
        is_elite = JSON.parse(is_elite);
        is_elite = typeof is_elite === 'boolean' ? is_elite : false;
    
        let cond = {
            _id: album_id, 
            school_id: user.schoolId * 1, 
            is_del: false
        };
        let album = await baseDao._getInstanceByCond('album', cond);
        if (!album) {
            return responseWrapper.error('NULL_ERROR');
        }
        let editAlbum = {
            type: type || album.type,
            name: name || album.name, 
            is_elite: is_elite,
            period: period || album.period, 
            subject: subject || album.subject, 
            groups: groups || album.groups
        };
        await baseDao._updateInstanceByCond('album', {_id: album_id, is_del: false}, {$set: editAlbum});
        return responseWrapper.succ({});
    } catch (error) {
        logger.error(error.stack);
        return responseWrapper.error('HANDLE_ERROR');
    }
};

const removeAlbum = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let user_id = req.user.id * 1;
        let album_id = ObjectId(req.params.album_id);
        let album = await baseDao._getInstanceByCond('album', {_id: album_id, is_del: false});
        if (!album) {
            return responseWrapper.error('NULL_ERROR');
        }
        let removeData = {
            is_del: true,
            dtime: new Date()
        };
        await baseDao._updateInstanceByCond('album', {_id: album_id}, {$set: removeData});
        responseWrapper.succ({});

        let favorite = await baseDao._getInstanceByCond('favorite', {user_id: user_id});
        let albums = favorite.albums || [];
        albums = albums.filter((ele) => {
            return `${ele.id}` !== `${album_id}`;
        });
        let cond = {user_id: user_id};
        let data = {$set: {albums: albums}};
        await baseDao._updateInstanceByCond('favorite', cond, data);
        return;
    } catch (error) {
        logger.error(error.stack);
        return responseWrapper.error('HANDLE_ERROR');
    }
};

const getAlbumList = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let {limit = 9, offset = 0, type, period, subject, sortBy, sortOri} = req.query;
        let cond = {
            //school_id: req.user.schoolId * 1,
            is_del: false
        };
        if (type) {
            cond.type = type;
        }
        if (period) {
            cond.period = period;
        }
        if (subject) {
            cond.subject = subject;
        }     
        let options = {
            limit: limit * 1 || 9,
            offset: offset * 1 || 0,
            total: true,
            sorts:  {ctime: -1}
        };
        if (sortBy) {
            options.sorts[sortBy] = sortOri || -1;
        }
        let data = await baseDao._getListByCond('album', cond, options);
        data.albums = data.list;
        delete data.list;
        await fillExam(data.albums);
        return responseWrapper.succ(data);
    } catch (error) {
        logger.error(error.stack);
        return responseWrapper.error('HANDLE_ERROR');
    }
};

const getAlbumEliteList = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let {limit = 6, offset = 0, period, subject, type, sortBy, sortOri} = req.query;
        let cond = {
            //school_id: req.user.schoolId * 1,
            is_elite: true,
            is_del: false
        };
        if (type) {
            cond.type = type;
        }
        if (period) {
            cond.period = period;
        }
        if (subject) {
            cond.subject = subject;
        }
        let options = {
            limit: limit * 1 || 6,
            offset: offset * 1 || 0,
            sorts: {ctime: -1},
            total: true
        };
        if (sortBy) {
            options.sorts[sortBy] = sortOri || -1;
        }
        let data = await baseDao._getListByCond('album', cond, options);
        data.albums = data.list;
        delete data.list;
        await fillExam(data.albums);
        return responseWrapper.succ(data);
    } catch (error) {
        logger.error(error.stack);
        return responseWrapper.error('HANDLE_ERROR');
    }
};

const getAlbumType = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let {period, subject} = req.query;
        let cond =  {
            //school_id: req.user.schoolId * 1,
            is_del: false
        }
        if (period) {
            cond.period = period;
        }
        if (subject) {
            cond.subject = subject;
        }
        let types = await baseDao._distinctByCond('album', 'type', cond);
        types = types.map((ele) => {
            return {
                key: 'type',
                name: ele
            }
        })
        types.unshift({
            key: 'type',
            name: '全部'
        });
        let result = {
            type: types,
        };
        return responseWrapper.succ(result);
    } catch (error) {
        logger.error(error.stack);
        return responseWrapper.error('HANDLE_ERROR');
    }
};


const getAlbumDetail = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let album_id = ObjectId(req.params.album_id);
        let data = await baseDao._getInstanceByCond('album', {_id: album_id});
        if (!data) {
            return  responseWrapper.succ({});
        }
        data.groups = data.groups || [];
        data = [data]
        await fillExam(data, 'detail');
        await baseDao._updateInstanceByCond('album', {_id: album_id}, {$inc: {view_times: 1}});
        data[0].view_times += 1;
        return responseWrapper.succ(data[0]);
    } catch (error) {
        logger.error(error.stack);
        return responseWrapper.error('HANDLE_ERROR');
    }
};

const collectAlbum = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    if(!req.user.isVip){
        return responseWrapper.error('NEED_VIP_ERROR', '');
    }
    try {
        let album_id = req.params.album_id;
        let user_id = req.user.id * 1;
        let favorite = await baseDao._getInstanceByCond('favorite', {user_id: user_id});
        let album = await baseDao._getInstanceByCond('album', {_id: ObjectId(album_id)});
        if (!favorite) {
            favorite = {albums: []};
        }
        let albums = favorite.albums || [];
        albums = albums.filter((ele) => {
            return `${ele.id}` !== `${album_id}`;
        });
        albums.push({
            ctime: new Date(),
            period: album.period,
            subject: album.subject,
            id: album_id,
        });
        let cond = {user_id: user_id};
        let data = {$set: {albums: albums}};
        await baseDao._updateInstanceByCond('favorite', cond, data, {upsert: true});
        return responseWrapper.succ({id:user_id});
    } catch (error) {
        logger.error(error.stack);
        return responseWrapper.error('HANDLE_ERROR');
    }
};

const getAlbumCollectList = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let {limit = 9, offset = 0, period, subject, type} = req.query;
        let user_id = req.user.id * 1;
        let favorite = await baseDao._getInstanceByCond('favorite', {user_id: user_id}, {albums: 1});
        if (!favorite || !favorite.albums || !Array.isArray(favorite.albums) || favorite.albums.length === 0) {
            return responseWrapper.succ({total: 0, albums: []});
        }
        let ids = favorite.albums.map((ele) => {
            return ObjectId(ele.id);
        });
        let cond = {
            _id: {$in: ids},
            //school_id: req.user.schoolId * 1,
            is_del: false
        };
        if (period) {
            cond.period = period;
        }
        if (subject) {
            cond.subject = subject;
        }
        if (type) {
            cond.type = type;
        }
        let options = {
            limit: limit * 1 || 9,
            offset: offset * 1 || 0,
            sorts: {ctime: -1},
            total: true
        };
        let data = await baseDao._getListByCond('album', cond, options);
        data.albums = data.list;
        delete data.list;
        await fillExam(data.albums);
        return responseWrapper.succ(data);
    } catch (error) {
        logger.error(error.stack);
        return responseWrapper.error('HANDLE_ERROR');
    }
};

const delCollectAlbum = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let album_id = req.params.album_id;
        let user_id = req.user.id * 1;
        let favorite = await baseDao._getInstanceByCond('favorite', {user_id: user_id});
        if (!favorite) {
            return responseWrapper.succ({id:user_id})
        }
        let albums = favorite.albums || [];
        albums = albums.filter((ele) => {
            return `${ele.id}` !== `${album_id}`;
        });
        let cond = {user_id: user_id};
        let data = {$set: {albums: albums}};
        await baseDao._updateInstanceByCond('favorite', cond, data);
        return responseWrapper.succ({id:user_id});
    } catch (error) {
        logger.error(error.stack);
        return responseWrapper.error('HANDLE_ERROR');
    }
}

const getCollectAlbumId = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let user_id = req.user.id * 1;
        let data = await baseDao._getInstanceByCond('favorite', {user_id: user_id}, {albums: 1});
        if (!data) {
            return responseWrapper.succ([]);
        }
        data.albums = data.albums || [];
        if (data.length === 0) {
            return responseWrapper.succ([]);
        }
        let result = data.albums.map((ele) => {
            return ele.id;
        });
        return responseWrapper.succ(result);
    } catch (error) {
        logger.error(error.stack);
        return responseWrapper.error('HANDLE_ERROR');
    }
}
module.exports = {
    createAlbum,
    editAlbum,
    removeAlbum,
    getAlbumList,
    getAlbumEliteList,
    getAlbumType,
    getAlbumDetail,
    collectAlbum,
    delCollectAlbum,
    getAlbumCollectList,
    getCollectAlbumId
};