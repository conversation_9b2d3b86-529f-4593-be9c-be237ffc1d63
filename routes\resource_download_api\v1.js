/**
 * 考后巩固
 */

const express = require('express');
const router = express.Router();
const service = require('../../modules/resource_download/v1/index');

// 获取分类信息
router.get('/category/info', service.getCategoryInfo);
// 添加分类
router.post('/category', service.initCategory);
// 增加浏览次数
router.put('/category/:id/view_times', service.addCategoryViewTimes);
// 获取购物车资源
router.get('/cart', service.getCartInfo);
// 添加资源
router.post('/cart', service.addResourceToCart);
// 删除资源
router.delete('/cart', service.deleteCartResource);

module.exports = router;
