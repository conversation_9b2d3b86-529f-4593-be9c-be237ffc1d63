
var rediser = require('./rediser');
var assert = require('assert');

var REDIS = { 
		"host": "127.0.0.1",
		//"port": 6378,
		"port": 6379,
		"db": 0,
		"password": ""
	};

rediser.init(REDIS, function(err){
    if (err){
        console.log('Connect to redis failed.');
        process.exit(-1);
    }
    var key = 'test';
    var value = 'test_value';
    var dua = 10;
    rediser.set(key, value, dua, function(err){
        rediser.get(key, function(err, _value){
            assert(value === _value);
            console.log(`${value} === ${_value}`)
            rediser.del(key, function(err){
                rediser.get(key, function(err, _value){
                    assert(_value == null);
                    console.log(`${_value} == null`);
                })
            })
        })
    });
});
