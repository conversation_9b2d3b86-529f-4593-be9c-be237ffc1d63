var mongodber = require('../../utils/mongodber');
var ResponseWrapper = require('../../middlewares/response_wrapper');
var logger = require('../../utils/logger');
var extendQues = require('../../assemble_api/v1/extend_ques');
const _ = require('underscore');
const config = require('config');
const KBSERVER = config.get('KB_API_SERVER');
const qs = require('qs');
const URL = require('url');
const request = require('request');
var extendExam = require('./extend_exampaper');
const KBPSERV = config.get('KBPLAT_API_SERVER');
const ObjectID = require("mongodb").ObjectID;
const client = require('zipkin-middleware').client;
const mapExampaperName = require('../../utils/mapExampaperName.js');
const lodash = require('lodash');
const server_client = require('../../client');
const Joi = require('@hapi/joi');
const enums = require('../../../bin/enum');

const httpGet = async (url) => {
    return new Promise((resolve, reject) => {
        request(url, (err, response, body) => {
            if (err) {
                return reject(err);
            }
            try {
                body = JSON.parse(body);
                return resolve(body);
            } catch (err) {
                return reject(err);
            }
        });
    });
};
var db = mongodber.use('tiku');
const db_open = mongodber.use('tiku_open');

var collection = db.collection('favorite');

if(!collection){
    logger.error('数据库连接初始化失败');
    throw new Error('');
}


/**
 *  收藏一道试题
 *  /user_api/v1/favorite/questions/
 */
function postQuestion(req, res){

    var resWrapper = new ResponseWrapper(req, res);
    if(!req.body || !req.body.question_id){
        return resWrapper.error('PARAMETERS_ERROR', '');
    }
    var userId = req.user.id;
    var question_id = Number(req.body.question_id);

    var cond = {user_id: userId};
    // 提取字段
    var proj = {questions: 1};

    return queryItems(cond, proj, function(err, items){

        if(err){
            logger.error(err.message);
            return resWrapper.error('HANDLE_ERROR', err.message);
        }

        items = items.questions? items.questions: [];
        for (var i in items){
            var item = items[i];
            if(item.id == question_id){
                return resWrapper.succ({id: userId});
            }
        }

        return extendQues.get_kb_questions([question_id], function(err, questions){
            if(err){
                logger.error(err.message);
                return resWrapper.error('HANDLE_ERROR', err.message);
            }
            var ques = {};

            if(Array.isArray(questions) && questions.length > 0){
                // 取questions 部分属性
                ques.ctime = new Date();
                ques.period = questions[0].period;
                ques.subject = questions[0].subject;

                ques.id = question_id;
                ques.description = questions[0].description;
                ques.type = questions[0].type;
                ques.blocks = questions[0].blocks;
                items.push(ques);
                updateQues(items, cond, function(err, res){
                    if(err){
                        logger.error(err.message);
                        return resWrapper.error('HANDLE_ERROR', err.message);
                    }

                    return resWrapper.succ({id: userId});
                });
            }else{
                return resWrapper.error('PARAMETERS_ERROR', err.message);
            }
        });
    });
}

function queryItems(cond, proj, callback){
    return collection.find(cond).project(proj).toArray(function(err, items){
            if (err){
                logger.error(err.message);
                return callback(err, items);
            }

            if (!items || !items[0]) {
				if (!cond.userId) {
                    cond.userId = cond.user_id;
                }
                collection.insert({user_id: cond.userId, questions:[], exampapers:[], edu_files: [], micro_courses: [] }, function (err, r){
                    if (err) {
                        logger.error(err)
                    }
                });
            }
            return callback( null, items[0]);
        });
}

function queryItems2(cond, proj, callback){

    return collection.find(cond).project(proj).sort({'ctime': -1})
        .toArray(function(err, items){

            if (err) {
                logger.error(err.message);
                return callback(err, items);
            }

            return callback( null, items[0]);
        });
}

function updateQues(ques_list, cond, callback){
    var doc = {'$set': {questions: ques_list}};
    collection.updateOne(cond, doc, function(err, res){
        callback(err, res);
    });
}

function updateExampaper(exam_list, cond, callback){
    var doc = {'$set': {exampapers: exam_list}};
    collection.updateOne(cond, doc, function(err, res){
        callback(err, res);
    });
}

function getQuesInfo(req, res){
    let resWrapper = new ResponseWrapper(req, res);
    let userId = req.user.id;
    let period = req.query.period || '';
    let subject = req.query.subject || '';
    let type = req.query.type || '';
    let offset = parseInt(req.query.offset || 0);
    let limit = parseInt(req.query.limit || 10);
    let match = {};
    period ? match.period = period : '';
    subject ? match.subject = subject : '';
    type ? match.type = type : '';
    let cond = {};
    if (match && (match.period || match.subject || match.type)) {
        cond = {user_id : userId,questions: {'$elemMatch' : match}};
    } else {
        cond = {user_id : userId};
    }
    let proj = {questions : 1};
    return queryItems2(cond, proj, function(err, items){
        if (err) {
            logger.error(err.message);
            return resWrapper.error('HANDLE_ERROR', null);
        }

        if (!items) {
            return resWrapper.succ({total_num: 0, questions: []});
        }

        items = items.questions ? items.questions : [];
        let ques_id_list = [];
        let collTimeMap = {};
        items = _.filter(items, (x) => {
            if (!!x === false)
                return false;
            if (match.period && match.period !== x.period) {
                return false;
            }
            if (match.type && match.type !== x.type) {
                return false;
            }
            if (match.subject && match.subject !== x.subject) {
                return false;
            }
            collTimeMap[x.id] = x.ctime;
            return true;
        });
        let total_num = items.length;
        items.sort((a, b) => {
            return new Date(b.ctime) - new Date(a.ctime);
        });
        let tmpItems = items.slice(offset, offset + limit);
        if (tmpItems && tmpItems.length === 0)
            items = items.slice(offset - limit, offset);
        if (tmpItems && tmpItems.length > 0)
            items = tmpItems;
        ques_id_list = _.pluck(items, 'id');

        if (!ques_id_list.length) {
            return resWrapper.succ({total_num: 0,questions: []});
        }

        return extendQues.get_kb_questions(ques_id_list, function(err, questions){
            if (err) {
                logger.error(err.message);
                return resWrapper.error('HANDLE_ERROR', null);
            }
            questions.forEach((ele) => {
                ele.ctime = collTimeMap[ele.id].toLocaleString();
                if (ele.blocks) {
                    delete ele.blocks.answers;
                    delete ele.blocks.solutions;
                    delete ele.blocks.explanations;
                    delete ele.blocks.knowledges;
                }
            });
            return resWrapper.succ({total_num: total_num,questions: questions});
        });
    });
}

/**
*
*/
function delFavoriteQues(req, res){

    var resWrapper = new ResponseWrapper(req, res);
    try {
        var userId = req.user.id;
        var ques_id = req.params.question_id;
        var cond = {user_id: userId};
        var proj = {questions : 1};
    } catch(err){
        return resWrapper.error('PARAMETERS_ERROR', err.message);
    }
    queryItems(cond, proj, function(err, items){
        if (err){
            logger.error(err.message);
            return resWrapper.error('HANDLE_ERROR', null);
        }
        items = items.questions? items.questions: [];
        var ques_list = [];
        for (var i in items){
            var item = items[i];
            if(item.id == ques_id){
                continue;
            }
            ques_list.push(item);
        }

        updateQues(ques_list, cond, function(err, res){

            if(err){
                logger.error(err.message);
                return resWrapper.error('HANDLE_ERROR', err.message);
            }

            return resWrapper.succ({id: userId});
        });
    });
}

function getQuesKeys(req, res){
    let startDate = Date.now();
    let resWrapper = new ResponseWrapper(req, res);
    let userId = req.user.id;
    let cond = {user_id: userId};
    let proj = {questions : 1};

    res.on('finish', () => {
        let require_time = Date.now() - startDate;
        if (require_time > 1000) {
            logger.info('favorite_question_keys require_time:' + require_time + 'ms, user_id:' + userId);
        }
    });

    queryItems(cond, proj, function(err, items){
        if (err){
            logger.error(err.message);
            return resWrapper.error('HANDLE_ERROR', err.message);
        }

        let ques_id_list = [];
        if(items) {
            items = items.questions? items.questions: [];
            for (let i in items) {
                let item = items[i];
                ques_id_list.push(Number(item.id));
            }
        }
        return resWrapper.succ(ques_id_list);
    });
}

async function postExampaperAsync(req, res) {
    const resWrapper = new ResponseWrapper(req, res);
    try {
        const userId = req.user.id;
        let exampaper_id = req.body.exampaper_id;
        if (!userId || !exampaper_id) return resWrapper.error('PARAMETERS_ERROR', '参数错误!');
        let data = await collection.findOne({user_id: userId});
        if (lodash.isEmpty(data)) {
            data = _init_user_favorite(req.user.id);
            const insertResult = await collection.insertOne(data);
            data._id = insertResult.insertedId;
        }
        const items = data.exampapers? data.exampapers: [];
        if (items.find(e => e.id == exampaper_id)) return resWrapper.succ({id: userId});
        let exampaper = null;
        if (lodash.isNumber(exampaper_id)) {
            exampaper_id = Number(exampaper_id);
            exampaper = await server_client.kb.getExampaper(exampaper_id);
        } else {
            exampaper = await db_open.collection(enums.OpenSchema.user_paper).findOne({_id: ObjectID(exampaper_id)});
            if (!_.isEmpty(exampaper) && exampaper.user_id !== userId) {
                return resWrapper.error('HANDLE_ERROR', '收藏资源不存在!');
            }
        }
        if (_.isEmpty(exampaper)) return resWrapper.error('HANDLE_ERROR', '收藏资源不存在!');
        items.push({
            ctime: new Date(),
            period: exampaper.period,
            subject: exampaper.subject,
            name: exampaper.name,
            id: exampaper_id,
            type: exampaper.type,
            view_times: exampaper.view_times,
            province: exampaper.province,
            grade: exampaper.grade
        });
        await collection.updateOne({_id: data._id}, { $set: { exampapers: items }});
        return resWrapper.succ({id: userId});
    } catch (err) {
        return resWrapper.error('HANDLE_ERROR', err.message);
    }
}

function postExampaper(req,res){
    var resWrapper = new ResponseWrapper(req, res);
    try {
        var userId = req.user.id;
        var exampaper_id = Number(req.body.exampaper_id);

    } catch(err) {
        return resWrapper.error('PARAMETERS_ERROR', err.message);
    }
    var cond = {user_id: userId};
    var proj = {exampapers: 1};

    queryItems(cond, proj, function(err, items){
        if(err){
            logger.error(err.message);
            return resWrapper.error('HANDLE_ERROR', err.message);
        }
        items = items.exampapers? items.exampapers: [];
        for (var i in items) {
            var item = items[i];
            if(item.id == exampaper_id){
                return resWrapper.succ({id: userId});
            }
        }
        extendExam.getKbExampaper(exampaper_id, function(err, exampaper){
            if(err){
                logger.error(err.message);
                return resWrapper.error('HANDLE_ERROR', err.message);
            }
            var paper = {};
            if(exampaper){
                // 取paper 部分属性
                paper.ctime = new Date();
                paper.period = exampaper.period;
                paper.subject = exampaper.subject;
                paper.name = exampaper.name;
                paper.id = exampaper_id;
                paper.type = exampaper.type;
                paper.view_times = exampaper.view_times;
                paper.province = exampaper.province;
                paper.grade = exampaper.grade;

                items.push(paper);
                updateExampaper(items, cond, function(err, res){
                    if(err){
                        logger.error(err.message);
                        return resWrapper.error('HANDLE_ERROR', err.message);
                    }
                    return resWrapper.succ({id: userId});
                });
            }else{
                return resWrapper.error('PARAMETERS_ERROR');
            }

        });

    });
}

const getFavoriteExampaperNew = async (req, res) => {
    let resWrapper = new ResponseWrapper(req, res);
    try {
        let userId = req.user.id;
        let offset = parseInt(req.query.offset || 0);
        let limit = parseInt(req.query.limit || 10);

        //获取收藏试卷 from tiku
        let favoriteExampapers = await db.collection('favorite').find({user_id : userId}).project({exampapers : 1}).sort({'ctime': -1}).toArray();
        let exampaperIds = [];
        let kb_exampaperIds = [];
        let collTimeMap = {};
        _.each(favoriteExampapers[0].exampapers, function (examp) {
            if (lodash.isNumber(examp.id)) {
                kb_exampaperIds.push(Number(examp.id));
            } else {
                exampaperIds.push(examp.id);
            }
            collTimeMap[examp.id] = examp.ctime;
        });

        if (exampaperIds.length === 0 && kb_exampaperIds.length === 0)
            return resWrapper.succ({total_num: 0, exampapers: []});
        const paperArray = [];

        try {
            if (lodash.size(kb_exampaperIds)) {
                for (const ids of _.chunk(kb_exampaperIds, 20)) {
                    let kbUrl = URL.format({
                        protocol: KBSERVER.protocol,
                        hostname: KBSERVER.hostname,
                        port: KBSERVER.port,
                        pathname: `/kb_api/v2/exampapers/${ids.join(',')}/list`,
                        search: qs.stringify({
                            api_key: req.apiKey
                        })
                    });
                    let _body = await client.axios.get(kbUrl);
                    if (!lodash.size(_body.data)) {
                        continue;
                    }
                    _body.data.forEach(e => paperArray.push(e));
                }
            }
            if (lodash.size(exampaperIds)) {
                const list = await db_open.collection(enums.OpenSchema.user_paper).find({_id: {$in: exampaperIds.map(e => ObjectID(e))}}).toArray();
                list.forEach(e => {
                    const paper = {
                        id: e._id.toString(),
                        name: e.name,
                        period: e.period,
                        subject: e.subject,
                        grade: e.grade,
                        type: e.type,
                        view_times: e.view_times,
                        ctime: e.ctime,
                        ques_num: 0
                    };
                    paperArray.push(paper);
                });
            }
        } catch (e) {
            logger.error(e);
        }

        // exampaperIds = exampaperIds.join(',');

        //获取试卷信息 from kb
		// let kbUrl = URL.format({
		// 	protocol: KBSERVER.protocol,
		// 	hostname: KBSERVER.hostname,
		// 	port: KBSERVER.port,
        //     pathname: `/kb_api/v2/exampapers/${exampaperIds}/list`,
        //     search: qs.stringify({
		// 		api_key: req.apiKey
		// 	})
        // });
        // let _body = await client.axios.get(kbUrl);
        //if (Array.isArray(_body.data)) {
            // _body.data.forEach(i => {
            //     i.name = mapExampaperName(i)
            // });
        //}
        //筛选数据
        let exampaper_list = [];
        exampaper_list = _.filter(paperArray, (x) => {
            if (req.query.period && x.period !== req.query.period)
                return false;
            if (req.query.subject && x.subject !== req.query.subject)
                return false;
            if (req.query.type && x.type !== req.query.type)
                return false;
            if (req.query.grade && x.grade !== req.query.grade)
                return false;
            x.ctime = collTimeMap[x.id].toLocaleString()
            return true;
        });
        exampaper_list.sort((a, b) => {
            return new Date(b.ctime) - new Date(a.ctime)
        });
        let total_num = exampaper_list.length;
        let tmpExam = exampaper_list.slice(offset, offset + limit);
        if (tmpExam && tmpExam.length === 0)
            exampaper_list = exampaper_list.slice(offset - limit, offset);
        if (tmpExam && tmpExam.length > 0)
            exampaper_list = tmpExam;

        if (!exampaper_list.length)
            return resWrapper.succ({total_num: 0,exampapers: []});
        return resWrapper.succ({total_num: total_num, exampapers: exampaper_list});
    } catch (error) {
        return resWrapper.error('HANDLE_ERROR', error.message);
    }
}

function getFavoriteExampaper(req, res){

    var resWrapper = new ResponseWrapper(req, res);
    try {
        var userId = req.user.id;
        var period = req.query.period || '';
        var subject = req.query.subject || '';
        var type = req.query.type || '';
        var offset = parseInt(req.query.offset || 0);
        var limit = parseInt(req.query.limit || 10);
    } catch(err){
        return resWrapper.error('PARAMETERS_ERROR', err.message);
    }

    var cond = {user_id : userId};
    var proj = {exampapers : 1};

    return queryItems2(cond, proj, function(err, items){

        if (err){
            logger.error(err.message);
            return resWrapper.error('HANDLE_ERROR', err.message);
        }

        if(!items || items.length == 0){
            return resWrapper.succ({total_num: 0,exampapers: []});
        }


        items = items.exampapers? items.exampapers: [];
        let exampaper_list = [];
        exampaper_list = _.filter(items, (x) => {
            if (req.query.period && x.period !== req.query.period) {
                return false;
            }
            if (req.query.subject && x.subject !== req.query.subject) {
                return false;
            }
            if (req.query.type && x.type !== req.query.type) {
                return false;
            }
            if (req.query.grade && x.grade !== req.query.grade) {
                return false;
            }
            return true;
        });
        let total_num = exampaper_list.length;
        exampaper_list = exampaper_list.slice(offset, offset + limit);
        if (!exampaper_list.length){
            return resWrapper.succ({total_num: 0,exampapers: []});
        }
        return resWrapper.succ({total_num: total_num, exampapers: exampaper_list});
    });
}

function delFavoriteExampaper(req, res){

    var resWrapper = new ResponseWrapper(req, res);
    try {
        var userId = req.user.id;
        var exam_id = req.params.exampaper_id;
        var cond = {user_id: userId};
        var proj = {exampapers : 1};
    } catch (err){
        return resWrapper.error('PARAMETERS_ERROR', err.message);
    }
    queryItems(cond, proj, function(err, items){
        if(err){
            logger.error(err.message);
            return resWrapper.error('HANDLE_ERROR', err.message);
        }
        items = items.exampapers? items.exampapers: [];
        var exam_list = [];
        for (var i in items){
            var item = items[i];
            if(item.id == exam_id){
                continue;
            }
            exam_list.push(item);
        }

        updateExampaper(exam_list, cond, function(err, res){

            if(err){
                logger.error(err.message);
                return resWrapper.error('HANDLE_ERROR', err.message);
            }

            return resWrapper.succ({id: userId});
        });
    });
}

function getExampaperKeys(req, res){
    var resWrapper = new ResponseWrapper(req, res);
    try {
        var userId = req.user.id;
        var cond = {user_id: userId};
        var proj = {exampapers : 1};
    } catch(err) {
        return resWrapper.error('PARAMETERS_ERROR', err.message);
    }
    queryItems(cond, proj, function(err, items){
        if (err){
            logger.error(err.message);
            return resWrapper.error('HANDLE_ERROR', err.message);
        }

        var examIdList = [];
        items = items && items.exampapers ? items.exampapers : [];
        for (var i in items){
            var item = items[i];
            examIdList.push(item.id);
        }

        return resWrapper.succ(examIdList);
    });
}

const postTable = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let user = await db.collection('favorite').findOne({user_id: req.user.id});
        if (!user) {
            user = {
                _id: new ObjectID(),
                user_id: req.user.id
            };
        }
        if (Array.isArray(user.tw_specifications) === false) {
            user.tw_specifications = [];
        }
        let body = req.body;
        body.id = ObjectID(body.id);
        if (_.find(user.tw_specifications, (x) => x.id.toString() === body.id.toString()) !== undefined) {
            return responseWrapper.succ({});
        }
        let kbTableUrl = URL.format({
            protocol: KBSERVER.protocol,
            hostname: KBSERVER.hostname,
            port: KBSERVER.port,
            pathname: `/kb_api/v2/tw_specifications/${body.id.toString()}/list`,
            search: qs.stringify({
                api_key: KBSERVER.appKey
            })
        });
        let data = await httpGet(kbTableUrl);
        if (data.data) {
            data = data.data;
        }
        if (Array.isArray(data)) {
            data = data[0];
        }
        body.ctime = new Date();
        body.type = data.type;
        body.subject = data.subject;
        body.period = data.period;
        body.grade = data.grade;
        body.province = data.province;
        user.tw_specifications = _.filter(user.tw_specifications, x => x.id.toString() !== body.id.toString());
        user.tw_specifications.push(body);
        await db.collection('favorite').update({_id: user._id}, {$set: user}, {upsert: true});
        return responseWrapper.succ({});
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR');
    }
};

const deleteTable = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let id = ObjectID(req.params.id);
        let user = await db.collection('favorite').findOne({user_id: req.user.id});
        if (!user) {
            user = {
                _id: new ObjectID()
            };
        }
        if (Array.isArray(user.tw_specifications) === false) {
            user.tw_specifications = [];
        }
        if (_.find(user.tw_specifications, (x) => x.id.toString() === id.toString()) === undefined) {
            return responseWrapper.succ({});
        }
        user.tw_specifications = _.filter(user.tw_specifications, x => x.id.toString() !== id.toString());
        await db.collection('favorite').update({_id: user._id}, {$set: user}, {upsert: true});
        return responseWrapper.succ({});
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR');
    }
};

const getTables = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let limit = req.query.limit ? req.query.limit * 1 : 0;
        let offset = req.query.offset ? req.query.offset * 1 : 0;
        let retval = {
            total_num: 0,
            tw_specifications: []
        };
        let user = await db.collection('favorite').findOne({user_id: req.user.id});
        if (!user) {
            return responseWrapper.succ(retval);
        }
        if (Array.isArray(user.tw_specifications) === false) {
            return responseWrapper.succ(retval);
        }

        retval.total_num = user.tw_specifications.length;
        if (retval.total_num === 0) {
            return responseWrapper.succ(retval);
        }
        let collTimeMap = {};
        user.tw_specifications = _.filter(user.tw_specifications, (x) => {
            if (!!x === false) {
                return false;
            }
            if (req.query.type && x.type !== req.query.type) {
                return false;
            }
            if (req.query.period && x.period !== req.query.period) {
                return false;
            }
            if (req.query.subject && x.subject !== req.query.subject) {
                return false;
            }
            // if (req.query.province && x.province !== req.query.province) {
            //     return false;
            // }
            if (req.query.grade && x.grade !== req.query.grade) {
                return false;
            }
            collTimeMap[x.id] = x.ctime;
            return true;
        });
        retval.total_num = user.tw_specifications.length;
        let tableIds = _.map(user.tw_specifications, function(doc){
            return doc.id.toString();
        });
        if (limit > 0) {
            let tmpIds = tableIds.slice(offset, offset + limit);
            if (tmpIds && tmpIds.length === 0)
                tableIds = tableIds.slice(offset - limit, offset);
            if (tmpIds && tmpIds.length > 0)
                tableIds = tmpIds;
        }
        if (tableIds.length === 0) {
            return responseWrapper.succ({
                total_num: retval.total_num,
                specifications: []
            });
        }
        let strIds = tableIds.join(',');
        strIds = encodeURIComponent(strIds);
        let listUrl = URL.format({
            protocol: KBSERVER.protocol,
            hostname: KBSERVER.hostname,
            port: KBSERVER.port,
            pathname: '/kb_api/v2/tw_specifications/'+strIds+'/list',
            search: qs.stringify({
                api_key: req.apiKey
            })
        });

        request({
            url: listUrl
        }, function(err, response, body){
            if (err){
                logger.error(err.message);
                return responseWrapper.error('HANDLE_ERROR', err.message);
            }

            if (response.statusCode !== 200){
                logger.error('GET: '+ listUrl);
                return responseWrapper.error('HANDLE_ERROR', '获取双向细目表列表失败');
            }

            let _body = JSON.parse(body);
            let arrSpec = [];
            for (let i = 0; i < _body.length; i ++) {
                let obj = _body[i];
                if (obj.year)
                    obj.year = Number(obj.year);
                else {
                    if (obj.utime) {
                        let utimes = obj.utime.split('-');
                        obj.year = Number(utimes[0]);
                    } else if (obj.ctime) {
                        let ctimes = obj.ctime.split('-');
                        obj.year = Number(ctimes[0]);
                    } else {
                        obj.year = '';
                    }
                }
                obj.ctime = collTimeMap[obj.id].toLocaleString();
                arrSpec.push(obj);
            }
            arrSpec = arrSpec.sort((a, b) => {
                return new Date(b.ctime) - new Date(a.ctime);
            });
            return responseWrapper.succ({
                total_num: retval.total_num,
                specifications: arrSpec
            });
        });
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR');
    }
};

const getFavoriteCount = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    let retval = {
        question: 0,
        exampaper: 0,
        tw_specification: 0,
        tiku_exampaper: 0,
        erratum: 0,
        album: 0,
        download_exampaper: 0,
        download_question:0
    };
    try {
        // let userId = req.user.id;
        // let cond = {user_id: userId};
        // let data = await db.collection('favorite').findOne(cond);
        // //学段学科过滤
        // req.query.period ? cond.period = req.query.period: '';
        // req.query.subject ? cond.subject = req.query.subject: '';
        // cond.resource_type = 'exampaper';
        // retval.download_exampaper = await db.collection('user_download').count(cond);
        // cond.resource_type = 'question';
        // retval.download_question = await db.collection('user_download').count(cond);
        // delete cond.resource_type;
        // delete cond.period;
        // delete cond.subject;
        // if (!data) {
        //     return responseWrapper.succ(retval);
        // }
        // const filter = (x) => {
        //     let retval = true;
        //     if (req.query.period) {
        //         retval = retval && (req.query.period === x.period);
        //     }
        //     if (req.query.subject) {
        //         retval = retval && (req.query.subject === x.subject);
        //     }
        //     return retval;
        // };
        // data.questions = _.filter(data.questions, filter) || [];
        // data.exampapers = _.filter(data.exampapers, filter) || [];
        // data.tw_specification = _.filter(data.tw_specification, filter) || [];
        // data.albums = _.filter(data.albums, filter) || [];
        // const exampaper_cond = lodash.assign({}, cond);
        // exampaper_cond.display = {$ne: 0};
        // data.tiku_exampaper = await db.collection('exampaper').count(exampaper_cond);
        // retval.tiku_exampaper = data.tiku_exampaper;
        // let search = {
        //     api_key: req.apiKey,
        //     reporter_id: req.user.id,
        //     resource_type: 'question',
        //     query_from:'tiku',
        // };
        // if (req.query.period) {
        //     search.period = req.query.period;
        // }
        // if (req.query.subject) {
        //     search.subject = req.query.subject;
        // }

        // retval.question = data.questions.length;
        // retval.exampaper = data.exampapers.length;
        // retval.album = data.albums.length;
        //获取指定学段、学科的细目表数量
        // let arrTW = [];
        // for (let ix in data.tw_specifications) {
        //     let tw = data.tw_specifications[ix];
        //     if ((req.query.subject && req.query.subject === tw.subject)
        //         && (req.query.period && req.query.period === tw.period)) {
        //         arrTW.push(tw);
        //     }
        // }
        // retval.tw_specification = arrTW.length;
        // let uri = URL.format({
        //     protocol: KBPSERV.protocol,
        //     hostname: KBPSERV.hostname,
        //     port: KBPSERV.port,
        //     pathname: '/kb_api/v2/erratums',
        //     search: qs.stringify(search)
        // });
        // let erratum = await httpGet(uri);
        // retval.erratum = erratum.data.total_num;
        const feedback = await db.collection('feedback').find({user_id: req.user.id, read_status: 1}).count();
        retval.feedback = feedback;
        return responseWrapper.succ(retval);
    } catch (err) {
        return responseWrapper.succ(retval);
    }
};

const JOI_GET_EDU_FILE_LIST = Joi.object({
    offset: Joi.number().required(),
    limit: Joi.number().required(),
    period: Joi.string().optional().allow(''),
    subject: Joi.string().optional().allow(''),
    category: Joi.number().optional().allow(''),
}).unknown(true);

async function getFavoriteEduFiles(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {

        const { offset, limit, period, subject, category } = await JOI_GET_EDU_FILE_LIST.validate(req.query);
        const result = {
            total: 0,
            list: []
        }
        const data = await collection.findOne({user_id: req.user.id});
        if (lodash.isEmpty(data)) return responseWrapper.succ(result);
        let list = data.edu_files || [];
        if (!lodash.size(list)) return responseWrapper.succ(result);
        // 过滤
        list = list.filter(e => {
            if (period && e.period !== period) return false;
            if (subject && e.subject !== subject) return false;
            if (category && e.category !== category) return false;
            return true;
        });
        // 排序
        list.sort((a, b) => {
            return new Date(b.ctime) - new Date(a.ctime)
        });
        result.total = lodash.size(list);
        result.list = lodash.chain(list).slice(offset, offset + limit).map(e => {
            return {
                id: e.id,
                name: e.name,
                category: e.category,
                ctime: e.ctime.getTime()
            }
        }).value();
        return responseWrapper.succ(result);
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
}
async function getFavoriteEduFileKeys(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        let result = [];
        const data = await collection.findOne({user_id: req.user.id});
        if (lodash.isEmpty(data)) return responseWrapper.succ(result);
        const list = data.edu_files || [];
        if (!lodash.size(list)) return responseWrapper.succ(result);
        result = list.map(e => e.id);
        return responseWrapper.succ(result);
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
}

const JOI_POST_EDU_FILE = Joi.object({
    id: Joi.number().required(),
}).unknown(true);

async function postFavoriteEduFiles(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const { id } =  await JOI_POST_EDU_FILE.validate(req.body);
        const file = await server_client.kb.getEduFileById(id);
        if (lodash.isEmpty(file)) return responseWrapper.error('PARAMETERS_ERROR', '资源不存在');
        let data = await collection.findOne({user_id: req.user.id});
        if (lodash.isEmpty(data)) {
            data = await _init_user_favorite(req.user.id);
        }
        const list = data.edu_files || [];
        const curr = list.find(e => e.id === id);
        if (lodash.isEmpty(curr)) {
            list.push({
                id: id,
                name: file.name,
                period: file.period,
                subject: file.subject,
                category: file.category === 'courseware' ? 1 : 2,
                ctime: new Date()
            });
            await collection.updateOne({_id: data._id}, {$set: {edu_files: list}});
        }
        return responseWrapper.succ({ id });
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
}

const JOI_DEL_EDU_FILE = Joi.object({
    id: Joi.number().required(),
}).unknown(true);

async function delFavoriteEduFiles(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const { id } =  await JOI_DEL_EDU_FILE.validate(req.params);
        let data = await collection.findOne({user_id: req.user.id});
        if (!lodash.isEmpty(data)) {
            let list = data.edu_files || [];
            const curr = list.find(e => e.id === id);
            if (!lodash.isEmpty(curr)) {
                list = list.filter(e => e.id !== id);
                await collection.updateOne({_id: data._id}, {$set: {edu_files: list}});
            }
        }
        return responseWrapper.succ({ id });
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
}


const JOI_GET_VIDEO_LIST = Joi.object({
    offset: Joi.number().required(),
    limit: Joi.number().required(),
    grade: Joi.number().optional().allow(''),
    subject: Joi.number().optional().allow(''),
    type: Joi.number().optional().allow(''),
}).unknown(true);

async function getFavoriteMicroCourses(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {

        const { offset, limit, grade, subject, type } = await JOI_GET_VIDEO_LIST.validate(req.query);
        const result = {
            total: 0,
            list: []
        }
        const data = await collection.findOne({user_id: req.user.id});
        if (lodash.isEmpty(data)) return responseWrapper.succ(result);
        let list = data.micro_courses || [];
        if (!lodash.size(list)) return responseWrapper.succ(result);
        // 过滤
        list = list.filter(e => {
            if (grade && e.grade !== grade) return false;
            if (subject && e.subject !== subject) return false;
            if (type && e.type !== type) return false;
            return true;
        });
        // 排序
        list.sort((a, b) => {
            return new Date(b.ctime) - new Date(a.ctime)
        });
        result.total = lodash.size(list);
        result.list = lodash.chain(list).slice(offset, offset + limit).map(e => {
            return {
                id: e.id,
                name: e.name,
                image: e.image,
                grade_name: e.grade_name,
                subject_name: e.subject_name,
                type_name: e.type_name,
                ctime: e.ctime.getTime()
            }
        }).value();
        return responseWrapper.succ(result);
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
}


async function getFavoriteMicroCourseKeys(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        let result = [];
        const data = await collection.findOne({user_id: req.user.id});
        if (lodash.isEmpty(data)) return responseWrapper.succ(result);
        const list = data.micro_courses || [];
        if (!lodash.size(list)) return responseWrapper.succ(result);
        result = list.map(e => e.id);
        return responseWrapper.succ(result);
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
}

const JOI_POST_VIDEO_FILE = Joi.object({
    id: Joi.string().required(),
    // grade: Joi.number().required(),
    // grade_name: Joi.string().required(),
    // subject: Joi.number().required(),
    // subject_name: Joi.string().required(),
    // type: Joi.number().required(),
    // type_name: Joi.string().required(),
}).unknown(true);

async function postFavoriteMicroCourses(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const params =  await JOI_POST_VIDEO_FILE.validate(req.body);
        const { id } =  params;
        const pack = await server_client.sy.getPackageDetail(id);

        if (lodash.isEmpty(pack)) return responseWrapper.error('PARAMETERS_ERROR', '课程不存在');

        let data = await collection.findOne({user_id: req.user.id});
        if (lodash.isEmpty(data)) {
            data = await _init_user_favorite(req.user.id);
        }
        const list = data.micro_courses || [];
        const curr = list.find(e => e.id === id);
        if (lodash.isEmpty(curr)) {
            // 获取筛选项
            const options = await server_client.sy.getOptions();
            const video = {
                id: id,
                name: pack.pack_name || '',
                image: pack.pack_imgurl || '',
                grade: pack.pack_grade,
                grade_name: '',
                subject: pack.pack_subject,
                subject_name: '',
                type: pack.pack_type,
                type_name: '',
                ctime: new Date()
            };
            const grade = options.find(e => e.grade === video.grade);
            if (grade) {
                video.grade_name = grade.grade_name;
                const subject = (grade.subjects || []).find(e => e.subject === video.subject);
                if (subject) {
                    video.subject_name = subject.subject_name;
                    const type = subject.types.find(e => e.type === video.type);
                    if (type) {
                        video.type_name = type.type_name;
                    }
                }
            }
            list.push(video);
            await collection.updateOne({_id: data._id}, {$set: {micro_courses: list}});
        }
        return responseWrapper.succ({ id });
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
}

const JOI_DEL_VIDEO_FILE = Joi.object({
    id: Joi.string().required(),
}).unknown(true);

async function delFavoriteMicroCourses(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const { id } =  await JOI_DEL_VIDEO_FILE.validate(req.params);
        let data = await collection.findOne({user_id: req.user.id});
        if (!lodash.isEmpty(data)) {
            let list = data.micro_courses || [];
            const curr = list.find(e => e.id === id);
            if (!lodash.isEmpty(curr)) {
                list = list.filter(e => e.id !== id);
                await collection.updateOne({_id: data._id}, {$set: {micro_courses: list}});
            }
        }
        return responseWrapper.succ({ id });
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
}

async function stat(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const result = {
            exampaper: 0, // 试卷
            question: 0, // 试题
            edu_file: 0, // 备课资源
            edu_tool: 0, // 教学工具
            micro_course: 0, // 示范好课
        };
        const data = await collection.findOne({user_id: req.user.id});
        if (!_.isEmpty(data)) {
            result.exampaper = _.size(data.exampapers || []);
            result.question = _.size(data.questions || []);
            result.edu_file = _.size(data.edu_files || []);
            result.edu_tool = _.size(data.edu_tools || []);
            result.micro_course = _.size(data.micro_courses || []);
        }
        return responseWrapper.succ(result);
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
}


const JOI_GET_EDU_TOOL_LIST = Joi.object({
    offset: Joi.number().required(),
    limit: Joi.number().required(),
    // period: Joi.string().optional().allow(''),
    // subject: Joi.string().optional().allow(''),
    // category: Joi.number().optional().allow(''),
}).unknown(true);

async function getFavoriteEduTools(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {

        const { offset, limit } = await JOI_GET_EDU_TOOL_LIST.validate(req.query);
        const result = {
            total: 0,
            list: []
        }
        const data = await collection.findOne({user_id: req.user.id});
        if (lodash.isEmpty(data)) return responseWrapper.succ(result);
        let list = data.edu_tools || [];
        if (!lodash.size(list)) return responseWrapper.succ(result);
        // 排序
        list.sort((a, b) => {
            return new Date(b.ctime) - new Date(a.ctime)
        });
        result.total = lodash.size(list);
        result.list = lodash.chain(list).slice(offset, offset + limit).map(e => {
            return {
                id: e.id,
                name: e.name,
                size: e.size || 0,
                suffix: e.suffix,
                // category: e.category,
                ctime: e.ctime.getTime()
            }
        }).value();
        return responseWrapper.succ(result);
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
}
async function getFavoriteEduToolKeys(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        let result = [];
        const data = await collection.findOne({user_id: req.user.id});
        if (lodash.isEmpty(data)) return responseWrapper.succ(result);
        const list = data.edu_tools || [];
        if (!lodash.size(list)) return responseWrapper.succ(result);
        result = list.map(e => e.id);
        return responseWrapper.succ(result);
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
}

const JOI_POST_EDU_TOOL = Joi.object({
    id: Joi.number().required(),
}).unknown(true);

async function postFavoriteEduTools(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const { id } =  await JOI_POST_EDU_TOOL.validate(req.body);
        const file = await server_client.kb.getEduToolById(id);
        if (lodash.isEmpty(file)) return responseWrapper.error('PARAMETERS_ERROR', '资源不存在');
        let data = await collection.findOne({user_id: req.user.id});
        if (lodash.isEmpty(data)) {
            data = await _init_user_favorite(req.user.id);
        }
        const list = data.edu_tools || [];
        const curr = list.find(e => e.id === id);
        if (lodash.isEmpty(curr)) {
            list.push({
                id: id,
                name: file.name,
                size: file.size,
                suffix: file.suffix,
                ctime: new Date()
            });
            await collection.updateOne({_id: data._id}, {$set: {edu_tools: list}});
        }
        return responseWrapper.succ({ id });
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
}

const JOI_DEL_EDU_TOOL = Joi.object({
    id: Joi.number().required(),
}).unknown(true);

async function delFavoriteEduTools(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const { id } =  await JOI_DEL_EDU_TOOL.validate(req.params);
        let data = await collection.findOne({user_id: req.user.id});
        if (!lodash.isEmpty(data)) {
            let list = data.edu_tools || [];
            const curr = list.find(e => e.id === id);
            if (!lodash.isEmpty(curr)) {
                list = list.filter(e => e.id !== id);
                await collection.updateOne({_id: data._id}, {$set: {edu_tools: list}});
            }
        }
        return responseWrapper.succ({ id });
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
}

async function _init_user_favorite(user_id) {
    const data = {
        user_id: user_id,
        questions: [],
        exampapers: [],
        tw_specifications: [],
        edu_files: [],
        micro_courses: [],
        edu_tools: [],
    }
    const insert_result = await collection.insertOne(data);
    data._id = insert_result.insertedId.toString();
    return data;
}

module.exports = {
    getQuesInfo: getQuesInfo,
    postQuestion: postQuestion,
    getQuesKeys: getQuesKeys,
    delFavoriteQues: delFavoriteQues,
    postExampaper: postExampaper,
    // getFavoriteExampaper: getFavoriteExampaper,
    getFavoriteExampaper: getFavoriteExampaperNew,
    delFavoriteExampaper: delFavoriteExampaper,
    getExampaperKeys: getExampaperKeys,
    postTable,
    getTables,
    deleteTable,
    getFavoriteCount,
    postExampaperAsync,
    getFavoriteEduFiles,
    getFavoriteEduFileKeys,
    postFavoriteEduFiles,
    delFavoriteEduFiles,
    getFavoriteMicroCourses,
    getFavoriteMicroCourseKeys,
    postFavoriteMicroCourses,
    delFavoriteMicroCourses,
    stat,
    getFavoriteEduTools,
    getFavoriteEduToolKeys,
    postFavoriteEduTools,
    delFavoriteEduTools,
};


