
const _ = require('lodash');
const mongodber = require('../utils/mongodber');
const db = mongodber.use('tiku');
const yjClient = require('../../bin/yjClient');
const subjectUtils = require('../utils/subject_utils');
const enums = require('../../bin/enum');
const client = require('../client');

const defaultPeriod = '高中';
const defaultSubject = '数学';
const DEFAULT_EXPIRE_MONTH = 3;

module.exports = {
    initUser,
    getCookieUser,
    getUserById,
    insertUser,
    updateUser,
    getSubjectInfoByTeacher,
    getSubjectInfoByYjTeacher,
    getTeacherInfoById,
}

function initUser(user_id, role = enums.UserType.TEACHER) {
    const user = {
        _id: Number(user_id),
        is_vip: false,
        role: enums.UserType.TEACHER,
        curr: {
            period: defaultPeriod,
            subject: defaultSubject,
        },
        trace: {
            period: defaultPeriod,
            subject: defaultSubject,
        },
        last_time: new Date(),
        finished: 1,
        ctime: new Date(),
        utime: new Date(),
    };
    return user;
}

async function getCookieUser(user) {
    const cookie_user = {
        userId: parseInt(user._id),
        id: parseInt(user._id),
        ucId: user.uc_id,
        name: user.name,
        schoolId: user.sch_id,
        schoolName: user.sch_name,
        province: user.province,
        city: user.city,
        role: user.role,
        grade: user.grade,
        avatar: user.avatar,
        need_bind: user.need_bind,
        qxid: user.qxid,
        // expireMonth: DEFAULT_EXPIRE_MONTH,
        // isVip: user.is_vip || false,
        // startTime: user.start_time && user.start_time.getTime(),
        // expiredTime: user.expired_time && user.expired_time.getTime(),
    }
    // if (cookie_user.isVip) {
    //     if (Object.values(enums.MemberType).includes(user.vip_type)) {
    //         cookie_user.vipType = user.vip_type;
    //     } else {
    //         cookie_user.vipType = enums.MemberType.TIKU_PERSONAL;
    //     }
    // }
    // if (user.logo) cookie_user.logo = user.logo;
    // if (user.type) cookie_user.type = user.type;
    // if (user.nav_bg_color) cookie_user.nav_bg_color = user.nav_bg_color;
    return cookie_user;
}

async function getUserById(id) {
    return await db.collection('user').findOne({_id: id});
}

async function insertUser(user) {
    if (!user._id) throw new Error('用户信息异常');
    await db.collection('user').insertOne(user);
}

async function updateUser(id, user) {
    await db.collection('user').updateOne({_id: id}, {$set: user});
}

async function getSubjectInfoByTeacher(teacher) {
    if (_.isEmpty(teacher)) return;
    const school_id = teacher.schoolId;
    let period, subject, grade;
    for (const cls of teacher.classes || []) {
        if (!grade && cls.grade) {
            grade = cls.grade;
        }
        if (!subject && _.size(cls.roles)) {
            for (let sub of cls.roles) {
                sub = subjectUtils.regularSubject(sub);
                if (sub) {
                    subject = sub;
                    break;
                }
            }
        }
    }
    if (grade) {
        const yjGradeMapping = await db.collection('yj_grade_mapping').findOne({yj_grade: grade});
        if (!_.isEmpty(yjGradeMapping)) {
            grade = yjGradeMapping.grade;
            period = yjGradeMapping.period;
        }
    }
    if (!period && school_id) { // 根据学制设置学段
        const edu = await yjClient.getSchoolEdu(school_id);
        const eduSystem = _.get(edu, 'eduSystem', '');
        if (eduSystem) {
            const periods = enums.ZHISHIXUEDUAN[eduSystem];
            if (_.size(periods)) {
                period = periods[periods.length - 1];
            }
        }
    }
    if (!period) period = defaultPeriod;
    if (!subject) subject = defaultSubject;
    if (period) {
        if (!subject) subject = defaultSubject;
        if (!grade) {
            const list = await db.collection('yj_grade_mapping').find({period: period}).toArray();
            if (_.size(list)) {
                grade = list[list.length -1].grade;
            }
        }
    }
    return { period, subject, grade };
}

async function getTeacherInfoById(user_id) {
    if (!user_id) return;
    const teacher = await client.yj.getUserInfo(user_id);
    const result = await getSubjectInfoByYjTeacher(teacher);
    return result;
}
/**
 * 根据阅卷教师获取教师标准信息
 * @param teacher
 * @returns {Promise<{phone: *, schoolId: *, name: *, schoolName: *, userId: *}>}
 */
async function getSubjectInfoByYjTeacher(teacher) {
    if (_.isEmpty(teacher)) return;
    const subjectInfo = await _get_yj_subject_info(teacher);
    const result = {
        userId: teacher.userId,
        name: teacher.name,
        phone: teacher.phone,
        schoolId: teacher.schoolId,
        schoolName: teacher.schoolName,
        ...subjectInfo
    }
    return result;
}

async function _get_yj_subject_info(teacher) {
    let subject, grade;
    const roles = teacher['[role]'] || [];
    for (const role of roles) {
        for (const nj of role['[nianji]'] || []) { // 年级
            if (_.size(nj)) grade = nj;
        }
        for (const xk of role['[xueke]'] || []) { // 学科
            if (!_.size(xk)) continue;
            const tmp = subjectUtils.regularSubject(xk);
            if (tmp) {
                subject = tmp;
            }
        }
    }
    const subjectInfo = await _get_subject_info(teacher.schoolId, grade, subject);
    return subjectInfo;
}

async function _get_subject_info(school_id, grade, subject) {
    const result = {};
    result.subject = subject || defaultSubject;
    if (grade) {
        const yjGradeMapping = await db.collection('yj_grade_mapping').findOne({yj_grade: grade});
        if (!_.isEmpty(yjGradeMapping)) {
            result.grade = yjGradeMapping.grade;
            result.period = yjGradeMapping.period;
        }
    }
    if (!result.period && school_id) { // 根据学制设置学段
        const edu = await yjClient.getSchoolEdu(school_id);
        const eduSystem = _.get(edu, 'eduSystem', '');
        if (eduSystem) {
            const periods = enums.ZHISHIXUEDUAN[eduSystem];
            if (_.size(periods)) {
                result.period = periods[periods.length - 1];
            }
        }
    }
    if (!result.period) result.period = defaultPeriod;
    if (!result.grade) {
        const list = await db.collection('yj_grade_mapping').find({period: result.period}).toArray();
        if (_.size(list)) {
            result.grade = list[list.length -1].grade;
        }
    }
    return result;
}

