const config = require('config');
const moment = require('moment');
const Joi = require('@hapi/joi');
const _ = require('lodash');


const ResponseWrapper = require('../../middlewares/response_wrapper');
const mongodber = require('../../utils/mongodber');
const db = mongodber.use('tiku');
const ObjectId = require('mongodb').ObjectId;
const logger = require('../../utils/logger');
const utils = require('../../utils/utils');
const enums = require('../../../bin/enum');
const schema = require('../../../bin/schema');
const client = require('../../client');

const JOI_POST_CATEGORY = Joi.object({
    type: Joi.number().required(),
    name: Joi.string().required(),
    topic_id: Joi.string().required().allow(''),
    view_times: Joi.number().optional().default(0),
});

async function initCategory(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const params = await JOI_POST_CATEGORY.validate(req.body);
        const doc = await db.collection(schema.resource_download_category).findOne({type: params.type});
        if (!_.isEmpty(doc)) return responseWrapper.succ({id: doc._id.toString()});
        params.ctime = new Date();
        params.utime = new Date();
        const insert = await db.collection(schema.resource_download_category).insertOne(params);
        return responseWrapper.succ({id: insert.insertedId.toString()});
    } catch (e) {
        logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

async function getCategoryInfo(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const list = await db.collection(schema.resource_download_category).find({}).toArray();
        const result = (list || []).map(e => {
            return {
                id: e._id.toString(),
                name: e.name,
                type: e.type,
                topic_id: e.topic_id,
                view_times: e.view_times
            };
        });
        return responseWrapper.succ(result);
    } catch (e) {
        logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}


const JOI_ADD_CATEGORY_VIEW_TIMES = Joi.object({
    id: Joi.string().required()
});

async function addCategoryViewTimes(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const { id } = await JOI_ADD_CATEGORY_VIEW_TIMES.validate(req.params);
        const data = await db.collection(schema.resource_download_category).findOne({_id: new ObjectId(id)});
        let view_times = 0;
        if (!_.isEmpty(data)) {
            view_times = data.view_times + 1;
            await db.collection(schema.resource_download_category).updateOne({_id: new ObjectId(id)}, {$set: {view_times: view_times, utime: new Date()}});
        }
        return responseWrapper.succ({view_times: view_times});
    } catch (e) {
        logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

async function getCartInfo(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const user_id = req.user.id;
        const data = await db.collection(schema.user_resource_cart).findOne({user_id: user_id});
        const result = {
            total: data && _.size(data.items) || 0,
            list: []
        }
        if (result.total) {
            result.list = data.items;
        }
        return responseWrapper.succ(result);
    } catch (e) {
        logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const JOI_PUT_CART = Joi.object({
    type: Joi.number().required(),
    resource_id: [Joi.number(), Joi.string()]
});

async function addResourceToCart(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const { type, resource_id } = await JOI_PUT_CART.validate(req.body);
        const user_id = req.user.id;
        let cart = await db.collection(schema.user_resource_cart).findOne({user_id: user_id});
        const resource_category = await db.collection(schema.resource_download_category).findOne({type: type});
        if (_.isEmpty(resource_category)) return responseWrapper.error('NULL_ERROR', '数据分类不存在');
        let resource = null;
        // 1-期中真卷 2-期中模拟 3-单元分层 4-考后讲评 5-月考 6-PPT课件 7-教学工具
        if (type === 4) { // 考后讲评不存在专题数据
            const arr = resource_id.split('-'); // 0: 科目ID，1-学校ID，2-分类
            if (_.size(arr) !== 3) return responseWrapper.error('NULL_ERROR', '资源数据不存在');
            const paper_id = `${arr[0]}-${arr[1]}`;
            const category = Number(arr[2]);
            const exam = await db.collection(schema.exam_teacher_paper).findOne({user_id: user_id, paper_id: paper_id});
            let exam_papers = exam && exam.papers || [];
            if (!_.size(exam_papers)) return responseWrapper.error('NULL_ERROR', '资源数据不存在');
            let cat_resource = null;
            if (category !== 6) {
                const paper = exam_papers.find(e => e.category === category);
                if (_.isEmpty(paper)) return responseWrapper.error('NULL_ERROR', '资源数据不存在');
                cat_resource = await db.collection(schema.exampaper).findOne({_id: new ObjectId(paper.id)});
            } else { // 教师讲义
                cat_resource = {
                    name: '教师讲义',
                    category: 6
                }
            }
            resource = {
                type,
                resource_type: 'exam_consolidate',
                resource_id: resource_id,
                resource_name: cat_resource.name,
                resource_image: '',
                resource_info: {
                    exam_id: exam.exam_id,
                    category: category
                }
            };
        } else if (type === 6) { // 课件
            const edu_file = await client.kb.getEduFileById(resource_id);
            resource = {
                type,
                resource_type: 'edu_file',
                resource_id: resource_id,
                resource_name: edu_file.name,
                resource_image: '',
                resource_info: {
                    category: edu_file.category,
                    host: edu_file.host,
                    url: edu_file.url,
                }
            }
        } else {
            const page = await db.collection(schema.topic_page).findOne({_id: new ObjectId(resource_category.topic_id)});
            resource = await db.collection(schema.topic_data).findOne({topic_id: {$in: page.topics}, resource_id: resource_id});
        }
        if (_.isEmpty(resource)) return responseWrapper.error('NULL_ERROR', '资源数据不存在');

        const date = new Date();
        const data = {
            type: type,
            resource_type: resource.resource_type, // 资源类型: exampaper, question, word, excel
            resource_id: resource.resource_id, // 资源ID
            resource_name: resource.resource_name, // 资源名字
            resource_image: resource.resource_image || '', // 图片
            resource_info: resource.resource_info,
        };
        if (_.isEmpty(cart)) {
            cart = {
                user_id: user_id,
                items: [data],
                ctime: date,
                utime: date
            };
            await db.collection(schema.user_resource_cart).insertOne(cart);
        } else {
            const tmp = cart.items.find(e => e.type === type && e.resource_id === resource_id);
            if (_.isEmpty(tmp)) {
                cart.items.push(data);
                await db.collection(schema.user_resource_cart).updateOne({_id: cart._id}, {$set: {items: cart.items, utime: date}});
            }
        }
        return responseWrapper.succ('');
    } catch (e) {
        logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const JOI_DELETE_CART_RESOURCE = Joi.array().items(Joi.object({
    type: Joi.number().required(),
    resource_id: [Joi.number(), Joi.string()]
})).required().min(1);

async function deleteCartResource(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const user_id = req.user.id;
        const params = await JOI_DELETE_CART_RESOURCE.validate(req.body);
        const cart = await db.collection(schema.user_resource_cart).findOne({user_id: user_id});
        if (cart && _.size(cart.items)) {
            const items = cart.items.filter(e => !params.find(p => p.type === e.type && p.resource_id === e.resource_id));
            await db.collection(schema.user_resource_cart).updateOne({_id: cart._id}, {$set: {items: items, utime: new Date()}});
        }
        return responseWrapper.succ('');
    } catch (e) {
        logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}



module.exports = {
    initCategory,
    getCategoryInfo,
    addCategoryViewTimes,
    getCartInfo,
    addResourceToCart,
    deleteCartResource,
}
