const _ = require('lodash');

const FetchOrder = ['数学', '语文', '英语', '生物', '物理', '化学', '政治', '历史', '地理'];

const FetchMap = {
    '语文': ['语文'],
    '数学': ['数', '数学（文）', '数学（理）','数,文', '数,理'],
    '英语': ['英'],
    '生物': ['生'],
    '物理': ['物'],
    '化学': ['化'],
    '政治': ['政', '思想品德', '思品', '道德与法治', '道德与法制', '道法'],
    '历史': ['史'],
    '地理': ['地']
};

// const defaultSubject = '数学';
/**
 * dw获得的科目信息转换为数据库中科目枚举。'数学'：1
 * @param subject
 * @returns {string}
 */
function regularSubject (subject) {
    let other = '';
    if (!subject) return other;
    if (FetchOrder.includes(subject)) return subject;
    for (let i = 0; i < FetchOrder.length; i++) {
        let sub = FetchOrder[i];
        if (checkSubjectFetch(sub, subject)) {
            return sub;
        }
    }
    return other;
}

function checkSubjectFetch (subject, teacherSubject) {
    if (subject === '物理' && _.includes(teacherSubject, '生')) {
        return false;
    }
    let fetchList = FetchMap[subject] || [];
    for (let i = 0; i < fetchList.length; i++) {
        let matchWords = _.split(fetchList[i], ',');
        let allMatch = true;
        for (let word of matchWords) {
            if (!_.includes(teacherSubject, word)) {
                allMatch = false;
            }
        }
        if (allMatch) return allMatch;
    }
    return false;
}

module.exports = {
    regularSubject,
}
