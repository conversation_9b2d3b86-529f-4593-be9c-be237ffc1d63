const logger = require('../../utils/logger');
const utils = require('../../utils/utils');
const enums = require('../../../bin/enum');
const _ = require('lodash');
const ObjectID = require("mongodb").ObjectID;
const Joi = require('@hapi/joi');

const ResponseWrapper = require('../../middlewares/response_wrapper');

const mongodber = require('../../utils/mongodber');
const db = mongodber.use('tiku');
const db_open = mongodber.use('tiku_open');
const collection = db_open.collection(enums.OpenSchema.exampaper_question_template);


module.exports = {
    list,
    create,
    remove,
    getSysList,
}

const JOI_LIST = Joi.object({
    period: Joi.string().required(), //  学段
    subject: Joi.string().required(), // 科目
}).unknown(true);


async function getSysList () {
    const sys_list = await collection.find({ type: enums.QuestionTemplate.SYS }).toArray();
    for (const item of sys_list) {
        item['template_id'] = item._id.toString();
        delete item._id;
        delete item.ctime;
        delete item.utime;
        delete item.user_id;
    }
    return sys_list;
}

async function list (req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const { period, subject } = await JOI_LIST.validate(req.query);
        // 查询自定义
        const result = await collection.find({ user_id: utils.formatUserId(req.user.id), period, subject, type: enums.QuestionTemplate.CUSTOM }).toArray();
        // 处理数据
        if (_.size(result)) {
            for (const item of result) {
                item['template_id'] = item._id.toString();
                delete item._id;
                delete item.ctime;
                delete item.utime;
                delete item.user_id;
            }
        }
        // 返回结果
        return responseWrapper.succ(result);
    } catch (err) {
        logger.error(err);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
}

const JOI_CREATE = Joi.object({
    name: Joi.string().required(), // 名称
    period: Joi.string().required(), //  学段
    subject: Joi.string().valid(...Object.values(enums.Subject)).required(), // 科目
    blocks: Joi.array().items(Joi.object({
        type: Joi.string().required(),
        num: Joi.number().required(),
    })).required().min(1),
});

async function create (req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const nowDate = new Date();
        const insertData = await JOI_CREATE.validate(req.body);
        insertData.user_id = utils.formatUserId(req.user.id);
        insertData.type = enums.QuestionTemplate.CUSTOM;
        insertData.ctime = nowDate;
        insertData.utime = nowDate;
        const insertResult = await collection.insertOne(insertData);
        return responseWrapper.succ({ template_id: insertResult.insertedId.toString() });
    } catch (err) {
        logger.error(err);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
}

const JOI_REMOVE = Joi.object({
    template_id: Joi.string().required(), //  模板ID
});

async function remove (req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const { template_id } = await JOI_REMOVE.validate(req.params);
        const data = await collection.findOne({_id: new ObjectID(template_id) });
        if (_.isEmpty(data) || data.user_id !== utils.formatUserId(req.user.id)) return responseWrapper.error('NULL_ERROR');
        await collection.deleteOne({_id: new ObjectID(template_id)});
        return responseWrapper.succ('');
    } catch (err) {
        logger.error(err);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
}
