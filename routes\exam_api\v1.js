/**
 * 考后巩固
 */

const express = require('express');
const router = express.Router();
const exam = require('../../modules/exam_api/v1/index');

// 获取教师考试列表
router.get('/list', exam.getList);

// 获取
router.get('/detail', exam.getExamDetail);

// 获取班级考试信息
router.get('/class', exam.getClassDetail);

// 组卷
router.post('/paper', exam.createPaper);

// 保存细目表
router.post('/table', exam.createTable);

module.exports = router;
