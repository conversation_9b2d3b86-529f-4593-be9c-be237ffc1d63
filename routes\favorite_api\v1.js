var express = require('express');
var router = express.Router();

var favorite = require('../../modules/favorite_api/v1/favorite_api');

//收藏试题
router.post('/favorite/questions/', favorite.postQuestion);
//根据对应条件获取收藏试题
router.get('/favorite/questions/', favorite.getQuesInfo);
//获取收藏所有试题的id
router.get('/favorite/questions/keys/', favorite.getQuesKeys);
//取消收藏试题
router.delete('/favorite/questions/', favorite.delFavoriteQues);

//收藏试卷
router.post('/favorite/exampapers/', favorite.postExampaper);
//根据对应条件获取收藏试卷
router.get('/favorite/exampapers/', favorite.getFavoriteExampaper);
//获取收藏所有试卷的id
router.get('/favorite/exampapers/keys/', favorite.getExampaperKeys);
//取消收藏试卷
router.delete('/favorite/exampapers/', favorite.delFavoriteExampaper);

// 收藏备课资源
router.post('/favorite/edu_files/', favorite.postFavoriteEduFiles);
//根据对应条件获取收藏试卷
router.get('/favorite/edu_files/', favorite.getFavoriteEduFiles);
//获取收藏所有试卷的id
router.get('/favorite/edu_files/keys/', favorite.getFavoriteEduFileKeys);
//取消收藏试卷
router.delete('/favorite/edu_files/:id', favorite.delFavoriteEduFiles);


// 收藏备课资源
router.post('/favorite/micro_courses/', favorite.postFavoriteMicroCourses);
//根据对应条件获取收藏试卷
router.get('/favorite/micro_courses/', favorite.getFavoriteMicroCourses);
//获取收藏所有试卷的id
router.get('/favorite/micro_courses/keys/', favorite.getFavoriteEduFileKeys);
//取消收藏试卷
router.delete('/favorite/micro_courses/:id', favorite.delFavoriteMicroCourses);

// 用户收藏统计
router.get('/favorite/stat', favorite.stat);

module.exports = router;
