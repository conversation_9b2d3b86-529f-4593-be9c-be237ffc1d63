const _ = require('lodash');
const URL = require('url');
const config = require('config');
const YJSERVER = config.get('YJ_API_SERVER');
const axios = require('axios');
const logger = require('../modules/utils/logger');

module.exports = {
    getSchoolEdu,
};

/**
 * 获取学校学制
 * @param schoolId 学校ID
 * @returns {Promise<*>}
 */
async function getSchoolEdu (schoolId) {
    let url = URL.format({
        protocol: YJSERVER.protocol,
        hostname: YJSERVER.hostname,
        pathname: '/v0/school/setting',
        port: YJSERVER.port,
    });
    let opstions = { headers: {}, timeout: 50000 };
    let result = await axios.get(url, { params: { schoolId: schoolId, fields: 'eduSystem' } }, opstions);
    if (!result.data || result.data.code !== 0) {
        logger.error(`获取学校学制失败,URL:[${url}] 学校ID：[${schoolId}]`);
        throw new Error('获取学校学制失败');
    }
    return result.data.data;
}
