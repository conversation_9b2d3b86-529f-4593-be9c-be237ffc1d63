const client = require('zipkin-middleware').client;
const _ = require('underscore');
const config = require('config');
const KBAPISERV = config.get('KB_API_SERVER');
const qs = require('qs');
const uri = require('url');

// 扩展学段基准值
const stageMap = {
    '小学': 0,   // 小学1-6年级
    '初中': 6,   // 初中7-9年级
    '高中': 9,   // 高中10-12年级
    '中职': 12  // 中职13-15年级
};

// 扩展数字映射
const gradeMap = {
    '一年级':1, '二年级':2, '三年级':3, '四年级':4, '五年级':5, '六年级':6,
    '七年级':7, '八年级':8, '九年级':9, '高一':10, '高二':11, '高三':12
};

function parseEntry(period, grade) {
    const isSpecial = grade.includes('专题') || grade.includes('全部');
    return {
        original: grade,
        isSpecial: isSpecial,
        grade: isSpecial ? 0 :(stageMap[period] || 0) + (gradeMap[grade.replace(/[上下]$/, '')] || 0),
        term: isSpecial ? 0 : grade.match(/([上下])$/)[1] === '上' ? 1 : 2
    };
}

function sortGrade(period, grades) {
    return grades
        .map(item => {
            item.entry = parseEntry(period, item.name);
            return item;
        })
        .sort((a, b) => {
            if (a.entry.isSpecial !== b.entry.isSpecial) return b.entry.isSpecial - a.entry.isSpecial;
            if (a.entry.grade !== b.entry.grade) return a.entry.grade - b.entry.grade;
            return a.entry.term - b.entry.term;
        })
        .map(item => {
            delete item.entry;
            return item;
        });
}

// 完整的筛选结构
const loadCategoryFiltersAll = (categorys, filters) => {
    try {
        for (let ix in filters) {
            if (filters.hasOwnProperty(ix)) {
                filters[ix] = _.object(_.pluck(filters[ix], 'grade'), _.pluck(filters[ix], 'type'));
            }
        }
        let category = categorys.category;
        for (let px in category.children) {
          if (category.children.hasOwnProperty(px)) {
            let period = category.children[px];
            for (let sx in period.children) {
                if (period.children.hasOwnProperty(sx)) {
                    let subject = period.children[sx];
                    for (let pv in subject.children) {
                        if (subject.children.hasOwnProperty(pv)) {
                            let pressVersion = subject.children[pv];
                            for (let gx in pressVersion.children) {
                                if (pressVersion.children.hasOwnProperty(gx)) {
                                    let grade = pressVersion.children[gx];
                                    grade.children = [{
                                        key: 'type',
                                        name: '全部',
                                    }];
                                    delete grade.id;
                                    for (let tx in filters[period.name][grade.name]) {
                                        if (filters[period.name][grade.name].hasOwnProperty(tx)) {
                                            let type = filters[period.name][grade.name][tx];
                                            grade.children.push({
                                                key: 'type',
                                                name: type
                                            });
                                        }
                                    }
                                }
                            }
                            pressVersion.children = sortGrade(period.name, pressVersion.children);
                            pressVersion.children[0].name = '全部';
                            pressVersion.children[0].children = _.uniq(
                                _.flatten(
                                _.pluck(pressVersion.children, 'children')),
                                (x) => x.name);
                        }
                    }
                    subject.children.unshift({
                        key: 'press_version',
                        name: '全部',
                        children: []
                    });
                    subject.children[0].children = _.uniq(
                        _.flatten(
                        _.pluck(subject.children, 'children')),
                        (x) => x.name);
                    subject.children[0].children = sortGrade(period.name, subject.children[0].children);

                }
            }
          }
        }
    } catch (err) {
        throw err;
    }
};

// 并不是完整的筛选结构是简单的，少了一层
const loadCategoryFiltersSimple = (categorys, filters) => {
    try {
        for (let ix in filters) {
            if (filters.hasOwnProperty(ix)) {
                filters[ix] = _.object(_.pluck(filters[ix], 'grade'), _.pluck(filters[ix], 'type'));
            }
        }
        let category = categorys.category;
        for (let px in category.children) {
          if (category.children.hasOwnProperty(px)) {
            let period = category.children[px];
            for (let sx in period.children) {
                if (period.children.hasOwnProperty(sx)) {
                    let subject = period.children[sx];
                    subject.children = _.uniq(_.flatten(_.pluck(subject.children, 'children')), x => x.name);
                    for (let gx in subject.children) {
                        if (subject.children.hasOwnProperty(gx)) {
                            let grade = subject.children[gx];
                            grade.children = [];
                            delete grade.id;
                            for (let tx in filters[period.name][grade.name]) {
                                if (filters[period.name][grade.name].hasOwnProperty(tx)) {
                                    let type = filters[period.name][grade.name][tx];
                                    grade.children.push({
                                        key: 'type',
                                        name: type
                                    });
                                }
                            }
                        }
                    }
                    let all = (list => {
                        let all = _.uniq(
                            _.flatten(
                            _.pluck(list, 'children')),
                            (x) => x.name);
                        all.push({
                            key: 'type',
                            name: '其他'
                        });
                        return all;
                    })(subject.children);
                    subject.children.shift();
                    // ---
                    for (let gx in subject.children) {
                        if (subject.children.hasOwnProperty(gx)) {
                            let grade = subject.children[gx];
                            grade.children = all;
                        }
                    }

                }
            }
          }
        }
    } catch (err) {
        throw err;
    }
};


const getExampaperCategory = async (period, subject, all) => {
    try {
        let query = {};
        if (period) {
            query.period = period;
        }
        if (subject) {
            query.subject = subject;
        }
        query.api_key = KBAPISERV.appKey;
        let categoryURI = uri.format({
            protocol: KBAPISERV.protocol,
            hostname: KBAPISERV.hostname,
            port: KBAPISERV.port,
            pathname: '/kb_api/v2/exampapers/categorys',
            search: qs.stringify(query)
        });

        let filterURI = uri.format({
            protocol: KBAPISERV.protocol,
            hostname: KBAPISERV.hostname,
            port: KBAPISERV.port,
            pathname: '/kb_api/v2/exampapers/filters',
            search: qs.stringify(query)
        });
        let categorys = await client.axios.get(categoryURI);
        categorys = categorys.data;
        let filters = await client.axios.get(filterURI);
        filters = filters.data;
        filters = _.object(_.pluck(filters, 'period'), _.pluck(filters, 'grades'));
        if (!!all === true) {
            loadCategoryFiltersAll(categorys, filters);
        } else {
            loadCategoryFiltersSimple(categorys, filters);
        }
        return categorys;
    } catch (err) {
        return null;
    }
};

module.exports = {
    getExampaperCategory,
};
