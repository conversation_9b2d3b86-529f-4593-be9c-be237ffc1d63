const moment = require('moment');
const _ = require('lodash');
const mongodber = require('../utils/mongodber');
const db = mongodber.use('tiku');
const db_tiku_open = mongodber.use('tiku_open');
const ObjectId = require('mongodb').ObjectId;
const enums = require('../../bin/enum');
const schema = require('../../bin/schema');
const client = require('../client');

module.exports = {
    getById
}

async function getById(resource) {
    const {id, type, parent_id} = resource;
    let result = null;
    switch (type) {
        case enums.ResourceType.QUESTION:
            const arr = await client.kb.getQuestions([id]);
            if (_.size(arr)) {
                result = arr[0];
            }
            break;
        case enums.ResourceType.EXAMPAPER:
            result = await client.kb.getExampaper(id);
            break;
        case enums.ResourceType.ASSEMBLE_EXAMPAPER:
            // result = await collection(schema.exampaper).findOne({_id: new ObjectId(id)});
            result = await db_tiku_open.collection(enums.OpenSchema.user_paper).findOne({_id: new ObjectId(id)});
            if (!_.isEmpty(result)) {
                result.id = result._id.toString();
            }
            break;
        case enums.ResourceType.EDU_ASSISTANT_FILE:
            result = await client.kb.request('kb_api/v2/education_assistant_files/:id', {pathParams: {id: id}});
            break;
        case enums.ResourceType.RESOURCE_ALBUM:
            result = await collection(schema.resource_album_data).findOne({_id: new ObjectId(id)});
            if (!_.isEmpty(result)) {
                result.id = result._id.toString();
            }
            const album = await collection(schema.resource_album).findOne({_id: new ObjectId(parent_id)});
            result.period = album.period;
            result.grade = album.grade;
            result.subject = album.subject;
            break;
        default:
            result = null;
            break;
    }
    return result;
}

function collection(name) {
    return db.collection(name);
}

