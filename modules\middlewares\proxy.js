const proxyMiddleware = require('http-proxy-middleware');
const url = require('url');
const config = require('config');
const ResponseWrapper = require('./response_wrapper');
const logger = require('../utils/logger');
const client = require('../client');

async function jiaoyanProxy(path, app) {
  app.use(path, async (req, res, next) => {
    try {
      // 1. 获取 unifyToken
      const unifyToken = await client.yj.getUnifyToken(req.user.id);
      if (!unifyToken) {
        const resWrapper = new ResponseWrapper(req, res);
        return resWrapper.error('AUTH_ERROR', '获取统一认证令牌失败');
      }

      // 2. 使用 http-proxy-middleware 转发请求
      const exampleProxy = proxyMiddleware({
        target: url.format({
          protocol: config.get('JIAOYAN_SERVER').protocol,
          hostname: config.get('JIAOYAN_SERVER').hostname,
          port: config.get('JIAOYAN_SERVER').port,
        }),
        changeOrigin: true,
        onProxyReq: (proxyReq, req, res) => {
          // 在代理请求中添加必要的头信息
          proxyReq.setHeader('cookie', `unify_sid=${unifyToken}`);
          
          // 处理 body 数据
          if (req.body && Object.keys(req.body).length > 0) {
            const bodyData = JSON.stringify(req.body);
            // 更新 Content-Length
            proxyReq.setHeader('Content-Length', Buffer.byteLength(bodyData));
            
            // 写入请求体
            proxyReq.write(bodyData);
            proxyReq.end();
          }
        },
        pathRewrite: {
          '^/jiaoyan_api': '' // 移除路径前缀
        },
        // 添加错误处理
        onError: (err, req, res) => {
          logger.error('代理请求错误:', err);
          const resWrapper = new ResponseWrapper(req, res);
          return resWrapper.error('PROXY_ERROR', '代理请求失败');
        },
        // 确保 body-parser 解析后的请求体能正确传递
        bodyParser: false
      });

      // 执行代理
      return exampleProxy(req, res, next);
    } catch (err) {
      logger.error('校验服务代理错误:', err.stack || err.message);
      const resWrapper = new ResponseWrapper(req, res);
      return resWrapper.error('HANDLE_ERROR', err.message);
    }
  });
}

module.exports = jiaoyanProxy;