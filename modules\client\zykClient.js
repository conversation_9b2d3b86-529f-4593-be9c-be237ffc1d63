/**
 * 算法
 * wiki: https://wiki.iyunxiao.com/pages/viewpage.action?pageId=25232048
 */

const _ = require('lodash');
const URL = require('url');
const config = require('config');
const axios = require('axios');
const logger = require('../utils/logger');
const SERVER = config.get('ZYK_SERVER');
const qs = require('querystring');
const utils = require('../utils/utils');

module.exports = {
    getExampaper,
};

/**
 * 考后巩固组卷
 * @param params
 * @returns {Promise<*>}
 */
async function getExampaper(cookie, exampaperId) {
    let url = URL.format({
        protocol: SERVER.protocol,
        hostname: SERVER.hostname,
        pathname: `/ptk_api/v1/assemble/exampapers/${exampaperId}`,
        port: SERVER.port
    });
    let opstions = {
        headers: {
            'Content-Type': 'application/json',
            'cookie': cookie.replace(/tiku/g, 'ptk')
        },
        timeout: 2000
    };
    let result = await axios.get(url, opstions).then(utils.handler);
    // if (!result.data || result.data.code !== 0) {
    //     logger.error(`资源库获取试卷失败,url:[${url}]`);
    // }
    return result && result.exampaper || null;
}
