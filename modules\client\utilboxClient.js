const _ = require('lodash');
const URL = require('url');
const config = require('config');
const axios = require('axios');
const logger = require('../utils/logger');
const server = config.get('UTIL_SERV');
const utils = require('../utils/utils');
const qs = require('querystring');


async function downloadExampaper(doc) {
    // 系统试卷库
    const url = URL.format({
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        pathname: '/utilbox_api/v1/exampapers/documents',
        search: qs.stringify({
            type: 'json',
            fields_type: 'full',
            // api_key: server.appKey
        })
    });
    const options = {
        timeout: 20000,
        responseType: 'arraybuffer'
    }
    const result = await axios.post(url, doc, options);
    return result;
}


module.exports = {
    downloadExampaper,
}
