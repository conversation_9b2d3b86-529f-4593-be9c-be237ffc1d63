const enums = require('../../bin/enum');
const logger = require('../../modules/utils/logger');
const crypto = require('crypto');
const config = require('config');
const jwt = require('jsonwebtoken');
const util = require('util');
let aes = require('./aes');
let jwt2 = require('./jwt');
const _ = require('lodash');
const env = process.env.NODE_ENV;

function to_local_time(date_obj) {
    var y = date_obj.getFullYear();
    var m = date_obj.getMonth() + 1;
    var d = date_obj.getDate();
    var ho = date_obj.getHours(); //
    var mi = date_obj.getMinutes();
    var se = date_obj.getSeconds();
    return `${y}-${m}-${d} ${ho}:${mi}:${se}`;
}

function isMobileByUa(ua) {
    if (!ua) return false;

    let ipad = ua.match(/(iPad).*OS\s([\d_]+)/),
        isIphone = !ipad && ua.match(/(iPhone)/) && (ua.indexOf('HaoFenShu') !== -1),
        isAndroid = ua.match(/(Android)/) && (ua.indexOf('YX') !== -1),
        isMobile = isIphone || isAndroid;
    if (isMobile) {
        return true;
    }

    return false;
}

function getDeviceTypeByUa(ua) {
    if (!ua) return enums.DeviceType.NOT_KNOWN;

    let ipad = ua.match(/(iPad).*OS\s([\d_]+)/),
        isIphone = !ipad && ua.match(/(iPhone)/) && (ua.indexOf('HaoFenShu') !== -1),
        isAndroid = ua.match(/(Android)/) && (ua.indexOf('YX') !== -1);
    if (ipad) return enums.DeviceType.IOS_PAD;
    if (isIphone) return enums.DeviceType.IOS_PHONE;
    if (isAndroid) return enums.DeviceType.ANDROID;
    return enums.DeviceType.WEB;
}

/**
 * 默认express错误处理包裹
 * @param {String} msg
 * @param {function} handler
 * @returns {function} callback
 */
function reqHandler(handler) {
    return async (req, resp, next) => {
        try {
            await handler(req, resp, next);
        } catch (e) {
            logger.error(e);
            next(e);
        }
    };
};

// axios default data handler
function handler({ data: { code, msg, data }, config }) {
    if (code !== 0) {
        throw new Error(`code:${code}, msg: ${msg}`);
    }
    return data;
};

function kbHandler({ data, config }) {
    if (data.code && data.code !== 0) {
        throw new Error(`code:${data.code}, msg: ${data.msg}`);
    }
    return data;
};

function bossHandler({ data: { code, msg, data }, config }) {
    if (code !== 1) {
        throw new Error(`boss服务错误: ${msg}`);
    }
    return data;
};

function casHandler({ data: { code, msg, data }, config }) {
    if (code !== 1) {
        throw new Error(`passport服务错误: ${msg}`);
    }
    return data;
};

function casUserHandler({ data: { code, msg, data }, config }) {
    if (code === 0 && msg === '查不到相关的账号') {
        return data;
    }
    if (code !== 1) {
        throw new Error(`passport服务错误: ${msg}`);
    }
    return data;
};

function yjHandler({ data: { code, message, data }, config }) {
    if (code !== 0) {
        throw new Error(`code:${code}, msg: ${message}`);
    }
    return data;
};

function getRandomString(length, type) {
    let text = [];

    let possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    if (type == 'n') { // n: number
        possible = '0123456789';
    } else if (type == 'l') { // l: letter
        possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
    } else { // any
        possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    }

    for (let i = 0; i < length; i++) {
        text.push(possible.charAt(Math.floor(Math.random() * possible.length)));
    }

    return text.join('');
}

function renderTemplate(template, placeholder) {
    var result = template;
    for (var k in placeholder) {
        var regex = new RegExp('\\${' + k + '}', 'g');
        result = result.replace(regex, placeholder[k]);
    }
    return result;
}

/**
 * Desc: 解析企业微信二维码token
 * @param {string} token
 * @returns {Array}
    ["吴金锋", // 姓名
    "2302", // 部门
    "后端开发工程师"
    "wujinfeng"] // 企信id
 */
function parseQXToken(token) {
    try {
        const QX_TOKEN_SECRET_KEY = config.get('QX_TOKEN_SECRET_KEY');
        let decipher = crypto.createDecipher('aes192', QX_TOKEN_SECRET_KEY);
        let decrypted = decipher.update(token, 'hex', 'utf8');
        decrypted += decipher.final('utf8');
        let arr = decrypted.split('::');
        let now = Math.floor(Date.now() / 1000);
        if (arr.length === 5 && arr[0] > now) {
            arr.shift();
            return arr;
        }
        return null;
    } catch (e) {
        console.log('parse_jump_token error, token:' + token + ', error message:' + e);
    }
    return undefined;
};


function objectKeySort(data) {
    let sortKeys = Object.keys(data).sort();
    let retObj = {};
    for (let key of sortKeys) {
        retObj[key] = data[key];
    }
    return retObj;
};

const getYjToken = async (data, user) => {
    const YJ_API_KEY = config.get('YJ_API_SERVER').key;
    let curData = objectKeySort(Object.assign({
        name: user.name,
        schoolId: user.schoolId || user.school_id,
        userId: user.userId || user._id || user.id,
        jti: 'userId',
        iat: Date.now(),
        schoolName: user.schoolName,
    }, data));

    const token = await util.promisify(jwt.sign)(curData, Buffer.from(Buffer.from(YJ_API_KEY, 'base64')),
        //  {
        //     // jwtid: curData['userId'].toString(),
        //     expiresIn: 86400,
        //     algorithm: 'HS512',
        // }
    );
    return token;
};

const getYjTokenInfo = async (token) => {
    const YJ_API_KEY = config.get('YJ_API_SERVER').key;
    const data = await util.promisify(jwt.verify)(token, YJ_API_KEY);
    return data;
};

function getClientIp(req) {
    let ip = req.headers['x-forwarded-for'] || req.headers['x-real-ip'] || req.ip || req.connection.remoteAddress || '';
    ip = ipv6ToV4(ip);
    return ip;
}

function ipv6ToV4(ip) {
    if (ip.split(',').length > 0) {
        ip = ip.split(',')[0]
    }
    ip = ip.substr(ip.lastIndexOf(':') + 1, ip.length);
    return ip
}

function getTikuSessionUser (req) {
    let user = null;
    const cookie = config.get('TIKU_SERVER').sessionIdCookie;
    const tikuSession = req.cookies && req.cookies[cookie.name];
    if (!tikuSession) return user;
    try {
        user = jwt2.decode(aes.decript(tikuSession));
    } catch (e) {
    }
    return user;
}

/**
 * 生成范围随机数
 * @param min
 * @param max
 * @returns {number}
 */
function randomRange(min = 0, max = Number.MAX_SAFE_INTEGER) {
    const randomInt = Math.floor(Math.random() * (max - min + 1)) + min;
    return randomInt;
}

function maskPhoneNumber(phoneNumber) {
    return phoneNumber.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
}

function wxSign(str) {
    return crypto.createHash('sha1').update(str).digest('hex');
}

async function sleep(time) {
    return new Promise(resolve => setTimeout(resolve, time));
}


function matchNumber(str, no_match_value = Number.MAX_SAFE_INTEGER) {
    const arr = str.match(/\d+/g);
    if (arr && arr.length) {
        return parseInt(arr[0]);
    }
    return no_match_value;
}

function isTestEnv() {
    if (!env || env === 'development' || env === 'test') {
        return true;
    }
    return false;
}

function formatUserId(userId) {
    return _.padStart(userId, 24, '0');
}

/**
 * 获取学年
 * @param date
 * @returns {{to_year: number, from_year: number}}
 */
function getAcademicYear(date = new Date()) {
    const year = date.getFullYear();
    const month = date.getMonth() + 1; // 月份从0开始，所以加1

    // 学年从8月开始
    const result = {
        from_year: year,
        to_year: year
    };
    if (month >= 8) {
        result.to_year = year + 1;
    } else {
        result.from_year = year - 1;
    }
    return result;
}



module.exports = {
    to_local_time: to_local_time,
    isMobileByUa: isMobileByUa,
    reqHandler: reqHandler,
    handler: handler,
    kbHandler: kbHandler,
    bossHandler: bossHandler,
    casHandler: casHandler,
    casUserHandler: casUserHandler,
    yjHandler: yjHandler,
    getDeviceTypeByUa: getDeviceTypeByUa,
    getRandomString: getRandomString,
    renderTemplate: renderTemplate,
    parseQXToken,
    objectKeySort,
    getYjToken,
    getYjTokenInfo,
    getClientIp,
    getTikuSessionUser,
    randomRange,
    maskPhoneNumber,
    wxSign,
    sleep,
    matchNumber,
    isTestEnv,
    formatUserId,
    getAcademicYear,
}
