/**
 * 请求日志记录中间件
 * 记录所有请求的路由、query 和 body 数据到 reco_data_log 表
 */

const mongodber = require('../utils/mongodber');
const { schema } = require('../utils/constants');

/**
 * 请求日志记录中间件
 */
const requestLogger = async (req, res, next) => {
    try {
        // 获取请求开始时间
        const startTime = Date.now();

        // 准备日志数据
        const logData = {
            // 基础信息
            method: req.method,
            route: req.originalUrl || req.url,
            path: req.path,

            // 请求参数
            query: req.query || {},
            body: req.body || {},
            params: req.params || {},

            // 请求头信息
            headers: {
                'user-agent': req.headers['user-agent'],
                'content-type': req.headers['content-type'],
                'referer': req.headers['referer'],
                'host': req.headers['host']
            },

            // 客户端信息
            ip: req.ip || req.connection.remoteAddress,

            // 时间戳
            timestamp: new Date(),

            // 用户信息（如果有）
            user_id: null,
            user_name: null
        };

        // 如果请求中有用户信息，记录用户ID和用户名
        if (req.user) {
            logData.user_id = req.user.id || req.user._id;
            logData.user_name = req.user.name || req.user.username;
        } else if (req.body && req.body.user) {
            logData.user_id = req.body.user.id || req.body.user._id;
            logData.user_name = req.body.user.name || req.body.user.username;
        }

        // 监听响应结束事件，记录响应信息
        const originalSend = res.send;
        res.send = function (data) {
            res.send = originalSend;

            // 记录响应信息
            logData.response = {
                statusCode: res.statusCode,
                duration: Date.now() - startTime, // 请求处理时长（毫秒）
                timestamp: new Date()
            };

            // 如果响应数据不是太大，也记录响应内容（限制大小避免存储过多数据）
            try {
                if (data && typeof data === 'string' && data.length < 10000) {
                    const responseData = JSON.parse(data);
                    // 只记录关键信息
                    logData.response.ok = responseData.ok;
                    logData.response.errCode = responseData.errCode;
                    logData.response.error = responseData.error;
                }
            } catch (e) {
                // 如果解析失败，忽略
            }

            // 异步写入数据库，不阻塞响应
            saveLog(logData).catch(err => {
                console.error('保存请求日志失败:', err);
            });

            return originalSend.apply(res, arguments);
        };

        next();
    } catch (error) {
        // 记录日志出错不应该影响正常请求
        console.error('请求日志中间件错误:', error);
        next();
    }
};

/**
 * 保存日志到数据库
 */
async function saveLog(logData) {
    try {
        const db = mongodber.use('tiku');
        await db.collection(schema.reco_data_log).insertOne(logData);
    } catch (error) {
        console.error('保存日志到数据库失败:', error);
        throw error;
    }
}

/**
 * 创建请求日志记录中间件（可配置）
 * @param {Object} options - 配置选项
 * @param {Array} options.excludePaths - 要排除的路径列表
 * @param {Array} options.includePaths - 只包含的路径列表
 * @param {Function} options.filter - 自定义过滤函数
 */
function createRequestLogger(options = {}) {
    const { excludePaths = [], includePaths = [], filter } = options;

    return async (req, res, next) => {
        // 检查是否需要记录该请求
        const path = req.path;

        // 如果设置了包含路径，只记录这些路径
        if (includePaths.length > 0) {
            const shouldInclude = includePaths.some(p => {
                if (typeof p === 'string') {
                    return path.startsWith(p);
                } else if (p instanceof RegExp) {
                    return p.test(path);
                }
                return false;
            });

            if (!shouldInclude) {
                return next();
            }
        }

        // 检查排除路径
        const shouldExclude = excludePaths.some(p => {
            if (typeof p === 'string') {
                return path.startsWith(p);
            } else if (p instanceof RegExp) {
                return p.test(path);
            }
            return false;
        });

        if (shouldExclude) {
            return next();
        }

        // 自定义过滤
        if (filter && !filter(req)) {
            return next();
        }

        // 记录请求
        return requestLogger(req, res, next);
    };
}

module.exports = {
    requestLogger,
    createRequestLogger
};