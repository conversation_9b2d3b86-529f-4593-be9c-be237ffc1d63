const _ = require('lodash');
const Joi = require('@hapi/joi');
const axios = require('axios');
const moment = require('moment');

const ResponseWrapper = require('../../middlewares/response_wrapper');
const logger = require('../../utils/logger');
const enums = require('../../../bin/enum');

const mongodber = require('../../utils/mongodber');
const db = mongodber.use('tiku');
const client = require('../../client/index');
const assets = require('../../assets_api/v1/index');
const user_right_service = require('../../user_api/v2/user_right_service');
const user_download_service = require('../../user_api/v2/user_download_service');
const MAX_COUNT = 5;

const FILE_ACTION = {
    view_times: 'view_times',
    download_times: 'download_times'
}

module.exports = {
    getHomeData,
    getFilters,
    getList,
    getDownloadUrl,
    addViewTimes,
    getDetail,
    downloadFile,
    getById,
}

const JOI_GET_HOME_DATA = Joi.object({
    period: Joi.string().required(),
    subject: Joi.string().required(),
    category: Joi.string().required(),
    offset: Joi.number().required(),
    limit: Joi.number().required(),
}).unknown(true);

async function getHomeData(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const params = await JOI_GET_HOME_DATA.validate(req.query);
        params.page_img_status = enums.BooleanNumber.YES;
        params.preview_status = enums.BooleanNumber.YES;
        const kb_res = await client.kb.request('/kb_api/v2/education_assistant_files/list', {
            params: params
        });
        const result = {
            total: _.get(kb_res, 'total_num', 0),
            list: []
        };
        if (result.total) {
            for (const d of kb_res.records) {
                result.list.push({
                    id: d.id,
                    name: d.name,
                    host: d.host,
                    page_img_url: d.page_img_url,
                    page_num: d.page_num,
                    download_times: d.download_times,
                    view_times: d.view_times,
                    ctime: d.ctime,
                    period: d.period,                           // 学段
                    subject: d.subject,                         // 学科
                    press_version: d.press_version,                      // 教材版本
                    book_name: d.book_name,
                    category: d.category,
                    video_url: d.video_url,
                    file_preview_url: d.preview_url || '', // 预览地址
                });
            }
        }
        return responseWrapper.succ(result);
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const JOI_GET_FILTERS = Joi.object({
    period: Joi.string().required(),
    subject: Joi.string().required(),
    press_version: Joi.string().optional(),
    book_id: Joi.number().optional(),
    category: Joi.string().required(),
}).unknown(true);

async function getFilters(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const params = await JOI_GET_FILTERS.validate(req.query);
        // params.page_img_status = 1;
        params.preview_status = enums.BooleanNumber.YES;
        const result = await client.kb.request('kb_api/v2/education_assistant_files/filters', {
            params: params
        });
        return responseWrapper.succ(result);
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const JOI_GET_LIST = Joi.object({
    period: Joi.string().required(),
    subject: Joi.string().required(),
    press_version: Joi.string().optional(),
    book_id: Joi.number().optional(),
    chapter_id: Joi.number().optional(),
    category: Joi.string().required(),
    offset: Joi.number().required(),
    limit: Joi.number().required(),
}).unknown(true);

async function getList(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const params = await JOI_GET_LIST.validate(req.query);
        if (params.chapter_id) {
            params.parent_chapter_id = params.chapter_id;
            delete params.chapter_id;
        }
        params.page_img_status = enums.BooleanNumber.YES;
        params.preview_status = enums.BooleanNumber.YES;
        const kb_res = await client.kb.request('kb_api/v2/education_assistant_files/list', {
            params: params
        });
        const result = {
            total: _.get(kb_res, 'total_num', 0),
            list: []
        };
        if (result.total) {
            for (const d of kb_res.records) {
                result.list.push({
                    id: d.id,
                    name: d.name,
                    host: d.host,
                    page_img_url: d.page_img_url,
                    page_num: d.page_num,
                    download_times: d.download_times,
                    view_times: d.view_times,
                    ctime: d.ctime,
                    period: d.period,                           // 学段
                    subject: d.subject,                         // 学科
                    press_version: d.press_version,             // 教材版本
                    book_id: d.book_id,                         // 教材ID
                    book_name: d.book_name,                     // 教材名称
                    chapter_id: d.chapter_id,                   // 章节id
                    category: d.category,
                    video_url: d.video_url,
                    file_preview_url: d.preview_url || '', // 预览地址
                });
            }
        }
        return responseWrapper.succ(result);
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const JOI_GET_DOWNLOAD_URL = Joi.object({
    id: Joi.number().required(),
}).unknown(true);

async function getDownloadUrl(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        // const params = await JOI_GET_DOWNLOAD_URL.validate(req.query);
        // const data = await client.kb.request('kb_api/v2/education_assistant_files/:id', {
        //     pathParams: {
        //         id: params.id
        //     }
        // });
        // if (_.isEmpty(data)) {
        //     return responseWrapper.error('HANDLE_ERROR', '数据不存在');
        // }
        // // 会员权益处理
        // const user = await db.collection('user').findOne({_id: req.user.id});
        // if (!user.is_vip) return responseWrapper.error('NEED_VIP_ERROR', '需要购买会员后下载');
        // if (user.edu_file_download_num >= MAX_COUNT) {
        //     return responseWrapper.error('NEED_VIP_ERROR', '会员每日最多可以下载5次');
        // }
        // await db.collection('user').updateOne({_id: req.user.id}, {
        //     $inc: { edu_file_download_num: 1 },
        //     $set: { utime: new Date(), use_time: new Date() } });
        // // 增加下载次数
        // await _add_file_times(params.id, FILE_ACTION.download_times);
        // // 返回结果
        // const result = {
        //     url: `${data.host}${data.url}`
        // }
        // return responseWrapper.succ(result);
        return responseWrapper.succ('');
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const JOI_POST_DOWNLOAD = Joi.object({
    id: Joi.number().required(),
}).unknown(true);

async function downloadFile(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const user = req.user;
        const params = await JOI_POST_DOWNLOAD.validate(req.params);
        const data = await getById(params.id);
        if (_.isEmpty(data)) {
            return responseWrapper.error('HANDLE_ERROR', '数据不存在');
        }
        // 校验下载功能
        const fun_params = {
            type: enums.NewRightType.resource_download_fun,
            period: data.period,
            subject: data.subject,
            target: {
                id: data.id,
                type: enums.ResourceType.EDU_ASSISTANT_FILE
            }
        };
        const fun_status = await user_right_service.get_fun_right_status(req.user.id, fun_params);
        if (!fun_status) {
            return responseWrapper.error('NEED_VIP_ERROR', '开通会员后下载');
        }
        // 下载数量上限
        const limit_right = await user_right_service.get_right_limit(req.user.id, {
            type: enums.NewRightType.edu_file_download_limit,
            period: data.period,
            subject: data.subject
        });
        const download_count = await db.collection('user_download').find({
            user_id: req.user.id,
            resource_type: enums.ResourceType.EDU_ASSISTANT_FILE,
            ctime: {$gte: moment().startOf('day').toDate()}
        }).count();
        if (download_count >= limit_right.limit) {
            return responseWrapper.error('TIMES_NOT_ENOUGH_ERROR', `每日最多可下载${limit_right.limit}份`);
        }
        // 权益消耗
        const right_params =_.assign({}, fun_params, {type: enums.NewRightType.edu_file_download_num});
        const right = await user_right_service.get_use_right(req.user.id, right_params);
        if (!_.isEmpty(right) && (right.group === enums.NewVipType.school || right.group === enums.NewVipType.school_plus)) {
            const school_download_status = await user_download_service.check_school_download_limit(user.schoolId);
            if (!school_download_status) {
                return responseWrapper.error('TIMES_NOT_ENOUGH_ERROR', `学校每日最多可下载1000份资源`);
            }
        }
        const status = await user_right_service.use_right(req.user.id, right_params, right);
        if (!status) {
            return responseWrapper.error('OVER_LIMIT_ERROR', '下载次数不足');
        }
        // 下载记录
        const download = {
            user_id: user.id,
            sch_id: user.schoolId,
            resource_type: enums.ResourceType.EDU_ASSISTANT_FILE,
            resource_id: params.id,
            resource_name: data.name,
            type: data.category,
            period: data.period,                           // 学段
            subject: data.subject,                         // 学科
            press_version: data.press_version,             // 教材版本
            book_id: data.book_id,                         // 教材ID
            chapter_id: data.chapter_id,                   // 章节id
            download_times: 1,
            category: data.category === 'courseware' ? 1 : 2,
            ctime: new Date(),
            utime: new Date(),

        };
        const insert_id = await db.collection('user_download').insertOne(download);
        // 增加下载次数
        await _add_file_times(params.id, FILE_ACTION.download_times);
        // 读取
        const file_url = `${data.host}${data.url}`;
        const response = await axios.get(file_url, { responseType: 'stream' });
        if (!response || response.status !== 200 || !response.data) {
            return responseWrapper.error('HANDLE_ERROR', '下载文件出错');
        }
        response.data.pipe(res);
        res.setHeader('Content-disposition', `attachment; filename=${encodeURI(data.name)}`);
        res.setHeader('Content-type', 'application/octet-stream');
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const JOI_ADD_VIEW_TIMES = Joi.object({
    id: Joi.number().required(),
});

async function addViewTimes(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const params = await JOI_ADD_VIEW_TIMES.validate(req.body);
        // 增加浏览次数
        await _add_file_times(params.id, FILE_ACTION.view_times);
        return responseWrapper.succ({id: params.id});
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

async function getById(id) {
    const data = await client.kb.request('kb_api/v2/education_assistant_files/:id', {
        pathParams: {
            id: id
        }
    });
    return data;
}

const JOI_GET_DETAIL = Joi.object({
    id: Joi.number().required(),
}).unknown(true);

async function getDetail(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const params = await JOI_GET_DETAIL.validate(req.params);
        const data = await getById(params.id);
        if (_.isEmpty(data)) {
            return responseWrapper.error('NULL_ERROR', '数据不存在');
        }
        const result = {
            id: data.id,
            name: data.name,
            host: data.host,
            page_img_url: data.page_img_url,
            page_num: data.page_num,
            download_times: data.download_times,
            view_times: data.view_times,
            ctime: data.ctime,
            period: data.period,                           // 学段
            subject: data.subject,                         // 学科
            press_version: data.press_version,             // 教材版本
            book_id: data.book_id,                         // 教材ID
            book_name: data.book_name,                     // 教材名称
            chapter_id: data.chapter_id,                   // 章节id
            category: data.category,
            video_url: data.video_url,
            file_preview_url: data.preview_url || '', // 预览地址
        }
        // 增加浏览次数
        await _add_file_times(params.id, FILE_ACTION.view_times);
        return responseWrapper.succ(result);
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

async function _add_file_times(id, action) {
    // 增加浏览次数
    await client.kb.request('kb_api/v2/education_assistant_files/times', {
        method: 'put',
        data: {
            id: id,
            action: action
        }
    });
}
