const getExampaperFromTiku = require('../../assemble_api/v1/exampaper').get_exampaper_from_tiku;
const _get_exampaper_by_id = require('../../assemble_api/v1/exampaper').get_exampaper_by_id;
const get_exampaper_by_id_async = require('../../assemble_api/v1/exampaper').get_exampaper_by_id_async;

const mongodber = require('../../utils/mongodber');
const ResponseWrapper = require('../../middlewares/response_wrapper');
const logger = require('../../utils/logger');
const enums = require('../../../bin/enum');
const db = mongodber.use('tiku');
const ObjectId = require('mongodb').ObjectId;
const config = require('config');
const Joi = require('@hapi/joi');
const URL = require('url');
const axios = require('axios');
const _ = require('lodash');
const util = require('util');
const utils = require('../../utils/utils');
const redisCache = require('../../utils/redis_cache');

const getExampaperByIdSchema = Joi.object({
    id: Joi.number().required(),
    examId: Joi.string().required(),
    // token: Joi.string().required(),
}).unknown(true);

const getExampaperById = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let { id, examId } = await Joi.validate(req.query, getExampaperByIdSchema);
        const userInfo = await db.collection('user').findOne({ _id: id });
        if (!userInfo) return responseWrapper.error('PARAMETERS_ERROR', 'user not found');

        // let data = { id, examId };
        // const curToken = await utils.getYjToken(data, userInfo);
        // if (token !== curToken) return responseWrapper.error('PARAMETERS_ERROR', 'token 检验失败');
        const retObj = await util.promisify(getExampaperFromTiku)(id, examId);

        return responseWrapper.succ(retObj);
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};


const getUserInfoSchema = Joi.object({
    id: Joi.number().required(),
    // token: Joi.string().required(),
}).unknown(true);
/**
 * 用户信息
 */
const getUserInfo = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let { id } = await Joi.validate(req.query, getUserInfoSchema);
        const userInfo = await db.collection('user').findOne({ _id: id });
        if (!userInfo) return responseWrapper.error('PARAMETERS_ERROR', 'user not found');

        let resObj = {
            id: id,
            name: userInfo.name,
            schId: userInfo.sch_id,
            schName: userInfo.sch_name,
            isVip: false,
        };

        let schoolInfo;
        if (userInfo.sch_id) schoolInfo = await db.collection('school_info').findOne({ _id: userInfo.sch_id });
        if (schoolInfo) {
            const schoolAppUsage = await redisCache.getBossAppUsageSchoolCache(userInfo.sch_id);
            if (schoolInfo && schoolInfo.teachers && schoolInfo.teachers.includes(id) && schoolAppUsage && schoolAppUsage.appUsages && schoolAppUsage.appUsages[0] && schoolAppUsage.appUsages[0].status && new Date(schoolAppUsage.appUsages[0].endDate) > new Date()) {
                resObj.isVip = true;
                resObj.vipType = enums.CrmVersionToMemberType[schoolAppUsage.appUsages[0].name];
                resObj.startTime = new Date(schoolAppUsage.appUsages[0].beginDate).getTime();
                resObj.expiredTime = new Date(schoolAppUsage.appUsages[0].endDate).getTime();
            } else if (schoolInfo && Object.values(enums.YjSchoolVersionTypeToMemberType).includes(schoolInfo.vip_type) && schoolInfo.expired_time > new Date()) {
                resObj.isVip = true;
                resObj.vipType = schoolInfo.vip_type;
                resObj.startTime = schoolInfo.start_time && schoolInfo.start_time.getTime();
                resObj.expiredTime = schoolInfo.expired_time && schoolInfo.expired_time.getTime();
            }
        }

        return responseWrapper.succ(resObj);
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};


const getSchoolVipStatSchema = Joi.object({
    id: Joi.number().required(),
    // token: Joi.string().required(),
}).unknown(true);
/**
 * 学校会员统计
 */
const getSchoolVipStat = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let { id } = await Joi.validate(req.query, getSchoolVipStatSchema);
        const schoolInfo = await db.collection('school_info').findOne({ _id: id });
        if (!schoolInfo) return responseWrapper.error('PARAMETERS_ERROR', 'school not found');

        const userCond = {
            'sch_id': id,
        };
        let users = await db.collection('user').find(userCond).toArray();
        let userIds = users.map(e => e._id);

        const orderCond = {
            'status': 'success',
            'type': 'member',
            'user_id': { $in: userIds }
        };
        let orders = await db.collection('@Order').find(orderCond).toArray();

        const recordCond = {
            'status': 'normal',
            'sch_ids': id,
        };
        let records = await db.collection('@SchoolOrderRecord').find(recordCond).toArray();

        const resObj = {
            id: id,
            name: schoolInfo.name,
            tikuBasicCount: records.filter(e => e.vip_type === enums.SchoolVipType.YJ_ULTIMATE || e.vip_type === enums.SchoolVipType.YJ_PROFESSION).length,
            tikuProfessionCount: records.filter(e => e.vip_type === enums.SchoolVipType.TIKU_PROFESSION).length,
            tikuCountyCount: records.filter(e => e.vip_type === enums.SchoolVipType.TIKU_COUNTY).length,
            tikuCityCount: records.filter(e => e.vip_type === enums.SchoolVipType.TIKU_CITY).length,

            schoolVipList: records.map(e => {
                return {
                    'vipType': enums.SchoolVipTransformText[e.vip_type],
                    'stime': e.stime,
                    'etime': e.etime,
                }
            }),

            monthVipCount: orders.filter(e => e.goods.month === 1).length,
            halfYearVipCount: orders.filter(e => e.goods.month === 6).length,
            yearVipCount: orders.filter(e => e.goods.month === 12).length,
            userVipList: orders.map(e => {
                return {
                    'id': e.user_id,
                    'time': e.ctime,
                    'vipType': e.goods.name
                }
            }),
        };

        return responseWrapper.succ(resObj);
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};


const joi_get_exampaper_by_id = Joi.object({
    id: Joi.string().required(),
}).unknown(true);

const get_exampaper_by_id = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let { id } = await Joi.validate(req.params, joi_get_exampaper_by_id);
        // const retObj = await util.promisify(_get_exampaper_by_id)(id);
        const retObj = await get_exampaper_by_id_async(id);
        return responseWrapper.succ(retObj);
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

module.exports = {
    getExampaperById,
    getUserInfo,
    getSchoolVipStat,
    get_exampaper_by_id
};
