/**
 * 算法
 * wiki: https://wiki.iyunxiao.com/pages/viewpage.action?pageId=25232048
 */

const _ = require('lodash');
const URL = require('url');
const config = require('config');
const axios = require('axios');
const logger = require('../utils/logger');
const SERVER = config.get('YUANPEI_API_SERVER');
const qs = require('querystring');
const utils = require('../utils/utils');

module.exports = {
    getPaperQuestions,
};

/**
 * 考后巩固组卷
 * @param params
 * @returns {Promise<*>}
 */
async function getPaperQuestions(paperId) {
    if (paperId.indexOf('-') !== -1) {
        paperId = paperId.split('-')[0];
    }
    let url = URL.format({
        protocol: SERVER.protocol,
        hostname: SERVER.hostname,
        pathname: `/yuanpei_api/v4/papers/${paperId}`,
        port: SERVER.port,
        search: qs.stringify({
            api_key: SERVER.appkey,
        })
    });
    let opstions = { headers: {}, timeout: 50000 };
    let result = await axios.get(url, opstions);
    if (!result.data || result.data.code !== 0) {
        logger.error(`元培获取试卷信息失败,url:[${url}], result: ${result && JSON.stringify(result.data)}`);
        return null;
    }
    return result.data && result.data.data;
}
