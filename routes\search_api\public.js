
/*
 * Desc: the route of search api 
 * Author: guo<PERSON><PERSON>
 */

var router = require('express').Router();
var apikey = require('../../modules/middlewares/apikey');
var transmiter = require('../../modules/search_api/v1/transmiter'); 
const config = require('config');
const KBSERVCFG = config.get('KB_API_SERVER');

// kb api key middleware
router.use(function (req, res, next) {
    req.apiKey = KBSERVCFG.appKey;
    next();

});

// 搜索试题
router.get('/search/exampapers/', transmiter.transmit);
// 搜索试卷
router.get('/search/questions/', transmiter.transmit);

module.exports = router;
