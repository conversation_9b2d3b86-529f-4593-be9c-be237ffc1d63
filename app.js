/**
 * 引入依赖
 */
let express = require('express');
let bodyParser = require('body-parser');
let cookieParser = require('cookie-parser');
let config = require('config');
let thenjs = require('thenjs');
let gzip = require('compression');
const uuid = require('uuid');
const _ = require('lodash');
const rateLimit = require('express-rate-limit');
let logger = require('./modules/utils/logger');
let mongodber = require('./modules/utils/mongodber');
let rediser = require('./modules/utils/rediser');
let ResponseWrapper = require('./modules/middlewares/response_wrapper');
let verify = require('./modules/middlewares/verify');
// let userInfo = require('./modules/middlewares/user');
// let accessControl = require('./modules/middlewares/accessControl');
// let fs = require('fs');
// const isMobileByUa = require('./modules/utils/utils').isMobileByUa;
const proxyMiddleware = require('http-proxy-middleware');
const utils = require('./modules/utils/utils');
const proxyTable = config.get('proxyTable');
// const UTIL_SERV = config.get('UTIL_SERV');
const url = require('url');
const CORS = config.get('CORS');
const limitUser = require('./bin/limitUser');

let app = express();
// 定义一个速率限制器
const limiter = rateLimit({
    windowMs: 1 * 60 * 1000, // 1 分钟
    max: 2, // 每个 IP 在 1 分钟内最多请求 100 次
    message: '请求过于频繁，请稍后再试。',
    onLimitReached: async (req, res) => {
        await limitUser.checkToken(req);
    },
    keyGenerator: async (req) => {
        const key = await limitUser.getTokenUserId(req);
        return key;
    }
});

// let teacher = null;
// let student = null;
//
// try {
//     teacher = fs.readFileSync('./dist/teacher.html').toString();
//     student = fs.readFileSync('./dist/student.html').toString();
// } catch (err) {
//     console.error(err);
// }

thenjs(function (cont) {
    // 将速率限制器应用到所有请求
    // app.use(limiter);
    app.use(gzip());
    app.use('/favicon.ico', express.static('./favicon.ico'));
    // app.use('/node_modules', express.static('./node_modules'));
    Object.keys(proxyTable).forEach(function (context) {
        let options = proxyTable[context];
        if (typeof options === 'string') {
            options = { target: options };
        }
        app.use(proxyMiddleware(options.filter || context, options));
    });
    // app.use('/static', express.static('./dist/static'));

    // 初始化基础资源数据库
    let mongodbs = config.get('MONGODBS');
    mongodber.init(mongodbs, function (err) {
        if (err) {
            console.error(err);
            process.exit(-1);
        }
        cont(null, null);
    });
}).then(function (cont) {
    // 连接redis
    let redis_conf = config.get('REDIS');
    rediser.init(redis_conf, function (err) {
        if (err) {
            console.error('Connect Cache Error: ' + err);
            process.exit(-1);
        }
        cont(null, null);
    });
}).then(function (cont) {
    // 设置日志配置
    app.use(logger.init(config.get('LOGGING')));
    // 设置post上传格式和最大传输数据量
    app.use(bodyParser.json({ type: 'application/json', limit: '20480kb' }));
    app.use(bodyParser.urlencoded({ extended: true, limit: '20480kb' }));
    app.use(cookieParser());
    app.use('/download/xlsx', express.static('data/xlsx'));
    app.use(function (req, res, next) {
        req.port = app.get('port');
        next();
    });

    // 支持跨域访问控制CORS配置
    app.all('*', function (req, res, next) {
        const origin = req.headers.origin;
        if (CORS.includes(origin)){
            res.header('Access-Control-Allow-Origin', origin);
        }
        res.header('Access-Control-Allow-Credentials', true);
        res.header('Access-Control-Allow-Headers', 'api-key, Origin, X-Requested-With, Content-Type, Accept');
        res.header('Access-Control-Allow-Methods', 'PUT, POST, GET, DELETE, OPTIONS');
        res.header('X-Powered-By', ' 3.2.1');
        res.header('Content-Type', 'application/json;charset=utf-8');
        next();
    });

    app.options('*', (req, res, next) => {
        res.end('');
    });

    app.all('*', function (req, res, next) {
        const sn = req.headers.logsn || getSN();
        const ip = utils.getClientIp(req) || '';
        req.logCtx = {
            sn: sn,
            ip: ip,
            method: req.method,
            url: req.url,
            // brief: 'success',
            start: Date.now(),
        };
        const sensitiveProperties = ['password', 'oldPassword', 'newPassword'];
        const logQuery = req.query;
        const logBody = _.isObject(req.body) ? _.omit(req.body, sensitiveProperties) : req.body;
        const logDevice = utils.getDeviceTypeByUa(req.headers['user-agent']);
        const bodySize = logBody && JSON.stringify(logBody).length || 0;
        logger.info(`[SN_${sn}]${req.method} ${req.url} from ${ip} with query ${JSON.stringify(logQuery)} and body ${JSON.stringify(bodySize > 5000 ? null : logBody)} ${logDevice}`);
        next();
    });

    app.get('/v333/limit/users', (req, res) => {
        res.json({user: limitUser.getAll()});
    })

    // notification API
    // const notificationRoute = require('./routes/payments_api/notification');
    // notificationRoute(app);

    app.use(function (req, res, next) {
        if (req.headers['if-none-match'])
            delete req.headers['if-none-match'];
        next();
    });

    // Public API
    const innerRouter = require('./routes/public_api');
    innerRouter(app);

    // Public路由配置
    let publicRoute = require('./routes/public_index');
    publicRoute(app);

    // Public路由配置
    let feedbackRoute = require('./routes/feedback_index');
    feedbackRoute(app);

    // 验权中间件
    app.use(verify.userVerify);
    // app.use(userInfo);
    app.all('*', (req, res, next) => {
        const limit = limitUser.checkLimit(req.user.id);
        if (limit) return res.send('请求过于频繁，请稍后再试吧。');
        next();
    });
    // 访控中间件 hfs vip cookie
    // app.use(accessControl('cookie'));
    // app.use(verify.visitFreqVerify);

    app.use(proxyMiddleware('/utilbox_api', {
        target: url.format({
            protocol: config.get('UTIL_SERV').protocol,
            hostname: config.get('UTIL_SERV').hostname,
            port: config.get('UTIL_SERV').port,
        }),
        changeOrigin: true,
    }));

    // 路由配置
    let route = require('./routes/index');
    route(app);

    // app.use(function (req, res, next) {
    //     if (req.user.role === '教师' && !teacher || !student) {
    //         next();
    //         return;
    //     }
    //
    //     res.setHeader('content-type', 'text/html');
    //     if (req.user.role === '教师') {
    //         return res.end(teacher);
    //     }
    //
    //     return res.end(student);
    // });

    // 未定义的api
    app.use(function (req, res, next) {
        logger.error('Not Found:' + req.path);
        let err = new Error('Not Found');
        err.status = 404;
        next(err);
    });

    // 未知异常
    app.use(function (err, req, res) {
        let resWrapper = new ResponseWrapper(req, res);
        if (err) {
            if (err.stack)
                logger.error(err.stack);
            else
                logger.error(err.message);

            if (404 === err.status) {
                return resWrapper.error('URL_ERROR', err.message);
            }

            return resWrapper.error('HANDLE_ERROR', err.message);
        }
    });
    require('./modules/timed_task/updateSchoolInfo');
    cont(null, null);
}).then(function (cont) {
    // 启动server
    let app_port = process.env.NODE_PORT || 8887;
    app.set('port', app_port);
    let server = app.listen(app.get('port'), cont);
    server.on('error', on_error);
    server.on('listening', on_listening);

    // Event listener for HTTP server "error" event.
    function on_error(error) {
        if (error.syscall !== 'listen') {
            throw error;
        }

        let bind = typeof app_port === 'string'
            ? 'Pipe ' + app_port : 'Port ' + app_port;

        // handle specific listen errors with friendly messages
        switch (error.code) {
            case 'EACCES':
                logger.error(bind + ' requires elevated privileges');
                process.exit(1);
                break;
            case 'EADDRINUSE':
                logger.error(bind + ' is already in use');
                process.exit(1);
                break;
            default:
                throw error;
        }
    }

    function on_listening() {
        let addr = server.address();
        let bind = typeof addr === 'string'
            ? 'pipe ' + addr : 'port ' + addr.port;
        logger.info('Listening on ' + bind);
    }
}).then(function (cont) {
    let app_name = config.get('APP.name');
    logger.info(app_name + ', express Listen ' + app.get('port'));
}).fail(function (cont, err) {
    if (err.stack)
        logger.error('Error: ' + err.stack);
    else
        logger.error('Error: ' + err);
    process.exit(-1);
});

process.on('uncaughtException', function (err) {
    logger.error('Caught exception: ' + err.stack);

    // const errMsg = {
    //     api: 100,
    //     msg: 'Global caught exception!',
    //     errStack: err.stack || err
    // };
    // logger.error(`=== Error Code: `, errCodes.GLOBAL_ERR);
    // logger.error(`=== Error Msg: `, errMsg);
});

process.on('unhandledRejection', (reason, promise) => {
    if (reason instanceof Error) {
        console.log(`uncaughtRejection: ${JSON.stringify(reason.stack)}==`);
    }
});

function getSN() {
    return uuid.v1().replace(/-/g, '');
}
