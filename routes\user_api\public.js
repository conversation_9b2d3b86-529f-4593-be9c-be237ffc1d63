const express = require('express');
const router = express.Router();
const user = require('../../modules/user_api/v1/user');

router.post('/login', user.login);

router.post('/regist', user.regist);

router.post('/regist/code', user.registVcodes);

router.get('/', user.loginToken);

router.get('/token', user.loginToken);
router.get('/hfs/cookie', user.loginHfsCookie);
// router.get('/greetings', user.loginToken);

router.get('/admin/makePaper/byQuestions', user.loginToken);

// router.get('/admin/makePaper/byExam', user.loginHfsCookie);

router.get('/phone/exist', user.getUserByPhone);

// 好分数教师端使用，暂时允许不鉴权调用
router.post('/transfers/data', user.addTemporaryKnowItem);
router.get('/transfers/data/:jade', user.getTemporaryKnowItem);

// 作业系统获取cookie
router.get('/homework/session', user.homeworkLogin);

// 获取用户IP所在地
router.get('/region/', user.getIpRegion);

module.exports = router;
