const _ = require('lodash');
const Joi = require('@hapi/joi');
const axios = require('axios');
const moment = require('moment');

const ResponseWrapper = require('../../middlewares/response_wrapper');
const logger = require('../../utils/logger');
const enums = require('../../../bin/enum');

const mongodber = require('../../utils/mongodber');
const db = mongodber.use('tiku');
const client = require('../../client/index');
const schema = require('../../../bin/schema');

module.exports = {
    getList,
}

const JOI_GET_LIST = Joi.object({
    category: Joi.string().required()
}).unknown(true);

async function getList(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const { category } = await JOI_GET_LIST.validate(req.params);
        const cond = {code: category, status: enums.BooleanNumber.YES};
        const result = {
            total: 0,
            list: []
        };
        const total = await db.collection(schema.pms_spu).find(cond).count();
        if (!total) return responseWrapper.succ(result);
        result.total = total;
        const list = await db.collection(schema.pms_spu).find(cond).toArray();
        for (const spu of list) {
            const obj = {
                id: spu._id.toString(),
                name: spu.name,
                desc: spu.desc,
                detail: spu.detail,
                pub_specs: spu.pub_specs,
                prv_specs: spu.prv_specs,
                sku_list: []
            }
            const sku_list = await db.collection(schema.pms_sku).find({spu_id: obj.id, status: enums.BooleanNumber.YES}).toArray();
            if (_.size(sku_list)) {
                obj.sku_list = sku_list.map(sku => {
                    return {
                        id: sku._id.toString(),
                        name: sku.name,
                        key: sku.key, //
                        // 价格相关
                        original_price: sku.original_price, // 原价
                        discount_price: sku.discount_price, // 优惠金额
                        final_price: sku.final_price, // 实际金额
                        // 规格相关
                        specs: sku.specs,
                        desc: sku.desc || '', // 宣传语、描述、卖点
                        tag: sku.tag || '', // 标签文本(超值、划算等)
                    };
                });
            }
            result.list.push(obj);
        }
        return responseWrapper.succ(result);
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}
