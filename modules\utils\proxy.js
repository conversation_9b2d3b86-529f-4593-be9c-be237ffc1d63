const axios = require('axios');


const doRequest = async (doUrl, method, options) => {
    options = options || {};
    let response = {};
    if (axios.default) {
        response =  await axios.default({
            url: doUrl,
            method: method || 'GET',
            headers: options.headers || {},
            params: options.query || {},
            data: options.body || {},
            responseType: options.responseType || {},
            withCredentials: options.credentials || false
        });
    } else {
        if (options.body) {
            options.data = options.body;
            delete options.body;
        }
        options.method = method || 'GET';
        response =  await axios.sendRequest(doUrl, options);
    }
    return response;
};


module.exports = {doRequest};