const mongodber = require('./mongodber');
const db = mongodber.use('tiku');


const _addInstance  = async (table, data) => {
    let wr = await db.collection(table).insertOne(data);
    return {
        ok: wr.result.ok === 1 ? true : false,
        insertedId: wr.insertedId
    };
};

const _addList = async (table, list) => {
    let wr = await db.collection(table).insertMany(list);
    return {
        ok: wr.result.ok === 1 ? true : false,
        insertedIds: wr.insertedIds
    };
};

const _removeInstanceByCond = async (table, cond) => {
    let rs = await db.collection(table).deleteOne(cond);
    return {
        ok: rs.result.ok === 1 ? true : false
    };
};

const _removeListByCond = async (table, cond) => {
    let rs = await db.collection(table).deleteMany(cond);
    return {
        ok: rs.result.ok === 1 ? true : false
    };
};

const _updateInstanceByCond = async (table, cond, data, options = {}) => {
    let rs = await db.collection(table).updateOne(cond, data, options);
    return {
        ok: rs.result.ok === 1 ? true : false
    };
};

const _updateListByCond = async (table, cond, data, options = {}) => {
    let rs = await db.collection(table).updateMany(cond, data, options);
    return {
        ok: rs.result.ok === 1 ? true : false
    };
};

const _getInstanceByCond = async (table, cond, show = {}) => {
    let data = await db.collection(table).find(cond).project(show).limit(1).batchSize(1).toArray();
    return data[0];
};

const _getListByCond = async (table, cond, options = {}) => {
    let show = options.show ? options.show : {};
    let cursor = await db.collection(table).find(cond).project(show);
    let total = 0;
    if (options.sorts) {
        await cursor.sort(options.sorts);
    }
    if (typeof options.offset === 'number' && typeof options.limit === 'number') {
        await cursor.skip(options.offset).limit(options.limit);
    }
    let list = await cursor.toArray();
    if (options.total) {
        total = await cursor.count();
        return {total, list};
    } 
    return list;
};

const _countFilesByCond = async (tableName, cond) => {
    let num =  await db.collection(tableName).countDocuments(cond);
    return num;
};

const _distinctByCond = async (tableName, field, cond = {}) => {
    let data = await db.collection(tableName).distinct(field, cond);
    return data;
};

module.exports = {
    _addInstance,
    _addList,
    _removeInstanceByCond,
    _removeListByCond,
    _updateInstanceByCond,
    _updateListByCond,
    _getInstanceByCond,
    _getListByCond,
    _countFilesByCond,
    _distinctByCond
};

