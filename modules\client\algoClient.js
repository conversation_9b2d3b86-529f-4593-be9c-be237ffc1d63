/**
 * 算法
 * wiki: https://wiki.iyunxiao.com/pages/viewpage.action?pageId=25232048
 */

const _ = require('lodash');
const URL = require('url');
const config = require('config');
const axios = require('axios');
const logger = require('../utils/logger');
const ALGO_SERVER = config.get('ALGO_SERVER');
const utils = require('../utils/utils');

module.exports = {
    enhancePaper,
    levelPaper,
    recommendPaper,
    recommendQuestions,
    detailTablePaper,
    knowledgesPaper,
};

/**
 * 考后巩固组卷
 * @param params
 * @returns {Promise<*>}
 */
async function enhancePaper (params) {
    let url = URL.format({
        protocol: ALGO_SERVER.protocol,
        hostname: ALGO_SERVER.hostname,
        pathname: '/past_exam/enhance_paper',
        port: ALGO_SERVER.port,
    });
    let opstions = { headers: {}, timeout: 50000 };
    let result = await axios.post(url, params, opstions).then(utils.handler);
    // if (!result.data || result.data.code !== 0) {
    //     logger.error(`算法考后巩固组卷失败,url:[${url}], params：${JSON.stringify(params)}`);
    //     throw new Error('组卷失败');
    // }
    // return _.get(result, 'data.data', null);
    return result;
}

/**
 * 考后巩固组卷（简单卷，中等卷，培优卷）
 * @param params
 * @returns {Promise<*>}
 */
async function levelPaper (params) {
    let url = URL.format({
        protocol: ALGO_SERVER.protocol,
        hostname: ALGO_SERVER.hostname,
        pathname: '/past_exam/level_paper',
        port: ALGO_SERVER.port,
    });
    let opstions = { headers: {}, timeout: 50000 };
    let result = await axios.post(url, params, opstions).then(utils.handler);
    return result;
}


/**
 * 教师计划试卷推荐
 * wiki: https://wiki.iyunxiao.com/pages/viewpage.action?pageId=29688486
 * @param params
 * @returns {Promise<unknown>}
 */
async function recommendPaper(params) {
    let url = URL.format({
        protocol: ALGO_SERVER.protocol,
        hostname: ALGO_SERVER.hostname,
        pathname: '/recommend/organize_paper',
        port: ALGO_SERVER.port,
    });
    let opstions = { headers: {}, timeout: 50000 };
    let result = await axios.post(url, params, opstions);
    if (!result.data || result.data.code !== 0) {
        logger.error(`算法考后巩固组卷失败,url:[${url}], params：${JSON.stringify(params)}`);
        return null;
    }
    return _.get(result, 'data.data', null);
}


async function recommendQuestions(params) {
    let url = URL.format({
        protocol: ALGO_SERVER.protocol,
        hostname: ALGO_SERVER.hostname,
        pathname: '/recommend/questions',
        port: ALGO_SERVER.port,
    });
    let opstions = { headers: {}, timeout: 50000 };
    let result = await axios.post(url, params, opstions);
    if (!result.data || result.data.code !== 0) {
        logger.error(`算法考后巩固组卷失败,url:[${url}], params：${JSON.stringify(params)}`);
        return null;
    }
    return _.get(result, 'data.data', null);
}


/**
 * 薄弱知识点组卷
 * @param params
 * @returns {Promise<null|GetFieldType<AxiosResponse<any>, string>>}
 */
async function detailTablePaper(params) {
    let url = URL.format({
        protocol: ALGO_SERVER.protocol,
        hostname: ALGO_SERVER.hostname,
        pathname: '/detail_table/paper',
        port: ALGO_SERVER.port,
    });
    let opstions = { headers: {}, timeout: 50000 };
    let result = await axios.post(url, params, opstions);
    if (!result.data || result.data.code !== 0) {
        logger.error(`算法考后巩固组卷失败,url:[${url}], params：${JSON.stringify(params)}`);
        return null;
    }
    return _.get(result, 'data.data', null);
}

async function knowledgesPaper(params) {
    let url = URL.format({
        protocol: ALGO_SERVER.protocol,
        hostname: ALGO_SERVER.hostname,
        pathname: '/knowledges/paper',
        port: ALGO_SERVER.port,
    });
    let opstions = { headers: {}, timeout: 50000 };
    let result = await axios.post(url, params, opstions);
    if (!result.data || result.data.code !== 0) {
        logger.error(`算法知识点组卷失败,url:[${url}], params：${JSON.stringify(params)}`);
        return null;
    }
    return _.get(result, 'data.data', null);
}
