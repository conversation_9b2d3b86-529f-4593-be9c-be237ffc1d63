const axios = require('axios');
const URL = require('url');

let albums = [
    {
        'name': '2019年高考真卷',
        'type': '高考真卷',
        'period': '高中',
        'subject': '语文',
        'is_elite': true,
        'groups': [{
            'name': '',
            '_e': [
                2591647172,
                2589156804,
                2588894660,
                2587846084,
                2588763588,
                2588173764,
                2588370372
            ],
            'exampapers': []
        }]
    },
    {
        'name': '2019年高考真卷',
        'type': '高考真卷',
        'period': '高中',
        'subject': '数学',
        'is_elite': true,
        'groups': [{
            'name': '',
            '_e': [
                2588239300,
                2588698052,
                2583061956,
                2583389636,
                2588829124,
                2588632516,
                2569496004,
                2569299396,
                2573297092,
                2573559236,
                2573493700,
                2575590852
            ],
            'exampapers': []
        }]
    },
    {
        'name': '2019年高考真卷',
        'type': '高考真卷',
        'period': '高中',
        'subject': '英语',
        'is_elite': true,
        'groups': [{
            'name': '',
            '_e': [
                2591516100,
                2591188420,
                2590139844,
                2578998724,
                2589550020,
                2582013380,
                2581358020
            ],
            'exampapers': []
        }]
    },
    {
        'name': '2019年高考真卷',
        'type': '高考真卷',
        'period': '高中',
        'subject': '物理',
        'is_elite': true,
        'groups': [{
            'name': '',
            '_e': [
                2590729668,
                2589091268,
                2580964804,
                2572969412,
                2581030340,
                2581292484,
                2580768196
            ],
            'exampapers': []
        }]
    },
    {
        'name': '2019年高考真卷',
        'type': '高考真卷',
        'period': '高中',
        'subject': '化学',
        'is_elite': true,
        'groups': [{
            'name': '',
            '_e': [
                2588435908,
                2582144452,
                2581489092,
                2573428164,
                2570085828,
                2576049604,
                2573231556
            ],
            'exampapers': []
        }]
    },
    {
        'name': '2019年高考真卷',
        'type': '高考真卷',
        'period': '高中',
        'subject': '生物',
        'is_elite': true,
        'groups': [{
            'name': '',
            '_e': [
                2591581636,
                2591385028,
                2589418948,
                2581751236,
                2581947844,
                2589287876
            ],
            'exampapers': []
        }]
    },
    {
        'name': '2019年高考真卷',
        'type': '高考真卷',
        'period': '高中',
        'subject': '政治',
        'is_elite': true,
        'groups': [{
            'name': '',
            '_e': [
                2589025732,
                2588108228,
                2588042692,
                2573624772,
                2581816772,
                2580506052,
            ],
            'exampapers': []
        }]
    },
    {
        'name': '2019年高考真卷',
        'type': '高考真卷',
        'period': '高中',
        'subject': '地理',
        'is_elite': true,
        'groups': [{
            'name': '',
            '_e': [
                2590795204,
                2588304836,
                2588501444,
                2571068868,
                2578736580,
                2578539972
            ],
            'exampapers': []
        }]
    },
    {
        'name': '2019年高考真卷',
        'type': '高考真卷',
        'period': '高中',
        'subject': '历史',
        'is_elite': true,
        'groups': [{
            'name': '',
            '_e': [
                2590598596,
                2589353412,
                2588960196,
                2575852996,
                2581161412,
                2580637124
            ],
            'exampapers': []
        }]
    }
];
async function getExampaper(eid) {
    let url= `http://kb.yunxiao.com/kb_api/v2/exampapers/${eid}/?fields_type=common&serv_range=public&api_key=iyunxiao_tr19911225`;
    let res = await axios.get(url);

    return res.data;
}
async function insertAlbum(album) {
    const url = URL.format({
        protocol: 'http',
        hostname: 'tiku-serv-lan.yunxiao.com',
        pathname: '/album_api/v1/album/list',
        port: 80,
        query: {
            period: album.period,
            subject: album.subject,
            type: album.type
        }
    });
    let cookie = 'tiku-session-id=6719da848e0ec864081ea55f28ff77ab197f90829085338f8d5b7871aeae4f973355b6688a0407e42b531ebcd6cc13143280f77d3819b39a97f305344014e603f19d1edc6c87246033765b450d65c6dda6b468966d63f80b288901b0dfb8255f7d8138749e9db3241785595c7e39428310ecb8d15d5c0dac09ded468194818a2477b40bef5ecc04d00c3319372ccf60bf8cc803f11f9a145d1846853fc1dfb444be6b1f9b25d9160a54462896e8c5fa7b9f73a973fe066028a3fe88089c04a43f94bbfae66dada98024a4c51aa37ed0e75a4522284e12472f3c44abe99167e1022706d4ba4af5bd2130720fd39caed229ac91b37233acc5d2b2a41bcb125b6dcc94984a9e990afef21564f55d4204425c43e195381b96099a27fe6991d6e8e6a3fc964c1346dcc0c9e6f649f8bc359c85d086907b4808881ed8cdcfd7b83d0f2042b8165eaf59294b562e7990fe81d073e1072ec70746dee5b99a07ad87b5986287df15901f69cf457b512630ea9897c38108b319b9a2841cf34dc916910da472515a53bd01d44a4004d52d2a1922e9fc003872a1f2d5928227ce1e98d8d858fd6f699cdded3c3de7ae32954c869fa777689d3eb0bfaf983f8f92fa54ed1d695937446dfeddab8dbf705558fef1e0569180fe587555947153f8b5254ac4b28b6';

    for (let item of album.groups) {
        let buf = {};
        for (let id of item._e) {
            if (buf[id]) {
                continue;
            }
            buf[id]=1;
            item.exampapers.push({id: id* 1});
        }
        delete item._e;
        for (let ex of item.exampapers) {
            let exam = await getExampaper(ex.id);
            console.log(exam.period ,album.period ,exam.subject , album.subject);
            if (exam.period !== album.period || exam.subject !== album.subject) {
                throw new Error(ex.id + '学段学科不匹配');
            }
        }

    }
    let res = await axios({
        url,
        headers: {
            cookie: cookie
        }
    });
    let list = res.data.data.albums;
    let exist = list.filter(u => u.name ===album.name);
    if (exist.length) {
        return;
    }
    let url1 = URL.format({
        protocol: 'http',
        hostname: 'tiku-serv-lan.yunxiao.com',
        pathname: '/album_api/v1/album',
        port: 80
    });
    let res1 = await axios({
        url:url1,
        method: 'post',
        data: album,
        headers: {
            cookie: cookie
        }
    });
    console.log(res1.data.data.id, album.subject);
}
(async () => {
    try {
        for (let album of albums) {
            await insertAlbum(album);
        }
    } catch (err) {
        console.log(err.stack);
    }
})();
