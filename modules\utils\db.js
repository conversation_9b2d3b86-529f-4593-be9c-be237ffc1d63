var mongodb = require("mongodb");

function MongoDBer(dbs_conf){
    this.dbs = {};
}

MongoDBer.prototype.init(){
    var count = 0;
    var option = {auto_reconnect: true};
    for (var name in dbs_conf){
        ++count;
        var db_url = dbs_conf[name];
	    mongodb.MongoClient.connect(db_url, option, function(err, db){
            if (err){
                return callback(err);
            }
            g_dbs[name] = db;
            --count;
            if (!count){
                return callback(null);
            }
        });
    }
}

MongoClient.prototype.get(name){
    return this.dbs[name];
}


var g_dbs = {};

function init(dbs_conf, callback){
    var count = 0;
    var option = {auto_reconnect: true};
    for (var name in dbs_conf){
        ++count;
        var db_url = dbs_conf[name];
	    mongodb.MongoClient.connect(db_url, option, function(err, db){
            if (err){
                return callback(err);
            }
            g_dbs[name] = db;
            --count;
            if (!count){
                return callback(null);
            }
        });
    }
}

function get(name) {
	return g_dbs[name];
}

module.exports = {
	init: init,
	get: get,
}
