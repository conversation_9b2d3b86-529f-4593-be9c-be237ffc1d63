const config = require('config');
const request = require('request');
const qs = require('querystring');
const URL = require('url');
const KBServerCfg = config.get('KB_API_SERVER');
const mongodber = require('../../utils/mongodber');
const db = mongodber.use('tiku');
const ResponseWrapper = require('../../middlewares/response_wrapper');
const Thenjs = require('thenjs');
const logger = require('../../utils/logger');
const _ = require('lodash');
const excel = require('../../../lib/excel');
const utils = require('../../utils/utils');
const workday = require('chinese-workday');
const rediser = require('../../utils/rediser');
const moment = require('moment');
const axios = require('axios');

function _requestKBUpdate(queryStr, callback) {
    let updateUrl = URL.format({
        protocol: KBServerCfg.protocol,
        hostname: KBServerCfg.hostname,
        port: KBServerCfg.port,
        pathname: 'kb_api/v2/resources/update',
        search: queryStr,
    });

    request({
        url: updateUrl,
        timeout: 2000
    }, function (err, response, data) {
        if (err) {
            return callback(err, null);
        }

        try {
            let _data = JSON.parse(data);

            if (response.statusCode !== 200) {
                logger.error(['获取kb更新信息失败', updateUrl].join(':'));
                return callback('HANDLE_ERROR');
            }
            //var book = _data.book;
            return callback(null, _data);
        } catch (err) {
            logger.error(err.message);
            return callback(err, null);
        }
    });
}


function _requestKBNewHot(queryStr, callback) {
    let updateUrl = URL.format({
        protocol: KBServerCfg.protocol,
        hostname: KBServerCfg.hostname,
        port: KBServerCfg.port,
        pathname: 'kb_api/v2/resources/hots',
        search: queryStr,
    });

    request({
        url: updateUrl,
        // timeout: 2000
    }, function (err, response, data) {
        if (err) {
            return callback(err, null);
        }

        try {
            let _data = JSON.parse(data);

            if (response.statusCode !== 200) {
                logger.error(['获取kb更新信息失败', updateUrl].join(':'));
                return callback('HANDLE_ERROR');
            }
            return callback(null, _data);
        } catch (err) {
            logger.error(err.message);
            return callback(err, null);
        }
    });
}

function _genQueryStr(req) {
    var queryObj = {};
    // queryObj['api_key'] = req.apiKey;
    queryObj['api_key'] = KBServerCfg.appKey;
    queryObj.filter_mkp = true;

    if (req.query.offset) {
        queryObj['offset'] = req.query.offset;
    }
    if (req.query.limit) {
        queryObj['limit'] = req.query.limit;
    }
    if (req.query.fields_type) {
        queryObj['fields_type'] = req.query.fields_type;
    }
    if (req.query.period) {
        queryObj['period'] = req.query.period;
    }
    if (req.query.subject) {
        queryObj['subject'] = req.query.subject;
    }
    if (req.query.region) {
        queryObj['region'] = req.query.region;
    }if (req.query.grade) {
        queryObj['grade'] = req.query.grade;
    }
    return qs.stringify(queryObj);
}

function _getAssembleExampaperUpdateInfo(callback) {

    var startDate = new Date(new Date().setHours(0, 0, 0, 0));
    var endDate = new Date();

    var cond = {ctime: {$gte: startDate, $lt: endDate}};

    db.collection('exampaper').count(cond, function (err, num) {
        if (err) {
            return callback(err, null);
        }
        //return callback(null, num);
        db.collection('exampaper').count({}, function (err, totalNum) {
            if (err) {
                return callback(err, null);
            }
            return callback(null, [num, totalNum]);
        });
    });
}

function update(req, res) {
    var responseWrapper = new ResponseWrapper(req, res);
    try {
        if (!req.query.fields_type) {
            req.query.fields_type = 'all';
        }
        if (req.query.offset) {
            if (isNaN(Number(req.query.offset))) {
                return responseWrapper.error('PARAMETERS_ERROR', 'offset参数错误');
            }
        }

        if (req.query.limit) {
            if (isNaN(Number(req.query.limit))) {
                return responseWrapper.error('PARAMETERS_ERROR', 'limit参数错误');
            }
        }

        if (['all', 'exampaper', 'assembleExampaper', 'question'].indexOf(req.query.fields_type) === -1) {
            return responseWrapper.error('PARAMETERS_ERROR', 'fields_type参数错误');
        }

    } catch (e) {
        return responseWrapper.error('PARAMETERS_ERROR');
    }

    Thenjs(function (cont) {
        if (['all', 'assembleExampaper'].indexOf(req.query.fields_type) !== -1) {
            _getAssembleExampaperUpdateInfo(function (err, numArray) {
                if (err) {
                    return cont(err);
                }
                var assemble = {};
                assemble['num'] = numArray[0];
                assemble['totalNum'] = numArray[1];
                if (req.query.fields_type === 'assembleExampaper') {
                    return responseWrapper.succ({'assemExampaper': assemble});
                } else {
                    return cont(null, assemble);
                }
            });
        } else {
            return cont(null, null);
        }
    }).then(function (cont, assemble) {
        var queryStr = _genQueryStr(req)
        _requestKBUpdate(queryStr, function (err, retData) {
            if (err) {
                return cont(err);
            }
            if (assemble) {
                retData['assemExampaper'] = assemble;
            }
            return responseWrapper.succ(retData);
        });
    }).fail(function (cont, err) {
        return responseWrapper.error('HANDLE_ERROR');
    });
}


const _group_subjects = sourceSubjects => {
    let subjects = [];
    sourceSubjects.forEach(subjectName => {
        let subject = _.find(subjects, e => e.name === subjectName);
        if (subject) {
            subject.num++;
        } else {
            subjects.push({name: subjectName, num: 1});
        }
    });
    return subjects;
};

// 获取 组卷 总量统计信息
async function getExampaperTotalStatistics(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let exampaperAggregation = await db.collection('exampaper').aggregate([
            {$sort: {period: 1, subject: 1}},
            {$project: {_id: 1, period: 1, subject: 1}},
            {
                $group: {
                    _id: '$period',
                    period: {$first: '$period'},
                    num: {$sum: 1},
                    subjects: {$push: '$subject'},
                }
            },
        ], {allowDiskUse: true}).toArray();

        responseWrapper.succ({
            num: exampaperAggregation.reduce((value, b) => value + b.num, 0),
            periods: exampaperAggregation.map(exampaper => ({
                period: exampaper.period,
                num: exampaper.num,
                subjects: _group_subjects(exampaper.subjects),
            })),
        });
    } catch (e) {
        logger.error(e);
        responseWrapper.error('HANDLE_ERROR', e);
    }
}

// 组卷学校统计信息
async function getExampaperSchoolStatistics(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let schoolAggregations = (await Promise.all(['整体', '高中', '初中', '小学'].map(period => db.collection('exampaper').aggregate([
            {$sort: {sch_id: 1}},
            ...period === '整体' ? [] : [{$match: {period: period}}],
            {
                $group: {
                    _id: '$sch_name',
                    num: {$sum: 1},
                }
            },
            {$sort: {num: -1}},
            {$limit: 50},
            {$project: {_id: 0, name: '$_id', num: 1}},
        ], {allowDiskUse: true}).toArray()))).map((schools, index) => {
            return {
                period: ['整体', '高中', '初中', '小学'][index],
                num: schools.reduce((value, e) => value + e.num, 0),
                schools: schools.sort((a, b) => b.num - a.num).filter(e => !!e.name),
            };
        });
        schoolAggregations.num = await db.collection('exampaper').count({});
        responseWrapper.succ(schoolAggregations);
    } catch (e) {
        logger.error(e);
        responseWrapper.error('HANDLE_ERROR', e);
    }
}

// 组卷省份统计信息
async function getExampaperRegionStatistics(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let regionAggregations = (await Promise.all(['整体', '高中', '初中', '小学'].map(period => db.collection('exampaper').aggregate([
            {$sort: {province: 1}},
            ...period === '整体' ? [] : [{$match: {period: period}}],
            {
                $group: {
                    _id: '$province',
                    num: {$sum: 1},
                }
            },
            {$project: {_id: 0, name: '$_id', num: 1}},
        ], {allowDiskUse: true}).toArray()))).map((provinces, index) => {
            return {
                period: ['整体', '高中', '初中', '小学'][index],
                num: provinces.reduce((value, e) => value + e.num, 0),
                regions: provinces.sort((a, b) => b.num - a.num).filter(e => !!e.name),
            };
        });
        responseWrapper.succ(regionAggregations);
    } catch (e) {
        logger.error(e);
        responseWrapper.error('HANDLE_ERROR', e);
    }
}

// 获取 组卷 今日更新统计信息
async function getExamUpdateStatistics(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        // 前一天的时间
        let endDate = new Date(new Date().setHours(32, 0, 0, 0));
        let startDate = new Date(new Date().setHours(8, 0, 0, 0));

        // FAKE TIME
        // startDate = new Date('2018-9-1');

        let exampaperUpdate = await db.collection('exampaper').aggregate([
            {$match: {ctime: {$gte: startDate, $lt: endDate}}},
            {$project: {_id: 1, period: 1, subject: 1}},
            {
                $group: {
                    _id: '$period',
                    period: {$first: '$period'},
                    num: {$sum: 1},
                    subjects: {$push: '$subject'},
                }
            },
        ], {allowDiskUse: true}).toArray();
        responseWrapper.succ({
            num: exampaperUpdate.reduce((value, b) => value + b.num, 0),
            periods: exampaperUpdate.map(exampaper => ({
                period: exampaper.period,
                num: exampaper.num,
                subjects: _group_subjects(exampaper.subjects),
            })),
        });
    } catch (e) {
        logger.error(e);
        responseWrapper.error('HANDLE_ERROR', e);
    }
}

function hots(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        if (req.query.offset) {
            if (isNaN(Number(req.query.offset))) {
                return responseWrapper.error('PARAMETERS_ERROR', 'offset参数错误');
            }
        }

        if (req.query.limit) {
            if (isNaN(Number(req.query.limit))) {
                return responseWrapper.error('PARAMETERS_ERROR', 'limit参数错误');
            }
        }
    } catch (e) {
        return responseWrapper.error('PARAMETERS_ERROR');
    }
    const limit = req.query.limit;
    req.query.limit = 10; // 默认设定
    const result = {
        assem_exampaper: {
            num: 0,
            total: 0
        },
        exampaper: {  // 试卷
            total: 0,
            num: 0,
            hot: [], // 最热
            'new': [], // 最新
        },
        question: { // 试题
            total: 0,
            num: 0
        }
    };
    const redis_key = `stat_question_exampaper_num`;
    Thenjs(function (cont) {
        cont();
        // 组卷
        // _getAssembleExampaperUpdateInfo(function (err, numArray) {
        //     if (err) {
        //         return cont(err);
        //     }
        //     let assemble = {};
        //     assemble['num'] = numArray[0];
        //     assemble['total_num'] = numArray[1];
        //     return cont(null, assemble);
        // });
    }).then(function (cont, assemble) {
        let queryStr = _genQueryStr(req);
        _requestKBNewHot(queryStr, function (err, retData) {
            if (err) {
                return cont(err);
            }
            // if (assemble) {
            //     retData['assem_exampaper'] = assemble;
            // }
            // retData.question['total_num'] = retData.question.totalNum;
            // delete retData.question.totalNum;
            // return responseWrapper.succ(retData);

            result.exampaper.hot = retData.exampaper.hot;
            result.exampaper.new = retData.exampaper.new;
            if (_.size(result.exampaper.hot) > limit) {
                result.exampaper.hot = result.exampaper.hot.slice(0, limit);
            }
            if (_.size(result.exampaper.new) > limit) {
                result.exampaper.new = result.exampaper.new.slice(0, limit);
            }
            return cont();
        });
    }).then((cont, arg) => {
        rediser.get(redis_key, (err, data) => {
           if (err || _.isEmpty(data)) return cont();
           result.question.num = data.question.num;
           result.exampaper.num = data.exampaper.num;
           return responseWrapper.succ(result);
        });
    }).then((cont, arg) => {
        //
        const redisData = {
            question: {
                num: 0
            },
            exampaper: {
                num: 0
            }
        };

        // 随机试卷，试题 根据昨天
        const isWork = workday.isWorkday(moment().subtract(1, 'day').toDate());
        if (isWork) {
            redisData.exampaper.num = utils.randomRange(1000, 1500);
            redisData.question.num = utils.randomRange(13000, 16000);
        } else {
            redisData.exampaper.num = utils.randomRange(800, 1000);
            redisData.question.num = utils.randomRange(8000, 10000);
        }
        const date = moment().endOf('day').toDate();
        rediser.set(redis_key, redisData, parseInt((date.getTime() - Date.now()) / 1000), (err, data) => {
            if (err) {
                logger.error(err);
            }
            result.question.num = redisData.question.num;
            result.exampaper.num = redisData.exampaper.num;
            return responseWrapper.succ(result);
        });
    }).fail(function (cont, err) {
        return responseWrapper.error('HANDLE_ERROR');
    });
}

const postAccessSpots = async (req, res) => {
    process.nextTick(async function () {
        try {
            let body = req.body;
            if (req.user) {
                body.user_id = req.user.id;
                body.school_id = req.user.schoolId;
                body.province = req.user.province;
                body.city = req.user.city;
            }
            body.ip = req.ip;
            body.timestamp = new Date();
            let userInfo = await db.collection('user').findOne({ _id: body.user_id });
            if (userInfo) {
                let curr = userInfo.curr || {};
                body.period = curr.period || '';
                body.subject = curr.subject || '';

                db.collection('access_spot').insert(body);
            }
        } catch (err) {
            logger.error(err.stack);
        }
    });
    return res.end();
};

const event = (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    let startDate = new Date(new Date().setHours(0, 0, 0, 0));
    let endDate = new Date();
    let type = req.query.type ? req.query.type : 'all';
    if (req.query.stime && req.query.stime.length === 13)
        startDate = new Date(Number(req.query.stime));
    if (req.query.etime && req.query.etime.length === 13)
        endDate = new Date(Number(req.query.etime));

    try {
        let cont = {timestamp: {$gte: startDate, $lt: endDate}};
        db.collection('access_spot').find(cont).project({event_id:1, user_id:1}).toArray(function (err, accessSpots) {
            if (err) {
                return responseWrapper.error('HANDLE_ERROR', err);
            }
            // excel.getPvPcExcelFromTable(accessSpots, 'data/xlsx');
            accessSpots = excel.getPvPcDataFromTable(accessSpots, type);
            return responseWrapper.succ(accessSpots);
        });
    } catch (error) {
        logger.error(error.stack);
        return responseWrapper.error('HANDLE_ERROR', error.message);
    }
};

const getAccessSpots = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let offset = isNaN(req.query.offset * 1) ? 0 : req.query.offset * 1;
        let limit = isNaN(req.query.limit * 1) ? 10 : req.query.limit * 1;
        let eventId = req.query.event_id;
        let docs = await db.collection('access_spot').find({
            event_id: eventId
        }).sort({_id: -1}).skip(offset).limit(limit).toArray();
        return responseWrapper.succ(docs);
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
}

async function getRegionExampapers(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        req.query.offset = 0;
        let url = URL.format({
            protocol: KBServerCfg.protocol,
            hostname: KBServerCfg.hostname,
            port: KBServerCfg.port,
            pathname: 'kb_api/v2/resources/region',
            search: _genQueryStr(req),
        });
        request({
            url: url,
        },  (err, response, data) => {
            if (err) {
                logger.error(err);
                return responseWrapper.error('HANDLE_ERROR', err.message);
            }

            try {
                let _data = JSON.parse(data);
                if (response.statusCode !== 200) {
                    return responseWrapper.error('HANDLE_ERROR', 'KB接口异常');
                }
                return responseWrapper.succ(_data);
            } catch (err) {
                logger.error(err);
                return responseWrapper.error('HANDLE_ERROR', err.message);
            }
        });
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
}

async function getRegionExampapersV2(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const cacheKey = `tiku:exampaper:region:${req.query.period}:${req.query.subject}:${req.query.region}:${req.query.limit}`;
        const cacheResult = await rediser.redis.get(cacheKey);
        if (cacheResult) return responseWrapper.succ(JSON.parse(cacheResult));
        const url = URL.format({
            protocol: KBServerCfg.protocol,
            hostname: KBServerCfg.hostname,
            port: KBServerCfg.port,
            pathname: `/kb_api/v2/exampapers/by_search`,
            search: qs.stringify({
                api_key: req.apiKey,
            })
        });

        const data = {
            filter_mkp: 'true',
            period: req.query.period,
            subject: req.query.subject,
            province: req.query.region.split(',')[0],
            offset: 0,
            limit: 50,
            sort_by: 'year'
        }
        // 过滤mkp 题目、卷子
        // 根据
        const checkDate = moment().month(7).date(15).startOf('day');
        const year = moment().year();
        if (moment().isBefore(checkDate)) {
            data.to_year = `${year - 1},${year}`;
        } else {
            data.to_year = `${year},${year + 1}`;
        }
        const result = await axios.post(url, data);
        if (!result || result.status !== 200 || !result.data) {
            logger.error(`KB获取试题信息失败: url: ${url}, status: ${result.status}, params: ${JSON.stringify(data)}`);
            throw new Error('获取试题信息失败');
        }
        let teacher_names = ['刘建雪', '陈晓慧', '隋晓妍', '莉莉', '孙敏', '花瑞琴', '李光英', '杨乃仟', '刘帅'];
        let list = _.get(result, 'data.exampapers', []);
        list = list.filter(e => {
           if (e.city === '安阳' && e.hasOwnProperty('user_name') && teacher_names.includes(e.user_name)) return false;
           return true;
        });
        if (_.size(list) < req.query.limit) { // 试卷数量不够补充全国
            data.province = '全国';
            const fullResult = await axios.post(url, data);
            if (!result || result.status !== 200 || !result.data) {
                return responseWrapper.succ([]);
            }
            let full_list = _.get(fullResult, 'data.exampapers', []);
            if (_.size(full_list)) {
                full_list = full_list.filter(e => {
                    if (e.city === '安阳' && e.hasOwnProperty('user_name') && teacher_names.includes(e.user_name)) return false;
                    return true;
                });
                if (_.size(full_list) > 10) {
                    full_list = full_list.splice(10);
                }
                full_list.forEach(e => {
                    list.push(e);
                });
            }

        }
        list = list.splice(0, list.length >= req.query.limit ? req.query.limit : list.length);
        list = list.map(e => {
            return {
                id: e.id,
                name: e.name,
                type: e.type,
                view_times: e.view_times || 0,
                download_times: e.download_times || 0,
                ctime: moment(e.ctime).format('YYYY年MM月DD日')
            }
        });
        rediser.set(cacheKey, list, 60 * 60 * 2);
        return responseWrapper.succ(list);
    } catch (e) {
        logger.error(e);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}
module.exports = {
    update,
    getExamUpdateStatistics,
    getExampaperTotalStatistics,
    getExampaperSchoolStatistics,
    getExampaperRegionStatistics,
    hots,
    postAccessSpots,
    event,
    getAccessSpots,
    getRegionExampapers,
    getRegionExampapersV2
};
