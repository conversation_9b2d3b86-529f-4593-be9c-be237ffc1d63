const _ = require('lodash');
const ObjectId = require("mongodb").ObjectId;
const util = require('util');
const template = require('../assemble_api/v1/template');
const TYPES = template.TYPES;
const DIGIT_MAP_CHINESE = template.DIGIT_MAP_CHINESE;
const client = require('../client');
const mongodber = require('./mongodber');
const db = mongodber.use('tiku');
const db_open = mongodber.use('tiku_open');
const db_zyk = mongodber.use('kb_zyk');
const db_kb_zyk = mongodber.use('kb_zyk');
// const deleteQuestionBlocks = require('../assemble_api/v1/utils').deleteQuestionBlocks;
const enums = require('../../bin/enum');
const utils = require('../utils/utils');

module.exports = {
    build_algo_paper,
    set_display,
    get_question_num,
    get_exampaper_detail,
    render_basket,
    render_basket_question_score,
    insert_questions,
    replace_question,
    delete_question,
    build_by_questions,
    add_some_info,
    //***************
    get_profile_basket,
    init,
    getZxQuestionByIds,
    getZykQuestionByIds,
    traverse_questions,
    deleteQuestionBlocks,
    ques_process,
};

async function build_algo_paper(user, algo_paper) {
    const { blocks, name } = algo_paper;
    const paper = _init();
    const date = new Date();
    const {from_year, to_year} = utils.getAcademicYear();
    _build_user_info(user, paper);
    paper.ctime = date;
    paper.utime = date;
    paper.type = '考后巩固';
    paper.from_year = from_year;
    paper.to_year = to_year;
    paper.source_type = enums.PaperSourceType.EXAM;
    paper.source = enums.PaperSourceType.EXAM;
    paper.valid = enums.BooleanNumber.YES;
    paper.status = enums.PaperStatus.DONE;
    paper.exam_status = enums.ExamStatus.EDITABLE;
    // paper.category = category;
    // paper.display = 1; // 默认展示
    // paper.period = period;
    // paper.subject = subject;
    // paper.grade = grade;

    const ids = [];
    for (const b of blocks) {
        for (const q of b.questions) {
            ids.push(q.id);
        }
    }
    const kb_questions = await client.kb.getQuestions(ids);
    const ques_map = {};
    for (const q of kb_questions) {
        ques_map[q.id] = q;
    }
    for (const q of kb_questions) {
        if (_.isEmpty(q)) continue;
        const simple_question =  _.pick(q, ['id', 'type', 'period', 'subject']);
        insert_questions(paper, simple_question);
    }
    render_basket(paper);
    if (name) {
        paper.name = name;
    }
    return paper;
}

function build_by_questions({user, questions, period, subject, grade, type, name, category} = params) {
    const date = new Date();
    const paper = _init();
    if (name) paper.name = name;
    _build_user_info(user, paper);
    paper.ctime = date;
    paper.utime = date;
    paper.type = type;
    if (category) paper.category = category;
    // paper.display = 1; // 默认展示
    paper.period = period;
    paper.subject = subject;
    paper.grade = grade;
    for (const q of questions) {
        if (_.isEmpty(q)) continue;
        const simple_question =  _.pick(q, ['id', 'type', 'period', 'subject']);
        insert_questions(paper, simple_question);
    }
    render_basket(paper);
    return paper;
}

function set_display(paper, display) {
    paper.display = display;
}

function _build_user_info(user, paper) {
    if (_.isEmpty(user)) return paper;
    paper.user_id = utils.formatUserId(user.id);
    paper.sch_id = user.schoolId;
    paper.sch_name = user.schoolName;
    paper.province = user.province;
    paper.city = user.city;
}

function init() {
    return _init();
}

/**
 * 初始化试卷结构
 * @returns {{template: string, period: string, subject: string, attentions: null, volumes: [{note: string, blocks: [], title: string}, {note: string, blocks: [], title: string}], secret_tag: null, duration: null, score: null, gutter: null, subtitle: null, name: null, _id: *, paper_info: null, cand_info: null}}
 * @private
 */
function _init() {
    let paper = {
        // user_id: user_id,
        period: '',       // 学段
        subject: '',      // 学科
        name: null,         // 试卷名称
        subtitle: null,     // 副标题
        score: null,        // 试卷总分
        duration: null,     // 考试时间，单位分钟
        paper_info: null,   // 试卷信息栏，
        cand_info: null,
        attentions: null,   // 注意事项
        secret_tag: null,   // 保密标记文字
        gutter: null,       // 装订线
        template: 'standard',   // 组卷类型，stardard, exam, homework
        partsList: ["name", "subtitle", "paper_info", "gutter", "attentions", "volumes", "blocks", "secret_tag"],
        volumes: [{
            title: '卷I（选择题）',  // 卷I分卷名
            //note: '请点击修改第I卷的文字说明', // 分卷说明
            note: '', // 分卷说明
            blocks: [],
        }, {
            title: '卷II（非选择题）', // 卷II分卷名
            //note: '请点击修改第II卷的文字说明', // 分卷说明
            note: '', // 分卷说明
            blocks: [],
        }]
    }
    return paper;
}


function render_basket(paper) {
    render_basket_question_score(paper);
    // 试卷名称， 2017年5月3日
    // var t = new Date();
    // var info = `${t.getFullYear()}年${t.getMonth() + 1}月${t.getDate()}日${basket.period}${basket.subject}`;
    paper.name = paper.name || _gen_paper_name(paper);
    // 副标题
    paper.subtitle = paper.subtitle || '';
    // 考试时间，单位分钟
    paper.duration = paper.duration || 120;
    // 试卷信息栏，考试总分：100分；考试时间：100分钟
    const paper_info = `考试总分：${paper.score}   考试时间：${paper.duration}`;
    paper.paper_info = paper.paper_info || paper_info;
    // 候选人信息栏
    const line = '__________ ';
    const cand_info = `学校：${line}班级：${line}姓名：${line}考号：${line}`;

    paper.cand_info = paper.cand_info || cand_info;
    // 注意事项
    paper.attentions = paper.attentions || '注意事项：<br>1．答题前填写好自己的姓名、班级、考号等信息; <br>2．请将答案正确填写在答题卡上;<br>';
    // 保密标记文字
    paper.secret_tag = paper.secret_tag || '绝密★启用前';
    // 装订线
    paper.gutter = paper.gutter || 0;
    return paper;
}

function render_basket_question_score(paper) {
    // 试题篮分卷信息
    paper.score = 0;
    let block_num = 0;
    for (const volume of paper.volumes) {
        for (const block of volume.blocks) {
            const questions = block.questions;
            const n = questions.length;
            if (questions.length <= 0) {
                continue;
            }
            let s = Number(questions[0].score);
            let ts = Number(s);
            let tag = true;
            for (let i = 1; i < n; ++i) {
                if (questions[i].score !== questions[i - 1].score) {
                    tag = false;
                }
                ts += Number(questions[i].score);
            }
            const order = `${DIGIT_MAP_CHINESE[++block_num]}`;
            const ss = tag ? `，每题${s}分` : '';
            block.default_score = tag ? s : block.default_score;
            const detail = `本大题共计${n}小题${ss}，共计${ts}分`;
            const note = (block.note.length > 0) ? `，${block.note}` : '';
            block.title = `${order}、${block.type}（${detail}${note}）`;
            // block.title = `${block.type}（${detail}${note}）`;
            paper.score += Number(ts);
        }
    }
}


function _gen_paper_name(paper) {
    const nowDate = new Date();
    const year = nowDate.getFullYear();

    // 学年
    const academicYearStartDate = new Date();
    academicYearStartDate.setMonth(0, 1);
    academicYearStartDate.setHours(0, 0, 0, 0);

    const academicYearEndDate = new Date();
    academicYearEndDate.setMonth(7, 15);
    academicYearEndDate.setHours(0, 0, 0, 0);
    let academicYearLong = `${year.toString()}-${(year + 1).toString()}`;
    if (nowDate.getTime() >= academicYearStartDate.getTime() && nowDate.getTime() < academicYearEndDate.getTime()) {
        academicYearLong = `${(year - 1).toString()}-${year.toString()}`;
    }
    // 学期
    const semesterStartDate = new Date();
    semesterStartDate.setMonth(1, 15);
    semesterStartDate.setHours(0, 0, 0, 0);

    const semesterEndDate = new Date();
    semesterEndDate.setMonth(7, 15);
    semesterEndDate.setHours(0, 0, 0, 0);

    let semester = "上";//学期
    if (nowDate.getTime() >= semesterStartDate.getTime() && nowDate.getTime() < semesterEndDate.getTime()) {
        semester = '下';
    }
    const formatDateStr = `${nowDate.getFullYear()}年${nowDate.getMonth()+1}月${nowDate.getDate()}日`;
    let info = `${academicYearLong}学年${paper.period}${paper.subject} (${semester}) ${paper.type || ''}试卷(${formatDateStr})`;
    return info;
}
//
// function insert_questions(basket, ques) {
//     const ids = [];
//     for (const v of basket.volumes || []) {
//         for (const b of v.blocks) {
//             ids.push(...b.questions.map(e => e.id));
//         }
//     }
//     if (ids.includes(ques.id)) return basket;
//     const type = ques['type'];
//     const type_t = TYPES[type] || TYPES['default'];
//
//     const vol_pos = type_t['vol_pos'];
//     const volume = basket.volumes[vol_pos];
//
//     basket.period = ques['period'] || '';
//     basket.subject = ques['subject'] || '';
//     if (ques['exampaper_type'] && ques['exampaper_type'] !== enums.ExamPaperOtherType) {
//         basket.type = ques['exampaper_type'];
//     }
//
//     const ques_ = {
//         id: ques['id'],
//         period: ques['period'],
//         subject: ques['subject'],
//     }
//
//     for (const b in volume.blocks) {
//         const block = volume.blocks[b];
//         if (block.type !== type) {
//             continue
//         }
//         // deduplicated
//         const questions = block.questions;
//         for (const q in questions) {
//             if (questions[q]['id'] === ques['id']) {
//                 return basket;
//             }
//         }
//         ques_['score'] = ques['score'] ? ques['score'] : block['default_score'];
//         block.questions.push(ques_);
//         return basket;
//     }
//     // init a new block and push the question
//     ques_['score'] = ques['score'] ? ques['score'] : type_t['default_score'];
//     const block = {
//         title: '',
//         note: '',
//         type: type,
//         default_score: type_t['default_score'],
//         questions: [ques_],
//     };
//     // find the proper postion to insert the block
//     const blk_pos = type_t['blk_pos'];
//     for (const i in volume.blocks) {
//         const type_t_ = TYPES[volume.blocks[i].type] || TYPES['default'];
//         const pos = type_t_['blk_pos'];
//         if (pos > blk_pos) {
//             volume.blocks.splice(parseInt(i), 0, block);
//             return basket;
//         }
//     }
//     // not find proper postion, then insert to the last
//     volume.blocks.push(block);
//     return basket;
// }

/**
 * 替换试卷试题
 * @param paper 试卷
 * @param question_id 替换试题ID
 * @param new_question 新试题
 */
function replace_question(paper, question_id, new_question) {
    const new_ques = {
        id: new_question['id'],
        period: new_question['period'],
        subject: new_question['subject']
    }
    for (const v of paper.volumes) {
        for (const b of v.blocks) {
            for (const i in b.questions) {
                const ques = b.questions[i];
                if (ques.id === question_id) {
                    new_ques['score'] = new_question['score'] ? new_question['score'] : b['default_score'];
                    b.questions[i] = new_ques;
                }
            }
        }
    }
}

function delete_question(paper, id) {
    for (const volume of paper.volumes) {
        for (const block of volume.blocks) {
            block.questions = (block.questions || []).filter(e => e.id !== id);
        }
        volume.blocks = volume.blocks.filter(e => _.size(e.questions));
    }
}

function add_some_info(paper, info) {
    _.assign(paper, info);
}

function get_question_num(paper) {
    let num = 0;
    let blocks = [];
    if (paper.hasOwnProperty('volumes')) {
        for (const volume of paper.volumes) {
            blocks.push(...volume.blocks);
        }
    } else {
        blocks = _.get(paper, 'blocks', []);
    }
    for (const block of blocks) {
        num +=  _.chain(block).get('questions', []).size().value();
    }
    return num;
}

async function get_exampaper_detail(id, answer = false) {
    let query = {
        _id: new ObjectId(id)
    };
    let from = 'zx';
    let paper = await db_open.collection(enums.OpenSchema.user_paper).findOne(query);
    if (!_.isEmpty(paper)) {
        let options = {
            $inc: {
                view_count: 1
            }
        }
        await db_open.collection(enums.OpenSchema.user_paper).updateOne(query, options);
    }
    if (_.isEmpty(paper)) {
        paper = await db_kb_zyk.collection('assemble').findOne(query);
        from = 'zyk';
        if (_.isEmpty(paper)) {
            paper = await db_kb_zyk.collection('exampapers').findOne(query);
        }
    }
    if (_.isEmpty(paper)) return null;
    await extend_ques_async(paper, from);
    // 处理试题题号和去除题内题号
    ques_process(paper);
    // 隐藏试题答案、解析、解答
    if (!answer) {
        deleteQuestionBlocks(paper.volumes);
    }
    return paper;
}

async function extend_ques_async(basket, from = 'zx') {
    const quesMap = {}, kb_ids = [] ,zyk_ids = [], zx_ids = [];
    for (const volume of basket.volumes) {
        for (const block of volume.blocks) {
            for (const q of block.questions) {
                if (_.isNumber(q.id)) {
                    kb_ids.push(q.id);
                } else {
                    if (from === 'zyk') { // 校本
                        zyk_ids.push(q.id);
                    } else if (from === 'zx') { // 智学
                        if (q.source && q.source === 'zx') {
                            zx_ids.push(q.id);
                        } else if (q.source && q.source === 'zyk') {
                            zyk_ids.push(q.id);
                        } else if (q.source === enums.QuestionSource.UPLOAD) {
                            quesMap[q.id] = q;
                            // } else { 移除默认逻辑
                            //     // 上传数据异常block.questions 没有 source，默认为 zx题目
                            //     zx_ids.push(q.id);
                        }
                    }
                }
            }
        }
    }
    if (_.size(kb_ids)) {
        const questions = await client.kb.getQuestions(kb_ids);
        if (_.size(questions)) questions.forEach(q => quesMap[q.id] = q);
    }

    if (_.size(zyk_ids)) {
        const questions = await getZykQuestionByIds(zyk_ids);
        if (_.size(questions)) questions.forEach(q => quesMap[q.id] = q);
    }
    if (_.size(zx_ids)) {
        const questions = await getZxQuestionByIds(zx_ids);
        if (_.size(questions)) questions.forEach(q => quesMap[q.id] = q);
    }
    traverse_questions(basket, quesMap, true);
}

function get_profile_basket(user_id, questions) {
    let result = [];
    if (!_.size(questions)) return result;
    const basket = init(user_id);
    for (const ques of questions) {
        insert_questions(basket, ques);
    }
    result = profile_basket(basket);
    return result;
}

/**
 * 获取试题篮摘要信息
 * @param basket
 * @returns {[]}
 * @private
 */
function profile_basket(basket) {
    let profile = [];
    if (!basket) {
        return profile;
    }
    let volumes = basket.volumes;
    if (!volumes) {
        return profile;
    }
    for (let volume of volumes) {
        let blocks = volume['blocks'] || [];
        for (let block of blocks) {
            if (!block) {
                continue;
            }
            let type = block['type'];
            let questions = block['questions'];
            profile.push({
                type: type,
                questions: questions.map(function (x) {
                    return {
                        id: x['id'],
                        period: x['period'],
                        subject: x['subject'],
                        source: x['source'],
                        source_id: x['source_id'],
                    };
                }),
            });
        }
    }
    return profile;
}

/**
 * 获取云校智学试题
 * @param ids {[]}
 * @return {Promise<*>}
 */
async function getZxQuestionByIds(ids) {
    if (!_.size(ids)) return [];
    const questions = await db_open.collection(enums.OpenSchema.user_question).find({_id: {$in: ids.map(e => new ObjectId(e))}}).toArray();
    for (const q of questions){
        q.id = q._id.toString();
        delete q._id;
    }
    return questions;
}

/**
 * 获取老的校本题库试题
 * @param ids
 * @return {Promise<*[]|*>}
 */
async function getZykQuestionByIds(ids) {
    if (!_.size(ids)) return [];
    const questions = await db_zyk.collection('questions').find({_id: {$in: ids.map(e => new ObjectId(e.toString()))}}).toArray();
    const knowledge_set = new Set();
    for (const q of questions) {
        q.id = q._id.toString();
        delete q._id;
        if (_.size(q.knowledges)) {
            q.knowledges.forEach(e => {
                if (!_.isEmpty(e)) knowledge_set.add(e.id);
            });
        }
        const block_knowledges = _.get(q, 'blocks.knowledges', []);
        if (_.size(block_knowledges)) {
            block_knowledges.forEach(e => {
                if (_.size(e)) {
                    e.forEach(ee => {
                        if (!_.isEmpty(ee)) knowledge_set.add(ee.id)
                    })
                }
            });
        }
    }
    // 加载知识点
    const knowledges = await db_zyk.collection('knowledges').find({_id: {$in: [...knowledge_set] }}).toArray();
    knowledges.forEach(e => {
        e.id = e._id.toString();
        delete e._id;
    });
    for (const q of questions) {
        for (const i in q.knowledges) {
            const k = q.knowledges[i];
            if (!k) continue;
            const knowledge = knowledges.find(e => e.id === k.id.toString());
            if (knowledge) q.knowledges[i] = knowledge;
        }
        const block_knowledges = _.get(q, 'blocks.knowledges', []);
        if (_.size(block_knowledges)) {
            for (const i in q['blocks']['knowledges']) {
                for (const j in q['blocks']['knowledges'][i]) {
                    const k = q['blocks']['knowledges'][i][j];
                    if (!k) continue;
                    const knowledge = knowledges.find(e => e.id === k.id.toString());
                    if (knowledge) q['blocks']['knowledges'][i][j] = knowledge;
                }
            }
        }
    }
    return questions;
}


function insert_questions(basket, ques) {
    const ids = [];
    for (const v of basket.volumes || []) {
        for (const b of v.blocks) {
            ids.push(...b.questions.map(e => e.id));
        }
    }
    if (ids.includes(ques.id)) return basket;
    const type = ques['type'];
    const type_t = TYPES[type] || TYPES['default'];

    const vol_pos = type_t['vol_pos'];
    const volume = basket.volumes[vol_pos];

    // basket.period = ques['period'] || '';
    // basket.subject = ques['subject'] || '';
    if (ques['exampaper_type'] && ques['exampaper_type'] !== '其他') {
        basket.type = ques['exampaper_type'];
    }

    const ques_ = {
        id: ques['id'],
        period: ques['period'],
        subject: ques['subject'],
        source: ques['source'] || 'sys',
        source_id: ques['source_id'] || 'sys'
    }

    for (const b in volume.blocks) {
        const block = volume.blocks[b];
        if (block.type !== type) {
            continue
        }
        // deduplicated
        const questions = block.questions;
        for (const q in questions) {
            if (questions[q]['id'] === ques['id']) {
                return basket;
            }
        }
        ques_['score'] = ques['score'] ? ques['score'] : block['default_score'];
        block.questions.push(ques_);
        return basket;
    }
    // init a new block and push the question
    ques_['score'] = ques['score'] ? ques['score'] : type_t['default_score'];
    const block = {
        title: '',
        note: '',
        type: type,
        default_score: type_t['default_score'],
        questions: [ques_],
    };
    // find the proper postion to insert the block
    const blk_pos = type_t['blk_pos'];
    for (const i in volume.blocks) {
        const type_t_ = TYPES[volume.blocks[i].type] || TYPES['default'];
        const pos = type_t_['blk_pos'];
        if (pos > blk_pos) {
            volume.blocks.splice(parseInt(i), 0, block);
            return basket;
        }
    }
    // not find proper postion, then insert to the last
    volume.blocks.push(block);
    return basket;
}


function traverse_questions(basket, quesMap, hasAnswer = false) {
    const fields = ['id', 'elite', 'subject', 'period', 'description', 'comment', 'blocks', 'knowledges', 'difficulty', 'type', 'score', 'refer_exampapers', 'year', 'ctime', 'utime', 'audio'];
    for (const volume of basket.volumes || []) {
        for (const block of volume.blocks || []) {
            for (const i in block.questions) {
                const question = block.questions[i];
                const q = quesMap[question.id];
                if (!q) {
                    delete block.questions[i];
                    continue;
                }
                // 去除返回的试题电子化答案、解析、解答等信息
                if (!hasAnswer && q.blocks) {
                  q.blocks = {
                    stems: q.blocks.stems,
                    knowledges: q.blocks.knowledges,
                    types: q.blocks.types,
                  }
                }
                block.questions[i] = _.pick(q, fields);
                block.questions[i]['score'] = question['score'];
                block.questions[i]['source'] = question['source'];
                block.questions[i]['source_id'] = question['source_id'];
            }
        }
    }
}

/**
 * 删除试题答案解析解答等字段
 * @param volumes
 */
function deleteQuestionBlocks(volumes) {
    try {
        _.each(volumes,(volume) => {_.each(volume.blocks, block => {_.each(block.questions, question => {
            if (_.isNumber(question.id)) { // 当前只删除系统试题的后续考虑个人上传试题
                question.blocks = {
                    stems: question.blocks.stems,
                    // answers: question.blocks.answers,
                    knowledges: question.blocks.knowledges,
                    core_knowledges: question.blocks.core_knowledges,
                }
            }
        })})})
    } catch (err) {

    }
}


function ques_process(exam) {
    let subject = exam.subject || '';
    let volumes = exam.volumes || [];
    if (subject !== '英语') {
        return exam;
    }
    let ques_num = 1;
    for (let volume of volumes) {
        let blocks = volume.blocks || []
        for (let block of blocks) {
            let ques_type = block.type || '';
            let questions = block.questions || []
            for (let question of questions) {
                let ques_blocks = question.blocks || {};
                let stems = ques_blocks.stems || [];
                let description = question.description || '';
                let ques_number = [];
                if (ques_type === '单选题') {
                    ques_number.push(ques_num);
                    ques_num += 1
                } else if (ques_type === '填空题' || ques_type === '阅读理解') {
                    for (let one_stem of stems) {
                        ques_number.push(ques_num);
                        let regex = /[（\(]\d+?[\)）]/g;
                        let result = one_stem.stem.match(regex)
                        if (result && result.length !== 0) {
                            one_stem.stem = one_stem.stem.replace(result[0], ' ');
                        }
                        ques_num += 1
                    }
                } else if (ques_type === '语法填空' || ques_type === '七选五') {
                    let regex = /[（\(]\d+?[\)）]/g;
                    let result = stems[0].stem.match(regex)
                    if (result) {
                        for (let i = 0; i < result.length; i++) {
                            // ques_number.push(ques_num)
                            question.blocks.stems[0].stem = question.blocks.stems[0].stem.replace(result[i], '(' + ques_num + ')');
                            // ques_num += 1
                        }
                    }
                    for (const ans of question.blocks.answers[0] || []) {
                        ques_number.push(ques_num);
                        ques_num += 1;
                    }
                }else if (ques_type === '完形填空') {
                    // 删除小题号
                    for (let one_stem of stems) {
                        ques_number.push(ques_num)
                        one_stem.stem = ' '
                        ques_num += 1
                    }
                    // 替换试题中的题号
                    let regex = /[（\(]\d+?[\)）]/g;
                    let result = description.match(regex)
                    if (result) {
                        for (let i = 0; i < result.length; i++) {
                            if (result.length === ques_number.length) {
                                question.description = question.description.replace(result[i], '(' + ques_number[i] + ')');
                            } else {
                                question.description = question.description.replace(result[i], '');
                            }
                        }
                    }
                } else {
                    ques_number.push(ques_num);
                    ques_num += 1
                }
                question['ques_number'] = ques_number;
            }
        }
    }
}



