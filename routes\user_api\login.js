const express = require('express');
const router = express.Router();
const login_service = require('../../modules/user_api/v2/user_login_service');

// 用户名密码登录
router.post('', login_service.login);
// 使用令牌凭据登录
router.get('/token', login_service.loginByToken);
// 作业系统登录
router.get('/homework/session', login_service.homeworkLogin);
// 获取用户列表
router.post('/check', login_service.loginCheck);
// 根据学校登录
router.post('/by/school', login_service.loginBySchool);


module.exports = router;
