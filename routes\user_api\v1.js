var express = require('express');
var router = express.Router();
var apikey = require('../../modules/middlewares/apikey.js');

var user = require('../../modules/user_api/v1/user');
var resource = require('../../modules/user_api/v1/resource');
var kboe = require('../../modules/user_api/v1/kboe');
var video = require('../../modules/user_api/v1/video');

// kboe api key middleware
router.use(apikey('KBOE'));

// 用户的提分宝典, 临时屏蔽十一学校
router.get('/aplus/papers/', (req, res, next) => {
    try {
        if ([84, 11542, 27463, 16602,16601,27463,17432,21373,17583,55668,17261,43025,43392,
            16920,
            16676].indexOf(req.user.schoolId) >= 0) {
            return res.status(200).json({
                code: 0,
                msg: 'OK',
                data: {
                    total_num: 0,
                    exampapers: []
                }
            });
        }
        return next();
    } catch (err) {
        next();
    }
}, kboe.aplusPapers);
// 用户的知识点水平以及答题信息
router.get('/knowledges/:knowledge_id/', kboe.knowledge);
router.get('/weak/knowledges/', kboe.weakKnowledges);
router.get('/weak/knowledges/reco/', kboe.recoWeakKnowledges);

// 视频相关操作
router.get('/experiment/videos/reco/', video.recoExpVideos);

// 用户权益信息
router.get('/assets', user.getAssets);
router.get('/vip/right/status', user.getVipRightStatus);

// 用户关于教材与试卷配置信息
router.get('/books/profile', user.getBooksProfile);
router.put('/books/profile', user.putBooksProfile);
router.get('/exampapers/profile', user.getExampapersProfile);
router.put('/exampapers/profile', user.putExampapersProfile);
router.get('/userinfo', user.getUserInfo);
router.put('/userinfo', user.putUserInfo);
router.put('/usertraces', user.updateUserInfoTrace);
router.get('/usertraces', user.getUserInfoTrace);
router.get('/userprofile', user.getUserInfoProfile);
router.put('/userprofile', user.updateHfsInfo);
router.put('/userprofile/curr', user.updateUserCurrInfo);
router.put('/qixin/unbind', user.setQiXinUnbind);
router.put('/qixin/binded', user.setQiXinBinded);


// 用户对资源操作
router.post('/resources/erratum/', resource.postErratum);
router.get('/resources/erratum/', resource.getErratum);
router.post('/resources/favorite/', resource.postFavorite);
router.get('/resources/favorite/', resource.getFavorite);

router.post('/transfers/data', user.addTemporaryKnowItem);
router.get('/transfers/data/:jade', user.getTemporaryKnowItem);
var favorite = require('../../modules/favorite_api/v1/favorite_api');

//收藏试题
router.post('/favorite/questions/', favorite.postQuestion);
//根据对应条件获取收藏试题
router.get('/favorite/questions/', favorite.getQuesInfo);
//获取收藏所有试题的id
router.get('/favorite/question/keys/', favorite.getQuesKeys);
//取消收藏试题
router.delete('/favorite/questions/:question_id', favorite.delFavoriteQues);
//
//收藏试卷
router.post('/favorite/exampapers/', favorite.postExampaperAsync);
//根据对应条件获取收藏试卷
router.get('/favorite/exampapers/', favorite.getFavoriteExampaper);
//获取收藏所有试卷的id
router.get('/exampaper/favorite/keys/', favorite.getExampaperKeys);
//取消收藏试卷
router.delete('/favorite/exampapers/:exampaper_id', favorite.delFavoriteExampaper);

//收藏细目表
router.post('/favorite/tw_specifications/', favorite.postTable);
//获取收藏所有试卷的id
router.get('/favorite/tw_specifications/', favorite.getTables);
//取消收藏试卷
router.delete('/favorite/tw_specifications/:id', favorite.deleteTable);

router.get('/favorite/count', favorite.getFavoriteCount);


// 收藏备课资源
router.post('/favorite/edu_files/', favorite.postFavoriteEduFiles);
//根据对应条件获取收藏试卷
router.get('/favorite/edu_files/', favorite.getFavoriteEduFiles);
//获取收藏所有试卷的id
router.get('/favorite/edu_files/keys/', favorite.getFavoriteEduFileKeys);
//取消收藏试卷
router.delete('/favorite/edu_files/:id', favorite.delFavoriteEduFiles);

// 收藏教学工具
router.post('/favorite/edu_tools/', favorite.postFavoriteEduTools);
//根据对应条件获取收藏
router.get('/favorite/edu_tools/', favorite.getFavoriteEduTools);
//获取收藏所有的id
router.get('/favorite/edu_tools/keys/', favorite.getFavoriteEduToolKeys);
//取消收藏
router.delete('/favorite/edu_tools/:id', favorite.delFavoriteEduTools);


// 收藏示范好课
router.post('/favorite/micro_courses/', favorite.postFavoriteMicroCourses);
//根据对应条件获取收藏示范好课
router.get('/favorite/micro_courses/', favorite.getFavoriteMicroCourses);
//获取收藏所有示范好课的id
router.get('/favorite/micro_courses/keys/', favorite.getFavoriteMicroCourseKeys);
//取消收藏示范好课
router.delete('/favorite/micro_courses/:id', favorite.delFavoriteMicroCourses);

// 用户收藏统计
router.get('/favorite/stat', favorite.stat);
//退出登陆
router.delete('/session', user.logOut);

// 下载记录
router.get('/download/question/list', resource.getQuestionsDownloadRecord);
router.get('/download/exampaper/list', resource.getExamapapersDownloadRecord);
router.get('/download/edu_file/list', resource.getEduFileDownloadRecord);
router.get('/download/edu_tool/list', resource.getEduToolDownloadRecord);
router.get('/download/resource_album/list', resource.getAlbumDownloadRecord);
router.get('/download/record', resource.getResourceRecord);
router.get('/download/stat', resource.getUserDownloadStat);


// 阅卷
router.get('/yj/exam/list', user.getYjExamList);
router.get('/yj/exam/:exam_id/url', user.getYjExamUrl);
router.get('/yj/grade-subject', user.getYjGradeSubject);

// 用户选题过滤信息保存
router.post('/filter', user.addFilterInfo);
router.get('/filter', user.getFilterInfo);

// 获取用户unify_token
router.get('/unify_token', user.getUnifyToken);


module.exports = router;

