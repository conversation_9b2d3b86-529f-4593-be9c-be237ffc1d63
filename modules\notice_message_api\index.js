const Joi = require('@hapi/joi');
const mongodber = require('../utils/mongodber')
const db = mongodber.use('tiku')
const logger = require('../utils/logger');
let ResponseWrapper = require('../middlewares/response_wrapper');
const utils = require('../utils/utils');
const schema = require('../../bin/schema');
const enums = require('../../bin/enum');

module.exports = {
    getList,
    addVipMsg,
}

const JOI_GET_LIST = Joi.object({
    offset: Joi.number().required(),
    limit: Joi.number().required(),
    type: Joi.number().required(),
});

async function getList(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const { offset, limit, type } = await JOI_GET_LIST.validate(req.query);
        const result = {
            total: 0,
            list: []
        }
        const total = await db.collection(schema.notice_message).find({type: type}).count();
        if(!total) return responseWrapper.succ(result);
        result.total = total;
        const list = await db.collection(schema.notice_message).find({type: type}).sort({ctime: -1}).skip(offset).limit(limit).toArray();
        result.list = list.map(e => {
            return {
                id: e._id.toString(),
                content: e.content
            };
        });
        return responseWrapper.succ(result);
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

async function addVipMsg(user_name, spu_name) {
    if (!user_name) return;
    let name = '';
    if (user_name.length === 1) {
        name = user_name;
    } else if (user_name.length === 2) {
        name = user_name[0] + '*';
    } else if (user_name.length >= 3) {
        name += user_name[0];
        name += user_name.slice(1, -1).replace(/./g, '*');
        name += user_name[user_name.length - 1];
    }
    const ds = {
        type: 10,
        content: `用户${name}已成功购买${spu_name}`,
        deleted: enums.BooleanNumber.NO,
        ctime: new Date(),
        utime: new Date()
    }
    await db.collection(schema.notice_message).insertOne(ds);
}
