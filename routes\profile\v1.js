var router = require('express').Router();
var apikey = require('../../modules/middlewares/apikey');
var transmiter = require('../../modules/kb_api/v2/transmiter');
var downloader = require('../../modules/kb_api/v2/downloader');

// kb api key middleware
router.use(apikey('KB'));

router.get('/questions/:question_id/download', downloader.downloadQuestion);
router.get('/exampapers/:exampaper_id/download', downloader.downloadExampaper);
router.post('/assemble/exampaper/download', downloader.assembleExampaper);
router.get('/docx/:filename', downloader.downloadDocx);

router.all('*', transmiter.transmit);

module.exports = router;
