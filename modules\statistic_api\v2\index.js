/*
 * @Descripttion:
 * @Date: 2024-03-05 17:10:35
 */
const config = require('config')
const TIKUSERVER = config.get('TIKU_SERVER')
const mongodber = require('../../utils/mongodber')
const db = mongodber.use('tiku')
const ObjectId = require('mongodb').ObjectId
const ResponseWrapper = require('../../middlewares/response_wrapper');
const utils = require('../../utils/utils');



/**
 * @description: 组合埋点参数
 * @param {*} user cookie用户信息
 * @param {*} query 用户传递参数
 * @return {*}
 */
function concatInfo (user, query) {
  const userInfo = user ? JSON.parse(user) : {}
  const { userId, name, schoolId, schoolName, province: _province, role, isVip, vipType } = userInfo
  return {
    period: null,
    subject: null,
    fingerprint: null,  // 用户指纹
    event_id: 'pv',
    web_route: null,  // 前端路由
    web_from_route: null,  // 上一级路由
    duration: null, // 停留时长
    province: _province || null, // 当前省份
    user_id: userId || null,
    user_name: name || null,  // 用户姓名
    school_id: schoolId || null,  // 学校id
    school_name: schoolName || null,   // 学校名称
    role: role || null,// 角色
    isVip: isVip || null, // 是否是会员
    vipType: vipType || null, // 会员类型
    timestamp: new Date(),
    origin: null,// 来源
    ...query,
  }
}


const postAccessSpots = async (req, res) => {
  const responseWrapper = new ResponseWrapper(req, res)
  const cookie_user_info = req.cookies[TIKUSERVER.userInfo.name]
  const body = req.body
  body.ip = utils.getClientIp(req) || null;
  // 组合cookie用户信息与前端传参
  const info = concatInfo(cookie_user_info, body)
  const payload = await db.collection('access_spot_v2').insertOne(info)
  return responseWrapper.succ({ id: payload.insertedId })
}

const updateStayTime = async (req, res) => {
  const responseWrapper = new ResponseWrapper(req, res)
  const body = req.body
  const objectId = new ObjectId(body.id)
  await db.collection('access_spot_v2').updateOne({ _id: objectId }, { $set: { duration: body.duration } })

  responseWrapper.succ({})
}

module.exports = {
  postAccessSpots,
  updateStayTime
}
