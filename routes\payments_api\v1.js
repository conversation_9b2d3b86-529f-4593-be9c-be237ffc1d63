var express = require('express');
var router = express.Router();

var payments = require('../../modules/payments_api/v1/index');

// 发起支付
router.post('/', payments.postPaymentV2);

// 获取支付状态
router.get('/status', payments.getPaymentStatus);

// 订单退款
router.post('/refund/:id', payments.refundPayment);
// 取消订单
router.delete('/:id', payments.deletePayment);

// 获取未支付订单
router.get('/unpaid',payments.getUnpaidTransaction);

// 手动完成
router.get('/:id/finish', payments.manualFinish);

// 提交支付回调 微信支付 QR
// router.post('/wechat-pay/notification/qr', payments.postNotificationQr);
// 资源包下载发起支付
router.post('/resource/h5', payments.postPaymentResourceH5);
// 手动完成订单
router.get('/:id/h5/finish', payments.manualFinishH5);
// 资源包下载发起支付 - APP
router.post('/resource/app', payments.postPaymentResourceApp);

module.exports = router;
