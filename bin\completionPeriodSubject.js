const _ = require('underscore');
const mongodb = require('mongodb');
const Thenjs = require('thenjs');
const program = require('commander');

program
	.version('0.1.0')
	.option('-k, --kb [kb]', 'kb db')
	.option('-t, --tiku [tiku]', 'tiku db')
	.parse(process.argv);

var kbURL = program.kb;
var tikuURL = program.tiku;

var kb = null,
	tiku = null;

mongodb.connect(kbURL, function(err, _kb){
	mongodb.connect(tikuURL, function(err, _tiku){
		kb = _kb;
		tiku = _tiku;
		tiku.collection('resource_erratum').find({period:{$exists: false}}, {id: 1, type: 1}).
		toArray(function(err, docs){
			var group = _.groupBy(docs, function(doc){
				return doc.type;
			});
			Thenjs.each(['question', 'knowledge', 'exampaper'], function(cont, value){
				var ids = _.map(group[value], function(x){
					return Number(x.id);
				});
				kb.collection(value).find({_id: {$in: ids}}, {period: 1, subject: 1}).toArray(function(err, vs){
					Thenjs.each(vs, function(done, v){
						_.each(vs, function(v){
							tiku.collection('resource_erratum').updateMany({
								id: String(v._id)
							}, {
								$set: {
									"period": v.period, 
									"subject": v.subject
								}
							}, {
								w: 1
							}, function(err, writeResult){
								done(null, null);
							});
						});
					}).then(function(done, result){
						cont(null, null);
					});
				});
			}).then(function(cont, result){
				kb.close();
				tiku.close();
				cont(null, null);	
			});
		});
	});
});
