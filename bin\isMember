#!/usr/bin/env node

var program = require('commander');
var chalk = require('chalk');
var request = require('request');
var URL = require('url');

program
	.usage(' <options> [code string]')
	.option('-e, --env [mode]', 'set NODE_ENV production or development');

program.on('--help', function () {
	console.log();
})

function help () {
	program.parse(process.argv);
	if (program.args.length < 1) 
		return program.help();
}
help();

process.env.NODE_ENV = program.env === 'production' ? 'production' : 'development';
var config = require('config');

var userId = program.args[0];
var HFS_VIP_SERV = config.get('HFS_VIP_SERVER');
var url = URL.format({
	protocol: HFS_VIP_SERV.protocol,		
	pathname: '/v1/students/'+userId+'/member-status',		
	hostname: HFS_VIP_SERV.hostname,		
	port: HFS_VIP_SERV.port
});

console.log('request to', url);

request({
	url: url		
}, function(err, response, body){
	try{
		body = JSON.parse(body);
		if(body.data.isMember){
			console.log(chalk.green('该学生为VIP用户'));
		}else{
			console.log(chalk.red('该学生不是VIP用户'));
		}
	}catch(err){
		console.error(chalk.red('接口发生了意外'));
	}
});
