'use strict';
const moment = require('moment');
const mongodber = require('../../utils/mongodber');
const enums = require('../../../bin/enum');
const db = mongodber.use('tiku');
const _ = require('lodash');
const schema_name = 'user_log';


module.exports = {
    register,
    login,
    openVip,
    assembleExampaper,
    download,
}
// 注册
async function register(user) {
    const type = enums.UserLogType.u_register;
    const content = '';
    await _save_log_async(user, type, content);
    // await _sync_school_vip(user);
}

async function _sync_school_vip(user) {
    const school_id = user.sch_id;
    if (!school_id) return;
    const school = await db.collection('school_info').findOne({_id: school_id});
    if (_.isEmpty(school)
        || !school.vip_type
        || moment().isAfter(school.expired_time)
        || school.vip_type === enums.MemberType.TIKU_CITY
        || school.vip_type === enums.MemberType.TIKU_PROFESSION
        || school.vip_type === enums.MemberType.TIKU_COUNTY
    ) return;
    const db_user = await db.collection('user').findOne({_id: user.id || user._id});
    if (_.isEmpty(db_user)) return;
    if (school.vip_type === db_user.vip_type && school.start_time === db_user.start_time && school.expired_time === db_user.expired_time) {
        return; // 相同权益
    }
    const ds = {
        is_vip: moment().isBetween(school.start_time, school.expired_time),
        start_time: school.start_time,
        expired_time: school.expired_time,
        vip_rights: []
    }

    for (const key of Object.keys(enums.VipRightType)) {
        if (enums.VipRightType[key] === enums.VipRightType.vip_num) continue; // 只在学校生效
        const obj = {};
        obj[enums.VipRightType[key]] = school[enums.VipRightType[key]] || 0;
        ds.vip_rights.push(obj);
    }
    await db.collection('user').updateOne({_id: db_user._id}, {$set: ds});
}
// 登录
async function login(user) {
    const type = enums.UserLogType.u_login;
    const content = '';
    await _save_log_async(user, type, content);
}
// 开通
async function openVip(user_id, order, etime) {
    const user = await db.collection('user').findOne({_id: user_id});
    const type = enums.UserLogType.u_open_vip;
    const content = {
        order_id: order._id.toString(), // 订单ID(订单ID不存在时，系统赠送)
        order_no: order.no, // 订单ID(订单ID不存在时，系统赠送)
        vip_name: order.goods.name, // 会员名称
        vip_stime: moment(etime).subtract(order.goods.month, 'month').toDate().getTime(), // 会员开始时间
        vip_etime: moment(etime).toDate().getTime(), // 会员结束时间
    }
    await _save_log_async(user, type, content);

}
// 组卷
async function assembleExampaper(user, paper_id) {
    const type = enums.UserLogType.u_assemble_exampaper;
    const content = {
        id: paper_id,
    }
    await _save_log_async(user, type, content);
}
// 下载
async function download(user, resource_type, resource_id) {
    const type = enums.UserLogType.u_download;
    const content = {
        id: resource_id,
        type: resource_type
    }
    await _save_log_async(user, type, content);
}

async function _save_log_async(user, type, content) {
    const now = new Date();
    const data = {
        user_id: user.id || user._id,
        type: type,
        content: content || '',
        time: now,
        ctime: now,
        utime: now
    }
    if (user) {
        data.operator = {
            id: user.id || user._id,
            name: user.name
        }
    }
    await db.collection(schema_name).insertOne(data);
}
