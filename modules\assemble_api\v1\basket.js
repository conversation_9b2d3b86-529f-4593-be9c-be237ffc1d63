/*!
 * Author: guochanghui
 * Date: 2017-04-24
 */

/**
 * Module dependencies.
 * @private
 */
var _ = require('underscore');
var config = require('config');
var ObjectID = require("mongodb").ObjectID;
var request = require('request');
const KBSERVER = config.get('KB_API_SERVER');
const URL = require('url');
var mongodber = require('../../utils/mongodber');
var rediser = require('../../utils/rediser');
var ResponseWrapper = require('../../middlewares/response_wrapper');
var logger = require('../../utils/logger');
const deleteQuestionBlocks = require('./utils').deleteQuestionBlocks;
var template = require('./template');
var extend_ques = require('./extend_ques');
const enums = require('../../../bin/enum');

var db = mongodber.use('tiku');
const loadsh = require('lodash');
const qs = require('qs');
/**
 * Module variables.
 * @private
 */
var DURATION = 120 * 60;
var collection = db.collection('basket');
var TYPES = template.TYPES;
var DIGIT_MAP_CHINESE = template.DIGIT_MAP_CHINESE;

/**
 * Module functions.
 * @private
 */
function gen_rediser_key(user_id) {
    return 'assemble_api:v1:' + user_id + ':basket';
}

function init_basket(user_id) {
    let basket = {
        _id: user_id,     // 用户唯一标记, app + user_id

        period: '',       // 学段
        subject: '',      // 学科

        name: null,         // 试卷名称
        subtitle: null,     // 副标题
        score: null,        // 试卷总分
        duration: null,     // 考试时间，单位分钟
        paper_info: null,   // 试卷信息栏，
        cand_info: null,
        attentions: null,   // 注意事项
        secret_tag: null,   // 保密标记文字
        gutter: null,       // 装订线
        template: 'standard',   // 组卷类型，stardard, exam, homework

        volumes: [{
            title: '卷I（选择题）',  // 卷I分卷名
            //note: '请点击修改第I卷的文字说明', // 分卷说明
            note: '', // 分卷说明
            blocks: [],
        }, {
            title: '卷II（非选择题）', // 卷II分卷名
            //note: '请点击修改第II卷的文字说明', // 分卷说明
            note: '', // 分卷说明
            blocks: [],
        }]
    }
    return basket;
}

function check_basket(basket) {
    var _basket = init_basket();
    for (var i in _basket) {
        if (!(i in basket)) {
            throw new Error('basket缺少' + i + '字段');
        }
    }
    // check volumes
    var volumes = basket.volumes;
    if (volumes.length != 2) {
        throw new Error('volumes长度不为2');
    }
    for (var i in volumes) {
        var vol = volumes[i];
        if (!(vol instanceof Object && !Array.isArray(vol))) {
            throw new Error('volume ' + i + '：不合法');
        }
        if (!vol.hasOwnProperty('blocks')
            || !vol.hasOwnProperty('title')
            || !vol.hasOwnProperty('note')) {
            throw new Error('volume ' + i + '：不合法');
        }
    }
}

function handleBasket(basket) {
    basket.score = 0;
    let volumes = basket.volumes;
    for (let v in volumes) {
        let volume = volumes[v];
        let blocks = volume.blocks;
        for (let b in blocks) {
            let block = blocks[b];
            block.default_score = Number(block.default_score);
            let questions = block.questions;
            if (questions.length > 0) {
                let ts = 0;
                for (var i = 0; i < questions.length; i++) {
                    questions[i].score = Number(questions[i].score);
                    ts += Number(questions[i].score);
                }
                basket.score += Number(ts);
            }
        }
    }

    return basket;
}

function render_basket(basket) {
    // 试题篮分卷信息
    basket.score = 0;
    var block_num = 0;
    var volumes = basket.volumes;
    for (var v in volumes) {
        var volume = volumes[v];
        var blocks = volume.blocks;
        for (var b in blocks) {
            var block = blocks[b];
            var questions = block.questions;
            var n = questions.length;
            if (questions.length <= 0) {
                continue;
            }
            var s = Number(questions[0].score);
            var ts = Number(s);
            var tag = true;
            for (var i = 1; i < n; ++i) {
                if (questions[i].score != questions[i - 1].score) {
                    tag = false;
                }
                ts += Number(questions[i].score);
            }
            var order = `${DIGIT_MAP_CHINESE[++block_num]}`;
            var ss = tag ? `，每题${s}分` : '';
            block.default_score = tag ? s : block.default_score;
            var detail = `本大题共计${n}小题${ss}，共计${ts}分`;
            var note = (block.note.length > 0) ? `，${block.note}` : '';
            //block.title = `${order}、${block.type}（${detail}${note}）`;
            block.title = `${block.type}（${detail}${note}）`;
            basket.score += Number(ts);
        }
    }
    // 试卷名称， 2017年5月3日
    // var t = new Date();
    // var info = `${t.getFullYear()}年${t.getMonth() + 1}月${t.getDate()}日${basket.period}${basket.subject}`;
    basket.name = basket.name || genExamPaperName(basket);
    // 副标题
    basket.subtitle = basket.subtitle || '';
    // 考试时间，单位分钟
    basket.duration = basket.duration || 120;
    // 试卷信息栏，考试总分：100分；考试时间：100分钟
    var info = `考试总分：${basket.score}   考试时间：${basket.duration}`;
    basket.paper_info = basket.paper_info || info;
    // 候选人信息栏
    var line = '__________ ';
    var info = `学校：${line}班级：${line}姓名：${line}考号：${line}`;

    basket.cand_info = basket.cand_info || info;
    // 注意事项
    basket.attentions = basket.attentions || '注意事项：<br>1．答题前填写好自己的姓名、班级、考号等信息; <br>2．请将答案正确填写在答题卡上;<br>';
    // 保密标记文字
    basket.secret_tag = basket.secret_tag || '绝密★启用前';
    // 装订线
    basket.gutter = basket.gutter || 0;
    return basket;
}

function genExamPaperName(basket) {
    // const nowDate = new Date('2023/7/1');
    // let academicYearShort = "";//短格式学年度
    // let academicYearLong = "";//长格式学年度
    // let semester = "";//学期
    // const month = nowDate.getMonth();
    // const year = nowDate.getFullYear();
    //
    //
    // if (month >= 8) {
    //     academicYearShort = year.toString().substr(2, 2) + "-";
    //     academicYearShort += (year + 1).toString().substr(2, 2);
    //     academicYearLong = year.toString() + "-";
    //     academicYearLong += (year + 1).toString();
    //     semester = "1";
    // } else {
    //     if (month >= 2)//根据当前时间获取学年和学期,注意跨年份
    //     {
    //         academicYearShort = (year - 1).toString().substr(2, 2) + "-";
    //         academicYearShort += year.toString().substr(2, 2);
    //         academicYearLong = (year - 1).toString() + "-";
    //         academicYearLong += year.toString();
    //         semester = "2";
    //     } else {
    //         academicYearShort = (year - 1).toString().substr(2, 2) + "-";
    //         academicYearShort += year.toString().substr(2, 2);
    //         academicYearLong = (year - 1).toString() + "-";
    //         academicYearLong += year.toString();
    //         semester = "1";
    //     }
    // }
    // let info = `${academicYearLong}学年${basket.period}（${semester === '1' ? '上' : '下'}）${basket.subject}${basket.type || ''}试卷`;
    const nowDate = new Date();
    const year = nowDate.getFullYear();

    // 学年
    const academicYearStartDate = new Date();
    academicYearStartDate.setMonth(0, 1);
    academicYearStartDate.setHours(0, 0, 0, 0);

    const academicYearEndDate = new Date();
    academicYearEndDate.setMonth(7, 15);
    academicYearEndDate.setHours(0, 0, 0, 0);
    let academicYearLong = `${year.toString()}-${(year + 1).toString()}`;
    if (nowDate.getTime() >= academicYearStartDate.getTime() && nowDate.getTime() < academicYearEndDate.getTime()) {
        academicYearLong = `${(year - 1).toString()}-${year.toString()}`;
    }
    // 学期
    const semesterStartDate = new Date();
    semesterStartDate.setMonth(1, 15);
    semesterStartDate.setHours(0, 0, 0, 0);

    const semesterEndDate = new Date();
    semesterEndDate.setMonth(7, 15);
    semesterEndDate.setHours(0, 0, 0, 0);

    let semester = "上";//学期
    if (nowDate.getTime() >= semesterStartDate.getTime() && nowDate.getTime() < semesterEndDate.getTime()) {
        semester = '下';
    }
    const formatDateStr = `${nowDate.getFullYear()}年${nowDate.getMonth()+1}月${nowDate.getDate()}日`;
    let info = `${academicYearLong}学年${basket.period}${basket.subject} (${semester}) ${basket.type || ''}试卷(${formatDateStr})`;
    return info;
}

function profile_basket(basket) {
    let profile = [];
    if (!basket) {
        return profile;
    }
    let volumes = basket.volumes;
    if (!volumes) {
        return profile;
    }
    for (let volume of volumes) {
        let blocks = volume['blocks'] || [];
        for (let block of blocks) {
            if (!block) {
                continue;
            }
            let type = block['type'];
            let questions = block['questions'];
            profile.push({
                type: type,
                questions: questions.map(function (x) {
                    return {
                        id: x['id'],
                        period: x['period'],
                        subject: x['subject'],
                    };
                }),
            });
        }
    }
    return profile;
}

/*
 * save basket to database
 */
function save_basket(basket, callback) {
    try {
        check_basket(basket);
    } catch (err) {
        return callback(err, null);
    }
    var user_id = basket._id;
    var filter = { _id: basket._id };
    var options = { upsert: true };
    collection.replaceOne(filter, basket, options, function (err, result) {
        if (err) {
            return callback(err, null);
        }
        var rediser_key = gen_rediser_key(user_id);
        rediser.set(rediser_key, basket, DURATION);
        return callback(null, result);
    });
}

/*
 * find basket from rediser or database
 */
function find_basket(user_id, callback) {
    var rediser_key = gen_rediser_key(user_id);
    rediser.get(rediser_key, function (err, basket) {
        if (err) {
            logger.error(err);
        }
        if (null !== basket) {
            return callback(null, basket);
        }
        collection.findOne({ _id: user_id }, function (err, basket) {
            if (err) {
                return callback(err, null);
            }
            if (!basket) {
                basket = init_basket(user_id);
            }
            rediser.set(rediser_key, basket, DURATION);
            return callback(null, basket);
        });
    });
}

function filterBasket(basket) {
    for (var vx in basket.volumes) {
        var volume = basket.volumes[vx];
        for (var bx in volume.blocks) {
            volume.blocks[bx].questions = _.filter(volume.blocks[bx].questions, function (x) {
                return x && Object.keys(x).length > 0;
            });
        }
    }
    return basket;
}

/*
 * module functions
 * @public
 */
function get_basket(req, res) {
    var resWrapper = new ResponseWrapper(req, res);
    var user_id = req.user.id;
    var template = req.query.template;
    if (template && (template != 'standard' && template != 'exam'
        && template != 'homework')) {
        return resWrapper.error('HANDLE_ERROR', err.message);
    }
    // load a exampaper_id into basket
    var exampaper_id = req.query.exampaper_id;
    var op = req.query.op;
    let source_type = req.query.source_type; // 试卷来源
    if (!source_type) source_type = enums.PaperSourceType.SYS;
    find_basket(user_id, function (err, basket) {
        if (err) {
            logger.error(err);
            return resWrapper.error('HANDLE_ERROR', err.message);
        }
        let basketQuestionNum = get_questions_num(basket);
        if (!exampaper_id) {
            basket = render_basket(basket);
            if (template) {
                basket.template = template; // set exampaper type
            }
            extend_ques.extend_ques(basket, function (err) {
                if (err) {
                    logger.error(err);
                    return resWrapper.error('HANDLE_ERROR', err.message);
                }
                delete basket['_id'];
                if (!basket.partsList) {
                    basket.partsList = ["name", "subtitle", "paper_info", "gutter", "attentions", "volumes", "blocks", "secret_tag"];
                }
                if (res.freq !== '' && res.freq !== undefined) {
                    deleteQuestionBlocks(basket.volumes);
                    return resWrapper.send({
                        code: 6,
                        msg: res.freq,
                        data: basket
                    });
                }
                return resWrapper.succ(filterBasket(basket));
            });
        } else {
            if (source_type === enums.PaperSourceType.SYS) {
                // 系统试卷库
                const getExamUrl = URL.format({
                    protocol: KBSERVER.protocol,
                    hostname: KBSERVER.hostname,
                    port: KBSERVER.port,
                    pathname: '/kb_api/v2/exampapers/' + exampaper_id,
                    search: qs.stringify({
                        api_key: req.apiKey
                    })
                });
                request(getExamUrl, function (err, response, body) {

                    if (err) {
                        logger.error(err.message);
                        return resWrapper.error('HANDLE_ERROR', err.message);
                    }

                    if (response.statusCode !== 200) {
                        logger.error('GET: ' + getExamUrl);
                        const errMsg = err && err.message || '查询试卷不存在';
                        return resWrapper.error('HANDLE_ERROR', errMsg);
                    }
                    let exam = JSON.parse(body);
                    traverse_tiku_exampaper(exam, basket); // 转换
                    basket = merge_basket_question(op, basket, exam); // 合并
                    // basket['exampaper_id'] = exam['id'];
                    basket['_id'] = user_id;
                    handleBasket(basket);
                    save_basket(basket, function (err, result) {
                        if (err) {
                            logger.error(err);
                            return resWrapper.error('HANDLE_ERROR', err.message);
                        }
                        extend_ques.extend_ques(basket, function (err) {
                            if (err) {
                                logger.error(err);
                                return resWrapper.error('HANDLE_ERROR', err.message);
                            }
                            delete exam['_id'];
                            if (res.freq !== '' && res.freq !== undefined) {
                                deleteQuestionBlocks(basket.volumes);
                                return resWrapper.send({
                                    code: 6,
                                    msg: res.freq,
                                    data: filterBasket(basket)
                                });
                            }
                            return resWrapper.succ(filterBasket(basket));
                        });
                    });
                });
            } else if (source_type === enums.PaperSourceType.ASSEMBLE) {
                // 个人组卷
                // get exampaper
                var query = {
                    _id: new ObjectID(exampaper_id)
                    // user_id: user_id, 共享的时候删除这个
                };
                var options = {};
                var coll = db.collection('exampaper');
                coll.findOne(query, options, function (err, exam) {
                    if (err) {
                        logger.error(err);
                        return resWrapper.error('HANDLE_ERROR', err.message);
                    }
                    if (!exam) {
                        return resWrapper.error('HANDLE_ERROR');
                    }
                    let examQuestionNum = get_questions_num(exam);
                    if (examQuestionNum + basketQuestionNum > 60) {
                        return resWrapper.error('HANDLE_ERROR', '试题数量超出60道，不能进行组卷！');
                    }
                    basket = merge_basket_question(op, basket, exam);
                    basket = render_basket(basket);
                    basket.template = exam.template;
                    // basket['exampaper_id'] = exam['_id'] && exam['_id'].toString();
                    basket['_id'] = user_id;
                    if (!basket.partsList) {
                        basket.partsList = ["name", "subtitle", "paper_info", "gutter", "attentions", "volumes", "blocks", "secret_tag"];
                    }
                    handleBasket(basket);
                    save_basket(basket, function (err, result) {
                        if (err) {
                            logger.error(err);
                            return resWrapper.error('HANDLE_ERROR', err.message);
                        }
                        extend_ques.extend_ques(basket, function (err) {
                            if (err) {
                                logger.error(err);
                                return resWrapper.error('HANDLE_ERROR', err.message);
                            }
                            delete basket['_id'];
                            if (res.freq !== '' && res.freq !== undefined) {
                                deleteQuestionBlocks(basket.volumes);
                                return resWrapper.send({
                                    code: 6,
                                    msg: res.freq,
                                    data: filterBasket(basket)
                                });
                            }
                            return resWrapper.succ(filterBasket(basket));
                        });
                    });
                });
            } else if (source_type === enums.PaperSourceType.UPLOAD) {
                // 用户上传
                const query = {
                    _id: new ObjectID(exampaper_id)
                    // user_id: user_id, 共享的时候删除这个
                };
                const options = {};
                const coll = db.collection('upload_exampaper');
                coll.findOne(query, options, (err, exam) => {
                    if (err) {
                        logger.error(err);
                        return resWrapper.error('HANDLE_ERROR', err.message);
                    }
                    if (!exam) {
                        return resWrapper.error('HANDLE_ERROR');
                    }
                    const ids = [];
                    for (const volume of exam.volumes) {
                        for (const b of volume.blocks) {
                            for (const q of b.questions) {
                                ids.push(new ObjectID(q.id));
                            }
                        }
                    }
                    if (ids.length + basketQuestionNum > 60) {
                        return resWrapper.error('HANDLE_ERROR', '试题数量超出60道，不能进行组卷！');
                    }
                    db.collection('upload_questions').find({ _id: { $in: ids } }).toArray((err, exam_questions) => {
                        if (err) {
                            logger.error(err);
                            return resWrapper.error('HANDLE_ERROR', err.message);
                        }
                        if (!loadsh.size(exam_questions)) {
                            return resWrapper.error('HANDLE_ERROR');
                        }
                        // 题目信息增加到考试里面
                        for (const volume of exam.volumes) {
                            for (const b of volume.blocks) {
                                for (const q of b.questions) {
                                    const detail_question = exam_questions.find(e => e._id.toString() === q.id);
                                    assgin_question(q, detail_question);
                                }
                            }
                        }
                        basket = merge_basket_question(op, basket, exam);
                        basket = render_basket(basket);
                        // basket['exampaper_id'] = exam['_id'] && exam['_id'].toString();
                        basket['_id'] = user_id;
                        basket.template = 'standard';
                        if (!basket.partsList) {
                            basket.partsList = ["name", "subtitle", "paper_info", "gutter", "attentions", "volumes", "blocks", "secret_tag"];
                        }
                        handleBasket(basket);
                        save_basket(basket, function (err, result) {
                            if (err) {
                                logger.error(err);
                                return resWrapper.error('HANDLE_ERROR', err.message);
                            }
                            extend_ques.extend_ques(basket, function (err) {
                                if (err) {
                                    logger.error(err);
                                    return resWrapper.error('HANDLE_ERROR', err.message);
                                }
                                delete basket['_id'];
                                if (res.freq !== '' && res.freq !== undefined) {
                                    deleteQuestionBlocks(basket.volumes);
                                    return resWrapper.send({
                                        code: 6,
                                        msg: res.freq,
                                        data: filterBasket(basket)
                                    });
                                }
                                return resWrapper.succ(filterBasket(basket));
                            });
                        });
                    })

                });
            } else {
                return resWrapper.error('HANDLE_ERROR', '错误类型');
            }

        }
    });
}

function get_questions_num(basket) {
    let questionNum = 0;
    if (basket.volumes) {
        for (let a = 0; a < basket.volumes.length; a++) {
            let volume = basket.volumes[a];
            let blocks = volume.blocks;
            if (!blocks) {
                continue;
            }
            for (let b = 0; b < blocks.length; b++) {
                let block = blocks[b];
                if (!block) {
                    continue;
                }
                let questions = block.questions;
                if (!questions) {
                    continue;
                }
                for (let c = 0; c < questions.length; c++) {
                    questionNum++;
                }
            }
        }
    }
    return questionNum;
}

function merge_basket_question(op, basket, exam) {
    if (exam) {
        basket.name = exam.name + '（副本）';
        if (exam.partsList) {
            basket.partsList = exam.partsList;
        }
    }
    if (op === enums.BasketOption.MERGE) {
        const volumes = exam.volumes;
        for (const v in volumes) {
            const blocks = volumes[v]['blocks'];
            for (const b in blocks) {
                const block = blocks[b];
                const questions = block['questions'];
                for (const ques of questions) {
                    _insert_questions(basket, ques);
                }
            }
        }
        return basket;
    } else if (op === enums.BasketOption.DELETE) {
        basket.volumes = exam.volumes;
        basket.period = exam['period'] || '';
        basket.subject = exam['subject'] || '';
        return basket;
    }
}

/**
 * KB 试卷转换为题库格式
 * @param exam
 */
function traverse_tiku_exampaper(exam) {
    if (loadsh.isEmpty(exam)) return;
    if (loadsh.size(exam['volumes'])) return;
    exam.volumes = [{
        title: '卷I（选择题）',  // 卷I分卷名
        //note: '请点击修改第I卷的文字说明', // 分卷说明
        note: '', // 分卷说明
        blocks: [],
    }, {
        title: '卷II（非选择题）', // 卷II分卷名
        //note: '请点击修改第II卷的文字说明', // 分卷说明
        note: '', // 分卷说明
        blocks: [],
    }];
    for (const b of exam.blocks) {
        for (const ques of b.questions) {
            _insert_questions(exam, ques);
        }
    }
}

function assgin_question(question, upload_question) {
    if (loadsh.isEmpty(question) || loadsh.isEmpty(upload_question)) return;
    question['source_type'] = enums.BasketQuestionSourceType.UPLOAD;
    const obj = loadsh.pick(upload_question, ['id', 'description', 'comment', 'blocks', 'knowledges', 'difficulty', 'type']);
    loadsh.assign(question, obj);
}

function put_basket(req, res) {
    var resWrapper = new ResponseWrapper(req, res);
    try {
        var user = req.user;
        var basket = req.body;
        basket['_id'] = user.id;
        basket['province'] = user.province;
        basket['city'] = user.city;
        basket['sch_id'] = user.schoolId;
        basket['sch_name'] = user.schoolName;
    } catch (err) {
        return resWrapper.error('PARAMETERS_ERROR', err.message);
    }

    // check basket
    save_basket(basket, function (err, result) {
        if (err) {
            logger.error(err);
            return resWrapper.error('HANDLE_ERROR', err.message);
        }
        delete basket['_id'];
        return resWrapper.succ(filterBasket(basket));
    });
}

function delete_basket(req, res) {
    var resWrapper = new ResponseWrapper(req, res);
    var user_id = req.user.id;

    basket = init_basket(user_id);
    save_basket(basket, function (err, result) {
        if (err) {
            logger.error(err);
            return resWrapper.error('HANDLE_ERROR', err.message);
        }
        delete basket['_id'];
        return resWrapper.succ(filterBasket(basket));
    });
}

function _insert_questions(basket, ques) {
    var type = ques['type'];
    var type_t = TYPES[type] || TYPES['default'];

    var vol_pos = type_t['vol_pos'];
    var volume = basket.volumes[vol_pos];

    basket.period = ques['period'] || '';
    basket.subject = ques['subject'] || '';
    if (ques['exampaper_type'] && ques['exampaper_type'] !== enums.ExamPaperOtherType) {
        basket.type = ques['exampaper_type'];
    }

    var ques_ = {
        id: ques['id'],
        period: ques['period'],
        subject: ques['subject'],
    }

    for (var b in volume.blocks) {
        var block = volume.blocks[b];
        if (block.type !== type) {
            continue
        }
        // deduplicated
        var questions = block.questions;
        for (var q in questions) {
            if (questions[q]['id'] == ques['id']) {
                return basket;
            }
        }
        ques_['score'] = ques['score'] ? ques['score'] : block['default_score'];
        block.questions.push(ques_);
        return basket;
    }
    // init a new block and push the question
    ques_['score'] = ques['score'] ? ques['score'] : type_t['default_score'];
    var block = {
        title: '',
        note: '',
        type: type,
        default_score: type_t['default_score'],
        questions: [ques_],
    };
    // find the proper postion to insert the block
    var blk_pos = type_t['blk_pos'];
    for (var i in volume.blocks) {
        var type_t_ = TYPES[volume.blocks[i].type] || TYPES['default'];
        var pos = type_t_['blk_pos'];
        if (pos > blk_pos) {
            volume.blocks.splice(parseInt(i), 0, block);
            return basket;
        }
    }
    // not find proper postion, then insert to the last
    volume.blocks.push(block);
    return basket;
}

function _delete_questions(basket, qids) {
    var volumes = basket.volumes;
    for (var v in volumes) {
        var volume = volumes[v];
        var blocks = volume.blocks;
        if (!blocks) {
            continue;
        }
        for (var b in blocks) {
            var block = blocks[b];
            var questions = block.questions;
            if (!questions) {
                continue;
            }
            for (var q in questions) {
                var ques = questions[q];
                for (var i in qids) {
                    if (ques.id == qids[i]) {
                        questions[q] = undefined;
                    }
                }
            }
            block.questions = questions.filter(function (x) {
                return (x !== undefined);
            });
            // if block not has question, delete
            if (0 === block.questions.length) {
                blocks[b] = undefined;
            }
        }
        volume.blocks = blocks.filter(function (x) {
            return (x !== undefined);
        });
    }
    return basket;
}

/*
 * DESC:
 *
 * URI:
 *    /assemble_api/v1/basket/questions/
 * Method:
 *    GET
 */
function get_questions(req, res) {
    var resWrapper = new ResponseWrapper(req, res);
    var user_id = req.user.id;

    find_basket(user_id, function (err, basket) {
        if (err) {
            logger.error(err);
            return resWrapper.error('HANDLE_ERROR', err.message);
        }
        var profile = profile_basket(basket);
        return resWrapper.succ(profile);
    });
}

/*
 * DESC:
 *
 * URI:
 *    /assemble_api/v1/basket/questions/
 * Method:
 *    POST
 */
function post_questions(req, res) {
    var resWrapper = new ResponseWrapper(req, res);
    var user_id = req.user.id;
    var questions = [];
    if (req.body instanceof Array) {
        questions = req.body;
    } else {
        questions = [req.body];
    }

    find_basket(user_id, function (err, basket) {
        if (err) {
            logger.error(err);
            return resWrapper.error('HANDLE_ERROR', err.message);
        }
        try {
            let questionNr = _.flatten(_.pluck(_.flatten(_.pluck([basket], 'volumes')), 'blocks'), 'questions').length;
            let limitQuestionNum = 40; // 默认40个题
            db.collection('school_info').findOne({ _id: req.user.schoolId }, function (err, schoolInfo) {
                if (err) {
                    logger.error(err);
                    return resWrapper.error('HANDLE_ERROR', err.message);
                }
                if (schoolInfo && schoolInfo.member_id) {
                    db.collection('member').findOne({ _id: ObjectID(schoolInfo.member_id) }, function (err, memberInfo) {
                        if (err) {
                            logger.error(err);
                            return resWrapper.error('HANDLE_ERROR', err.message);
                        }

                        if (memberInfo && memberInfo.volume_exampaper_num && typeof memberInfo.volume_exampaper_num.question === 'number' && memberInfo.volume_exampaper_num.question > 0) {
                            limitQuestionNum = memberInfo.volume_exampaper_num.question;
                        }

                        if (questionNr >= limitQuestionNum) {
                            return resWrapper.error('PARAMETERS_ERROR', '您的账号已达数量上限');
                        }
                        for (let i in questions) {
                            let question = questions[i];
                            if (!question) {
                                continue;
                            }
                            if (!question.id ||
                                !question.type ||
                                !question.period ||
                                !question.subject) {
                                continue;
                            }
                            basket = _insert_questions(basket, questions[i]);
                        }
                        save_basket(basket, function (err, result) {
                            if (err) {
                                logger.error(err);
                                return resWrapper.error('HANDLE_ERROR', err.message);
                            }
                            let pro = profile_basket(basket);
                            return resWrapper.succ(pro);
                        });
                    });
                } else {
                    if (questionNr >= limitQuestionNum) {
                        return resWrapper.error('PARAMETERS_ERROR', '您的账号已达数量上限');
                    }
                    for (let i in questions) {
                        let question = questions[i];
                        if (!question) {
                            continue;
                        }
                        if (!question.id ||
                            !question.type ||
                            !question.period ||
                            !question.subject) {
                            continue;
                        }
                        basket = _insert_questions(basket, questions[i]);
                    }
                    save_basket(basket, function (err, result) {
                        if (err) {
                            logger.error(err);
                            return resWrapper.error('HANDLE_ERROR', err.message);
                        }
                        let pro = profile_basket(basket);
                        return resWrapper.succ(pro);
                    });
                }
            });
        } catch (err) {
            if (!err.code) {
                err.code = 'HANDLE_ERROR';
            }
            return resWrapper.error(err.code, err.message);
        }
    });
}

function delete_questions(req, res) {
    var resWrapper = new ResponseWrapper(req, res);
    var user_id = req.user.id;

    var qids = req.params.qids.split(',')
        .map(function (x) { return x.length === 24 ? x : parseInt(x) });
    find_basket(user_id, function (err, basket) {
        if (err) {
            logger.error(err);
            return resWrapper.error('HANDLE_ERROR', err.message);
        }
        var basket = _delete_questions(basket, qids);
        save_basket(basket, function (err, result) {
            if (err) {
                logger.error(err);
                return resWrapper.error('HANDLE_ERROR', err.message);
            }
            var pro = profile_basket(basket);
            return resWrapper.succ(pro);
        });
    });
}

module.exports = {
    get_questions: get_questions,
    post_questions: post_questions,
    delete_questions: delete_questions,
    get_basket: get_basket,
    put_basket: put_basket,
    delete_basket: delete_basket,
    _delete_questions: _delete_questions,
    _insert_questions: _insert_questions,
}
