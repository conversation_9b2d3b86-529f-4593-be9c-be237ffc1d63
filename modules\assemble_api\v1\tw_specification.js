const path = require('path');
const request = require('request');
const config = require('config');
const Thenjs = require('thenjs');
const _ = require('underscore');
const qs = require('qs');
const URL = require('url');
const rimraf = require('rimraf');
const ObjectID = require("mongodb").ObjectID;
const util = require('util');
const mongodber = require('../../utils/mongodber');
const Logger = require('../../utils/logger');
const ResponseWrapper = require('../../middlewares/response_wrapper');
const rediser = require('../../utils/rediser');
const excel = require('../../../lib/excel');
const KBSERVER = config.get('KB_API_SERVER');
const TIKUSERVER = config.get('TIKU_SERVER');
const db = mongodber.use('tiku');
const db_open = mongodber.use('tiku_open');
const client = require('zipkin-middleware').client;
const yxClient = require('../../client');
const enums = require('../../../bin/enum');
const user_right_service = require('../../user_api/v2/user_right_service');
const paper_utils = require('../../utils/paper_utils');
const utils = require('../../utils/utils');
const basket_service = require('../../assemble_api/v2/basket.service');
/*
 * Table is alias two-way-specification
 */

function _getTableByRefId(relevanceId, apiKey, viewOnly, done) {

	var getUrl = URL.format({
		protocol: KBSERVER.protocol,
		hostname: KBSERVER.hostname,
		port: KBSERVER.port,
		pathname: '/kb_api/v2/tw_specifications',
		search: qs.stringify({
			api_key: apiKey,
			relevance_id: relevanceId,
			view_only: viewOnly === true ? 'true' : 'false'
		})
	});

	request({
		url: getUrl
	}, function (err, response, body) {

		if (err) {
			Logger.error(err.message);
			return done('HANDLE_ERROR', null);
		}

		try {
			var _body = JSON.parse(body);

			if (_body.code === 5) {
				return done('NULL_ERROR', null);
			}

			if (_body.code) {
				return done('HANDLE_ERROR', null);
			}

			return done(null, _body);
		} catch (err) {
			return done('HANDLE_ERROR', null);
		}
	});
}

/**
 * @private
 */
function _getTableById(tableId, apiKey, viewOnly, done) {

	let getUrl = URL.format({
		protocol: KBSERVER.protocol,
		hostname: KBSERVER.hostname,
		port: KBSERVER.port,
		pathname: `/kb_api/v2/tw_specifications/${tableId}/info`,
		search: qs.stringify({
			api_key: apiKey,
			view_only: viewOnly === true ? 'true' : 'false'
		})
	});

	request({
		url: getUrl
	}, function (err, response, body) {

		if (err) {
			Logger.error(err.message);
			return done('HANDLE_ERROR', null);
		}

		if (response.statusCode !== 200) {
			Logger.error('GET: ' + getUrl);
			return done('HANDLE_ERROR', null);
		}

		let _body = JSON.parse(body);
		for (let i = 0; i < _body.blocks.length; i++) {
			let block = _body.blocks[i];
			for (let j = 0; j < block.questions.length; j++) {
				let question = block.questions[j];
				if ((question.score && question.score === 0) || !question.score)
					question.score = 5;
			}
		}

		return done(null, _body);
	});
}

function _createTable(postBody, userId, apiKey, done) {

	var createTableUrl = URL.format({
		protocol: KBSERVER.protocol,
		hostname: KBSERVER.hostname,
		port: KBSERVER.port,
		pathname: '/kb_api/v2/tw_specifications',
		search: qs.stringify({
			api_key: apiKey
		})
	});

	request.post({
		url: createTableUrl,
		headers: {
			'content-type': 'application/json'
		},
		body: JSON.stringify(postBody)
	}, function (err, response, body) {
		if (err) {
			Logger.error(err.message);
			return done('HANDLE_ERROR', null);
		}

		if (response.statusCode !== 200) {
			Logger.error('POST: ' + createTableUrl);
			let retObj = JSON.parse(body);
			if (retObj.code && retObj.msg) {
				return done(retObj.msg);
			} else {
				return done('HANDLE_ERROR', null);
			}
		}

		var _body = JSON.parse(body);
		var newId = _body.id;

		if (!userId)
			return done(null, _body);

		db_open.collection(enums.OpenSchema.tw_specification).insert({
			user_id: utils.formatUserId(userId),
			table_id: newId,
			ctime: new Date(),
			period: postBody.period,
			subject: postBody.subject,
			type: postBody.type,
			grade: postBody.grade,
			province: postBody.province
		}, function (err, writeResult) {
			if (err) {
				Logger.error(err.message);
				return done('HANDLE_ERROR');
			}

			if (writeResult.result.ok > 0) {
				return done(null, _body);
			} else {
				Logger.error('本地写入失败');
				return done('HANDLE_ERROR');
			}
		});
	});
}

function _getExampaperById(id, apiKey, done) {

	var getExamUrl = URL.format({
		protocol: KBSERVER.protocol,
		hostname: KBSERVER.hostname,
		port: KBSERVER.port,
		pathname: '/kb_api/v2/exampapers/' + id,
		search: qs.stringify({
			api_key: apiKey
		})
	});

	return request(getExamUrl, function (err, response, body) {

		if (err) {
			Logger.error(err.message);
			return done('HANDLE_ERROR', null);
		}

		if (response.statusCode !== 200) {
			Logger.error('GET: ' + getExamUrl);
			return done('HANDLE_ERROR', null);
		}

		var _body = JSON.parse(body);
		return done(null, _body);
	});
}

function _loadKnowledge(exampaper, apiKey, done) {

	var getQues = URL.format({
		protocol: KBSERVER.protocol,
		hostname: KBSERVER.hostname,
		port: KBSERVER.port,
		pathname: '/kb_api/v2/questions/',
		search: qs.stringify({
			api_key: apiKey
		})
	});

	var questionIds = _.pluck(_.flatten(_.pluck(exampaper.blocks, 'questions')), 'id');

	return request.post({
		url: getQues,
		headers: {
			'content-type': 'application/json'
		},
		body: JSON.stringify({
			question_ids: questionIds
		})
	}, function (err, response, body) {

		if (err) {
			Logger.error(err.message);
			return done('HANDLE_ERROR', null);
		}

		if (response.statusCode !== 200) {
			Logger.error('POST: ' + getQues);
			return done('HANDLE_ERROR', null);
		}

		var questions = JSON.parse(body);
		var objQues = _.object(_.pluck(questions, 'id'), questions);
		_.each(_.flatten(_.pluck(exampaper.blocks, 'questions')), function (question) {
			var knowledges = objQues[question.id].knowledges;
			question.knowledges = _.map(knowledges, function (knowledge) {
				return {
					id: knowledge.id,
					name: knowledge.name
				}
			});
		});
		return done(null, exampaper);
	});
}

function mapDiff(difficult) {
	var diff = Math.floor(difficult / 2);
	switch (diff) {
		case 5:
			return '困难';
		case 4:
			return '困难';
		case 3:
			return '较难';
		case 2:
			return '中等';
		case 1:
			return '较易';
		default:
			return '容易';
	}
}

function _exampaper2table(exampaper) {
	var retobj = {};
	const {to_year} = utils.getAcademicYear();
	retobj.name = exampaper.name;
	retobj.period = exampaper.period;
	retobj.subject = exampaper.subject;
	retobj.province = exampaper.province;
	retobj.type = exampaper.type;
	retobj.grade = exampaper.grade;
	retobj.year = exampaper.to_year || to_year;
	retobj.blocks = [];
	_.each(exampaper.blocks, function (block) {
		try {
			var b = {
				type: block.questions[0].type,
				name: block.title
			};

			b.questions = _.map(block.questions, function (question) {
				return {
					type: question.type,
					period: question.period,
					subject: question.subject,
					difficulty: mapDiff(question.difficulty),
					knowledges: question.knowledges,
					score: Math.floor(block.score / block.questions.length)
				};
			});
			retobj.blocks.push(b);
		} catch (err) {
			// retobj.blocks.push({});
		}
	});

	return retobj;
}

function getTableFromExampaper(req, res) {

	var responseWrapper = new ResponseWrapper(req, res);
	var exampaperId = Number(req.params.exampaper_id);

	return _getTableByRefId(exampaperId, req.apiKey, true, function (err, body) {

		if (err === 'NULL_ERROR') {
			return _getExampaperById(exampaperId, req.apiKey, function (err, exampaper) {

				if (err) {
					return responseWrapper.error(err);
				}

				return _loadKnowledge(exampaper, req.apiKey, function (err, exampaper) {
					if (err) {
						return responseWrapper.error(err);
					}

					var table = _exampaper2table(exampaper);
					table.relevance_type = 'exampaper';
					table.relevance_id = exampaperId;
					table.permission = 'public';

					return _createTable(table, null, req.apiKey, function (err, body) {

						if (err) {
							return responseWrapper.error(err);
						}

						var id = body.id;

						return _getTableById(id, req.apiKey, false, function (err, body) {
							if (err) {
								return responseWrapper.error(err);
							}
							return responseWrapper.succ(body);
						});
					});
				});
			});
		}

		if (err) {
			Logger.error('通过ref获取双向细目表失败');
			return responseWrapper.error('HANDLE_ERROR');
		}

		return responseWrapper.succ(body);
	})
}

function cloneTable(req, res) {
	var from = req.query.from;
	var user = req.user;
	var responseWrapper = new ResponseWrapper(req, res);

	return _getTableById(from, req.apiKey, false, function (err, table) {
		if (err) {
			return responseWrapper.error(err);
		}
		_createTable(table, user.id, req.apiKey, function (err, body) {
			if (err) {
				return responseWrapper.error(err);
			}
			return responseWrapper.succ(body);
		});
	});
}

function createFreshTable(req, res) {
	var user = req.user;
	var responseWrapper = new ResponseWrapper(req, res);

	var postBody = req.body;
	postBody.permission = 'private';
	postBody.relevance_type = null;
	postBody.relevance_id = null;

	return _createTable(postBody, user.id, req.apiKey, function (err, body) {
		if (err) {
			return responseWrapper.error(err);
		}

		return responseWrapper.succ(body);
	});
}

/**
 * Description: 创建一张双向细目标
 * Method: POST
 * Wiki: http://wiki.iyunxiao.com/pages/viewpage.action?pageId=25399323#id-组卷API-创建一张双向细目标
 * URL: /assemble_api/v1/tw_specifications
 * Author: cuiyunfeng
 * Date: 2017-8-31
 * @public
 */
function createTable(req, res) {

	var query = req.query;
	if (query.from) {
		return cloneTable(req, res);
	} else {
		return createFreshTable(req, res);
	}
}

const getTableListNew = async (req, res) => {
	let responseWrapper = new ResponseWrapper(req, res);
	let user = req.user;
	let offset = Number(req.query.offset);
	let limit = req.query.limit ? Number(req.query.limit) : 0;
	offset = isNaN(offset) || offset < 0 ? 0 : offset;

	let cond = {
		user_id: user.id
	};
	let sortType = '';
	if (req.query.sort)
		sortType = req.query.sort;
	if (req.query.period)
		cond.period = req.query.period;
	if (req.query.subject)
		cond.subject = req.query.subject;
	let type = '';
	if (req.query.type) {
		type = req.query.type;
		cond.type = req.query.type;
	}

	try {
		//收藏表
		let favorites = await db.collection('favorite').find({ user_id: user.id }).toArray();
		let tableIds = [];
		_.each(favorites, function (ele, idx) {
			if (ele.tw_specifications) {
				let arr = ele.tw_specifications;
				_.each(arr, function (eleSpec, index) {
					tableIds.push(eleSpec.id.toString());
				});
			}
		});

		//双向细目表
		cond.user_id = utils.formatUserId(user.id);
		let docs = await db_open.collection(enums.OpenSchema.tw_specification).find(cond).sort({ ctime: -1 }).toArray();
		_.each(docs, function (doc) {
			tableIds.push(doc.table_id.toString());
		});
		if (tableIds.length === 0)
			return responseWrapper.succ({});
		//数组去重
		tableIds = Array.from(new Set(tableIds));
		let strIds = tableIds.join(',');
		strIds = encodeURIComponent(strIds);

		//kb_api双向细目表
		let listUrl = URL.format({
			protocol: KBSERVER.protocol,
			hostname: KBSERVER.hostname,
			port: KBSERVER.port,
			pathname: '/kb_api/v2/tw_specifications/' + strIds + '/list',
			search: qs.stringify({
				api_key: req.apiKey,
				access: 'mine'
			})
		});
		let _body = await client.axios.get(listUrl);
		_body = _.sortBy(_body.data, function (x) {
			return tableIds.indexOf(x.id);
		});

		//构建返回信息
		let resArr = [];
		for (let ele of _body) {
			let obj = {};
			obj.id = ele.id;
			obj.name = ele.name;
			obj.period = ele.period ? ele.period : '';
			obj.type = ele.type ? ele.type : '';
			obj.subject = ele.subject ? ele.subject : '';
			obj.ctime = ele.ctime ? ele.ctime : '';
			obj.view_times = ele.view_times ? ele.view_times : 0;
			obj.download_times = ele.download_times ? ele.download_times : 0;
			obj.province = ele.province ? ele.province : '';
			obj.grade = ele.grade ? ele.grade : '';
			obj.relevance_type = ele.relevance_type ? ele.relevance_type : '';
			obj.relevance_id = ele.relevance_id ? ele.relevance_id : '';
			if (ele.year)
				obj.year = Number(ele.year);
			else {
				if (ele.utime) {
					let utimes = ele.utime.split('-');
					obj.year = Number(utimes[0]);
				} else if (ele.ctime) {
					let ctimes = ele.ctime.split('-');
					obj.year = Number(ctimes[0]);
				} else {
					obj.year = '';
				}
			}

			if (req.query.period && req.query.period !== obj.period)
				continue;
			if (req.query.subject && req.query.subject !== obj.subject)
				continue;
			if (type === '')
				resArr.push(obj);
			else {
				if (type === obj.type)
					resArr.push(obj);
			}
		}

		// 按时间排序
		if (sortType === 'time') {
			resArr.sort(function (x, y) {
				return Number(y.year) - Number(x.year);
			});
		}
		//按使用次数排序
		if (sortType === 'use_times') {
			resArr.sort(function (x, y) {
				return y.view_times - x.view_times;
			});
		}

		let count = resArr.length;
		if (limit > 0)
			resArr = resArr.slice(offset, offset + limit);

		return responseWrapper.succ({
			total_num: count,
			specifications: resArr
		});
	} catch (error) {
		return responseWrapper.error('HANDLE_ERROR', '获取双向细目表列表失败')
	}
}

/**
 * Description: 修改某双向细目表
 * Wiki: http://wiki.iyunxiao.com/pages/viewpage.action?pageId=25399323#id-组卷API-修改某双向细目表
 * Method: PUT
 * URL: /assemble_api/v1/tw_specification/{table_id}/
 * Author: cuiyunfeng
 * Date: 2017-9-1
 * @public
 */
async function updateTable(req, res) {

	var user = req.user;
	var tableId = req.params.table_id;
	var responseWrapper = new ResponseWrapper(req, res);

	// var updateUrl = URL.format({
	// 	protocol: KBSERVER.protocol,
	// 	hostname: KBSERVER.hostname,
	// 	port: KBSERVER.port,
	// 	pathname: '/kb_api/v2/tw_specifications/' + tableId,
	// 	search: qs.stringify({
	// 		api_key: req.apiKey
	// 	})
	// });

	var postBody = req.body;
	delete postBody.permission;
	delete postBody.relevance_type;
	delete postBody.relevance_id;
	const table = await db_open.collection(enums.OpenSchema.tw_specification).findOne({
		user_id: utils.formatUserId(user.id),
		table_id: tableId
	});
	if (_.isEmpty(table)) {
		return responseWrapper.error('HANDLE_ERROR', '细目表不存在');
	}
	await yxClient.kb.updateTable(tableId, postBody);
	return responseWrapper.succ({});
	// db.collection('tw_specification').findOne({
	// 	user_id: user.id,
	// 	table_id: ObjectID(tableId)
	// }, function (err, docUser) {
	//
	// 	if (err) {
	// 		Logger.error(err.message);
	// 		return responseWrapper.error('HANDLE_ERROR', err.message);
	// 	}
	//
	// 	if (!docUser) {
	// 		Logger.error('该用户没有这条记录');
	// 		return responseWrapper.error('NULL_ERROR', '您无权操作');
	// 	}
	//
	// 	request.put({
	// 		url: updateUrl,
	// 		headers: {
	// 			'content-type': 'application/json'
	// 		},
	// 		body: JSON.stringify(postBody)
	// 	}, function (err, response, body) {
	// 		if (err) {
	// 			Logger.error(err.message);
	// 			return responseWrapper.error('HANDLE_ERROR', err.message);
	// 		}
	//
	// 		if (response.statusCode !== 200) {
	// 			Logger.error('PUT: ' + updateUrl);
	// 			return responseWrapper.error('HANDLE_ERROR', '修改双向细目表失败');
	// 		}
	// 		return responseWrapper.succ({});
	// 	});
	// });
}

/**
 * Description: 选择双向细目表组卷
 * Wiki: http://wiki.iyunxiao.com/pages/viewpage.action?pageId=25399323#id-组卷API-选择双向细目表组卷
 * Method: GET
 * URL: /assemble_api/v1/tw_specification/{tw_specification_id}/exampaper
 * Author: cuiyunfeng
 * Date: 2017-8-29
 * @public
 */
function getExampaperByTable(req, res) {

}


/**
 * Description: 从试卷导出双向细目表
 * Wiki: http://wiki.iyunxiao.com/pages/viewpage.action?pageId=25399323#id-组卷API-从试卷导出双向细目表
 * Method: GET
 * URL: /assemble_api/v1/exampapers/{exampaper_id}/tw_specification/
 * Author: cuiyunfeng
 * Date: 2017-8-29
 * @public
 */
function getExampaperTable(req, res) {

}

/**
 * Description: 从试卷平行组卷
 * Wiki: http://wiki.iyunxiao.com/pages/viewpage.action?pageId=25399323#id-组卷API-从试卷平行组卷
 * Method: POST
 * URL: /assemble_api/v1/exampapers/{exampaper_id}/exampaper/
 * Author: cuiyunfeng
 * Date: 2017-8-29
 * @public
 */
function createExampaperFromExampaper(req, res) {

}

/**
 * Description: 获取热门双向细目表
 * Wiki: http://wiki.iyunxiao.com/pages/viewpage.action?pageId=25399323#id-组卷API-获取热门双向细目表
 * Method: GET
 * URL: /assemble_api/v1/tw_specification/hot
 * Author: cuiyunfeng
 * Date: 2017-8-31
 * @public
 */
function getHotTable(req, res) {

	var responseWrapper = new ResponseWrapper(req, res);

	req.query.api_key = req.apiKey;

	var hotUrl = URL.format({
		protocol: KBSERVER.protocol,
		hostname: KBSERVER.hostname,
		port: KBSERVER.port,
		pathname: '/kb_api/v2/tw_specifications/hot',
		search: qs.stringify(req.query)
	});

	request(hotUrl, function (err, response, body) {
		if (err) {
			Logger.error(err.message);
			return responseWrapper.error('HANDLE_ERROR', err.message);
		}

		if (response.statusCode !== 200) {
			try {
				var _body = JSON.parse(body);
				if (_body.code === 5)
					return responseWrapper.error('NULL_ERROR', '没有匹配的双向细目表');
			} catch (err) {
				Logger.error('GET: ' + hotUrl);
				return responseWrapper.error('HANDLE_ERROR', '获取双向细目表列表失败');
			}
		}

		var _body = JSON.parse(body);
		return responseWrapper.succ(_body);
	});
}

function getTableInfo(req, res) {

	var tableId = req.params.table_id;
	var responseWrapper = new ResponseWrapper(req, res);

	return _getTableById(tableId, req.apiKey, true, function (err, body) {
		if (err) {
			return responseWrapper.error(err);
		}
		return responseWrapper.succ(body);
	});
}

function _getQuestionByCond(cont, req, cond) {

	var apiKey = req.apiKey;

	var knowledgeId = cond.id;

	var queryObj = {
		api_key: apiKey,
		offset: 0,
		limit: 1000,
		type: cond.type
	};

	if (cond.difficulty !== '不限')
		queryObj.difficulty = cond.difficulty;

	if (cond.associate_kid)
		queryObj.associate_kid = cond.associate_kid;

	var quesUrl = URL.format({
		protocol: KBSERVER.protocol,
		hostname: KBSERVER.hostname,
		port: KBSERVER.port,
		pathname: '/kb_api/v2/knowledges/' + knowledgeId + '/questions/',
		search: qs.stringify(queryObj)
	});

	request(quesUrl, function (err, response, body) {
		if (err) {
			Logger.error(err.message);
			return cont(null, null);
		}

		if (response.statusCode !== 200) {
			Logger.error('GET: ' + quesUrl);
			return cont(null, null);
		}

		var _body = JSON.parse(body);
		var question = _.sample(_body.questions);
		question = {
			id: question.id,
			type: question.type,
			period: question.period,
			subject: question.subject,
			press_vertion: question.press_version,
			grade: question.grade
		}

		return cont(null, question);
	});
}

function _returnBucket(data) {
	var retdata = {};
	_.each(data, function (dt) {
		var type = dt.type;
		if (retdata[type] == undefined) {
			retdata[type] = {
				type: type,
				questions: []
			}
		}
		retdata[type].questions.push({
			id: dt.id,
			period: dt.period,
			subject: dt.subject
		});
	});

	return _.values(retdata);
}

function _filter_table_empty_knowledges(table) {
	for (const block of table.blocks || []) {
		block.questions = (block.questions || []).filter(e => _.isArray(e.knowledges) && _.size(e.knowledges));
	}
	table.blocks = (table.blocks || []).filter(e => _.isArray(e.questions) && _.size(e.questions));
}
function _handler_question_diff(table) {
	const diffArray = ['容易', '较易', '中等', '较难', '困难'];
	for (const block of table.blocks || []) {
		for (const q of block.questions || []) {
			const randomIndex = Math.floor(Math.random() * diffArray.length);
			// 不限难度的暂时随机
			if (q.difficulty === '不限') q.difficulty = diffArray[randomIndex];
		}
	}
}

function _createExampaperByTable(table, req, done) {
	_filter_table_empty_knowledges(table);
	var apiKey = req.apiKey;
	Thenjs(function (cont) {

		var deleteUrl = URL.format({
			protocol: TIKUSERVER.protocol,
			hostname: TIKUSERVER.hostname,
			port: TIKUSERVER.port,
			pathname: '/assemble_api/v2/basket/'
		});

		request.delete({
			url: deleteUrl,
			body: JSON.stringify({
				period: table.period,
				subject: table.subject
			}),
			headers: {
				'content-type': 'application/json',
				cookie: req.headers.cookie
			}
		}, function (err, response, body) {
			cont(null)
		});
	}).then(function (cont) {

		var postUrl = URL.format({
			protocol: KBSERVER.protocol,
			hostname: KBSERVER.hostname,
			port: KBSERVER.port,
			pathname: '/kb_api/v2/tw_specifications/exampapers',
			search: qs.stringify({
				api_key: apiKey
			})
		});

		request.post({
			url: postUrl,
			headers: {
				'content-type': 'application/json'
			},
			body: JSON.stringify(table)
		}, function (error, response, body) {
			if (error) {
				Logger.error(error.message);
				return done('HANDLE_ERROR', null);
			}

			if (response.statusCode !== 200) {
				return done('HANDLE_ERROR', null);
			}

			body = JSON.parse(body);

			var result = _.filter(body, function (x) {
				return x !== null;
			});

			const params = {
				period: table.period,
				subject: table.subject,
				questions: result.map(e => {
					return {
						id: e.id,
						type: e.type,
						period: table.period,
						subject: table.subject,
					};
				})
			}

			var postBasketUrl = URL.format({
				protocol: TIKUSERVER.protocol,
				hostname: TIKUSERVER.hostname,
				port: TIKUSERVER.port,
				pathname: '/assemble_api/v2/basket/questions/'
			});

			request.post({
				url: postBasketUrl,
				headers: {
					'content-type': 'application/json',
					'cookie': req.headers.cookie
				},
				body: JSON.stringify(params)
			}, function (err, response, body) {
				return;
			});

			return done(null, result);
		});

	});
}

function __createExampaperByTable(table, req, done) {

	var apiKey = req.apiKey;

	var questions = _.flatten(_.pluck(table.blocks, 'questions'));
	Thenjs(function (cont) {

		var deleteUrl = URL.format({
			protocol: TIKUSERVER.protocol,
			hostname: TIKUSERVER.hostname,
			port: TIKUSERVER.port,
			pathname: '/assemble_api/v1/basket/'
		});

		request.delete({
			url: deleteUrl,
			headers: {
				cookie: req.headers.cookie
			}
		}, function (err, response, body) {
			cont(null)
		});
	}).each(questions, function (cont, value) {
		var cond = {
			id: value.knowledges[0].id,
			period: value.period,
			subject: value.subject,
			type: value.type
		}

		if (value.knowledges.length > 1)
			cond.associate_kid = value.knowledges[1].id;

		_getQuestionByCond(cont, req, cond);
	}).then(function (cont, result) {

		result = _.filter(result, function (x) {
			return x !== null;
		});

		var postBasketUrl = URL.format({
			protocol: TIKUSERVER.protocol,
			hostname: TIKUSERVER.hostname,
			port: TIKUSERVER.port,
			pathname: '/assemble_api/v1/basket/questions/'
		});

		request.post({
			url: postBasketUrl,
			headers: {
				'content-type': 'application/json',
				'cookie': req.headers.cookie
			},
			body: JSON.stringify(result)
		}, function (err, response, body) {
			return;
		});
		return done(null, result);
	}).fail(function (cont, error) {
		return done('NULL_ERROR', null);
	});
}

function createExampaper(req, res) {
	var responseWrapper = new ResponseWrapper(req, res);

	if (JSON.stringify(req.body) === '{}') {
		if (!req.query.from)
			return responseWrapper.error('PARAMENT_ERROR');

		return _getTableById(req.query.from, req.apiKey, false, function (err, body) {
			if (err) {
				return responseWrapper.error(err);
			}

			return _createExampaperByTable(body, req, function (err, data) {

				if (err) {
					return responseWrapper.error(err);
				}
				if (data == null) {
					return responseWrapper.error('NULL_ERROR');
				}
				var retdata = _returnBucket(data);
				return responseWrapper.succ(retdata);
			});
		});
	}

	return _createExampaperByTable(req.body, req, function (err, data) {

		if (err) {
			return responseWrapper.error(err);
		}
		if (data == null) {
			return responseWrapper.error('NULL_ERROR');
		}
		var retdata = _returnBucket(data);
		return responseWrapper.succ(retdata);
	});
}

async function createExampaperAsync(req, res) {
	const responseWrapper = new ResponseWrapper(req, res);
	try {
		const user = req.user;
		let params = {};
		const body = req.body;
		if (JSON.stringify(req.body) === '{}') {
			if (!req.query.from) return responseWrapper.error('PARAMENT_ERROR');
			const kb_table = await yxClient.kb.getTableById(req.query.from);
			params = {
				period: kb_table.period,
				subject: kb_table.subject,
				type: '细目表组卷',
				school_id: user && user.school_id,
				user_id: user && user.id.toString() || '',
				blocks: kb_table.blocks,
				filtered_ques: []
			}
		} else {
			params = {
				period: body.period,
				subject: body.subject,
				type: '细目表组卷',
				school_id: user && user.school_id,
				user_id: user && user.id.toString() || '',
				blocks: body.blocks,
				filtered_ques: []
			}
		}
		_filter_table_empty_knowledges(params); // 过滤空的知识点
		_handler_question_diff(params);
		let kb_exampeper = null;
		if (body.exampaper_id) {
			kb_exampeper = await yxClient.kb.getExampaper(body.exampaper_id);
			if (!_.isEmpty(kb_exampeper)) return responseWrapper.error('PARAMENT_ERROR', '试卷不存在');
		}

		if (kb_exampeper) {
			params.paper_id = body.exampaper_id.toString();
			params.grade = kb_exampeper['grade'];
			for (const b of kb_exampeper['blocks']) {
				params.filtered_ques.push(...(b.questions || []).map(e => e.id));
			}
		}
		if (!params.grade) params.grade = user && user.grade || '';
		// 算法组卷
		const algo_paper = await yxClient.algo.detailTablePaper(params);
		if (_.isEmpty(algo_paper)) {
			return responseWrapper.error('HANDLE_ERROR', '组卷失败');
		}
		// 清空试题篮
		const del_res = await basket_service.delete_basket(req.user.id, params.period, params.subject);
		if (del_res.error) return responseWrapper.error(del_res.type, del_res.desc);
		// 添加试题
		const question_ids = [];
		for (const b of algo_paper.blocks) {
			question_ids.push(...b.questions.map(q => q.id));
		}
		const kb_questions = await yxClient.kb.getQuestions(question_ids);
		const questions = [];
		for (const id of question_ids) {
			const q = kb_questions.find(e=> e.id === id);
			if (!q) continue;
			questions.push({
				id: q.id,
				type: q.type,
				period: params.period,
				subject: params.subject,
				source: enums.QuestionSource.SYS,
				source_id: q.id
			})
		}
		const add_res = await basket_service.post_questions(req.user.id, params.period, params.subject, questions);
		if (add_res.error) return responseWrapper.error(add_res.type, add_res.desc);
		// 返回结果
		const retdata = _returnBucket(questions);
		return responseWrapper.succ(retdata);
	} catch (e) {
		Logger.error(e);
		return responseWrapper.error('HANDLE_ERROR', e.message);
	}
}

async function fakeExampaperAsync(req, res) {
	const responseWrapper = new ResponseWrapper(req, res);
	try {
		const exampaperId = req.params.exampaper_id;
		const exampaper = await yxClient.kb.getExampaper(exampaperId);
		if (_.isEmpty(exampaper)) {
			return responseWrapper.error('NULL_ERROR', '试卷不存在');
		}
		const fun_params = {
			type: enums.NewRightType.parallel_paper_fun,
			period: exampaper.period,
			subject: exampaper.subject
		}
		const fun_status = await user_right_service.get_fun_right_status(req.user.id, fun_params);
		if (!fun_status)  return responseWrapper.error('NEED_VIP_ERROR', '需要开通会员后使用');
		// await util.promisify(fakeExampaper)(req, res);
		// 算法组卷
		const algo_paper = await genPaperByAlgo(req.user, exampaper);
		if (_.isEmpty(algo_paper)) {
			return responseWrapper.error('HANDLE_ERROR', '组卷失败');
		}
		// 清空试题篮
		const del_res = await basket_service.delete_basket(req.user.id, exampaper.period, exampaper.subject);
		if (del_res.error) return responseWrapper.error(del_res.type, del_res.desc);
		// 添加试题
		const question_ids = [];
		for (const b of algo_paper.blocks) {
			question_ids.push(...b.questions.map(q => q.id));
		}
		const kb_questions = await yxClient.kb.getQuestions(question_ids);
		const questions = [];
		for (const id of question_ids) {
			const q = kb_questions.find(e=> e.id === id);
			if (!q) continue;
			questions.push({
				id: q.id,
				type: q.type,
				period: exampaper.period,
				subject: exampaper.subject
			})
		}
		const add_res = await basket_service.post_questions(req.user.id, exampaper.period, exampaper.subject, questions);
		if (add_res.error) return responseWrapper.error(add_res.type, add_res.desc);
		// 返回结果
		const retdata = _returnBucket(questions);
		return responseWrapper.succ(retdata);
	} catch (e) {
		Logger.error(e);
		return responseWrapper.error('HANDLE_ERROR', e.message);
	}
}

async function genPaperByAlgo(user, kb_exampeper) {
	const params = {
		period: kb_exampeper.period,
		subject: kb_exampeper.subject,
		type: '细目表组卷',
		school_id: user.schoolId,
		user_id: user.id.toString(),
		paper_id: kb_exampeper.id.toString(),
		grade: kb_exampeper.grade,
		blocks: [],
		filtered_ques: []
	}
	for (const b of kb_exampeper.blocks) {
		const block = {
			type: b.type,
			questions: []
		};
		for (const q of b.questions || []) {
			params.filtered_ques.push(q.id);
			if (!_.size(q.knowledges)) continue;
			const knowledges = q.knowledges.map(k => {
				return {
					id: k.id,
					name: k.name
				}
			});
			const question = {
				type: q.type,
				score: q.score,
				difficulty: enums.QuestionDifficultyName[q.difficulty],
				knowledges: knowledges.slice(0, 3)
			}
			block.questions.push(question);
		}
		if (!_.size(block.questions)) continue;
		params.blocks.push(block);
	}
	return await yxClient.algo.detailTablePaper(params);
}

function fakeExampaper(req, res) {

	var responseWrapper = new ResponseWrapper(req, res);
	var exampaperId = req.params.exampaper_id;

	return _getTableByRefId(exampaperId, req.apiKey, false, function (err, body) {

		if (err === 'NULL_ERROR') {
			return _getExampaperById(exampaperId, req.apiKey, function (err, exampaper) {

				if (err) {
					return responseWrapper.error(err);
				}

				return _loadKnowledge(exampaper, req.apiKey, function (err, exampaper) {
					if (err) {
						return responseWrapper.error(err);
					}

					var table = _exampaper2table(exampaper);
					table.relevance_type = 'exampaper';
					table.relevance_id = exampaperId;
					table.permission = 'public';

					return _createTable(table, null, req.apiKey, function (err, body) {

						if (err) {
							return responseWrapper.error(err);
						}

						var id = body.id;

						return _getTableById(id, req.apiKey, false, function (err, body) {
							if (err) {
								return responseWrapper.error(err);
							}

							return _createExampaperByTable(body, req, function (err, data) {

								if (err) {
									return responseWrapper.error(err);
								}

								if (data == null) {
									return responseWrapper.error('NULL_ERROR');
								}
								var retdata = _returnBucket(data);
								return responseWrapper.succ(retdata);
							});
						});
					});
				});
			});
		}

		if (err) {
			Logger.error('通过ref获取双向细目表失败');
			return responseWrapper.error('HANDLE_ERROR');
		}

		return _createExampaperByTable(body, req, function (err, data) {
			if (err) {
				return responseWrapper.error(err);
			}
			if (data == null) {
				return responseWrapper.error('NULL_ERROR');
			}
			var retdata = _returnBucket(data);
			return responseWrapper.succ(retdata);
		});
	})
}

async function deleteTableAsync(req, res) {
	const tableId = req.params.table_id;
	const userId = req.user.id;
	const open_user_id = utils.formatUserId(userId);
	// 删除用
	const writeResult = await db.collection(schema.tw_specification).deleteOne({table_id: tableId, open_user_id});
	if (writeResult.result.ok) {
		await client.kb.deleteTableById(tableId);
	}
	return {id: tableId};
}

async function deleteTable(req, res) {

	var responseWrapper = new ResponseWrapper(req, res);
	var tableId = req.params.table_id;
	var userId = req.user.id;
	var deleteUrl = URL.format({
		protocol: KBSERVER.protocol,
		hostname: KBSERVER.hostname,
		port: KBSERVER.port,
		pathname: '/kb_api/v2/tw_specifications/' + tableId,
		search: qs.stringify({
			api_key: req.apiKey
		})
	});
	await db_open.collection(enums.OpenSchema.tw_specification).deleteOne({table_id: tableId, user_id: userId});
	await yxClient.kb.deleteTableById(tableId);
	return responseWrapper.succ({
		status: 'ok'
	});
	// db.collection('tw_specification').remove({
	// 	table_id: ObjectID(tableId),
	// 	user_id: userId
	// }, function (err, writeResult) {
	// 	if (err) {
	// 		Logger.error(err.message);
	// 		return responseWrapper.error('HANDLE_ERROR', err.message);
	// 	}
	//
	// 	if (writeResult.result.ok > 0) {
	// 		request.delete(deleteUrl);
	// 		return responseWrapper.succ({
	// 			status: 'ok'
	// 		});
	// 	} else {
	// 		return responseWrapper.succ({
	// 			status: 'bad'
	// 		});
	// 	}
	// });
}

function downloadTimesInc(tableId, api_key) {

	var downloadTimesUrl = URL.format({
		protocol: KBSERVER.protocol,
		hostname: KBSERVER.hostname,
		port: KBSERVER.port,
		pathname: '/kb_api/v2/tw_specifications/' + tableId + '/download-times',
		search: qs.stringify({
			api_key: api_key
		})
	});

	request.post({ url: downloadTimesUrl }, function (error, response, body) { });
}

/*
 *
 */
function downloadTable(req, res) {
	var responseWrapper = new ResponseWrapper(req, res);
	var tableId = req.query.specification_id;
	var exampaperId = Number(req.query.exampaper_id);

	/*
	 * @private
	 */
	function generateExcel(err, body) {

		if (tableId) {
			downloadTimesInc(tableId, req.apiKey);
		} else if (body.id) {
			downloadTimesInc(body.id, req.apiKey);
		}

		if (err) {
			return responseWrapper.error(err);
		}

		return excel.getExcelFromTable(body, 'data/xlsx', function (err) {

			if (err) {
				return responseWrapper.error(err);
			}

			return responseWrapper.succ({
				url: TIKUSERVER.webUrl + '/download/xlsx/' + body.name + '.xlsx'
			});
		});
	}

	if (req.method.toUpperCase() === 'POST') {
		var body = req.body;
		return generateExcel(null, body);
	}

	if (tableId) {
		return _getTableById(tableId, req.apiKey, false, generateExcel);
	} else if (exampaperId) {
		return _getTableByRefId(exampaperId, req.apiKey, false, function (err, body) {

			if (err === 'NULL_ERROR') {
				return _getExampaperById(exampaperId, req.apiKey, function (err, exampaper) {

					if (err) {
						return responseWrapper.error(err);
					}

					return _loadKnowledge(exampaper, req.apiKey, function (err, exampaper) {
						if (err) {
							return responseWrapper.error(err);
						}

						var table = _exampaper2table(exampaper);
						table.relevance_type = 'exampaper';
						table.relevance_id = exampaperId;
						table.permission = 'public';

						return _createTable(table, null, req.apiKey, function (err, body) {

							if (err) {
								return responseWrapper.error(err);
							}

							var id = body.id;

							return _getTableById(id, req.apiKey, false, function (err, body) {
								return generateExcel(err, body);
							});
						});
					});
				});
			}
			return generateExcel(err, body);
		});
	} else {
		return responseWrapper.error('PARAMENT_ERROR', '没有指定下载的双向细目表');
	}
}

function _getKnowledgeByKnowledgeName(knowledges) {
	return function (obj) {
		return _.find(knowledges, function (knowledge) {
			return knowledge.name === obj.name &&
				knowledge.period === obj.period;
		});
	}
}

function _replaceKnowledgeField(knowledge, period, callback) {
	var knowledges = knowledge.split(';');
	var retlist = [];

	_.each(knowledges, function (kn) {
		var k = callback({
			name: kn,
			period: period
		});

		if (k) {
			retlist.push({
				id: k.id,
				name: k.name,
				period: k.period,
				subject: k.subject
			});
		}
	});

	return retlist;
}

function createTableByExcel(req, res) {
	var responseWrapper = new ResponseWrapper(req, res);

	if (!req.files) {
		Logger.error('文件不存在！');
		return responseWrapper.error('HANDLE_ERROR', '你需要文件');
	}
	var file = req.files.pop();
	if (!file) {
		Logger.error('文件不存在！');
		return responseWrapper.error('HANDLE_ERROR', '你需要文件');
	}

	excel.getTableFromExcel(file.path, function (err, table) {

		if (err) {
			Logger.error('excel不合理');
			return responseWrapper.error('HANDLE_ERROR', 'excel不合理');
		}

		rimraf(file.path, function (err) { });

		var names = _.flatten(
			_.map(
				_.pluck(
					_.flatten(
						_.pluck(table.blocks, 'questions')), 'knowledges'), function (knowledges) {
							return knowledges.split(';');
						}));

		var getKnowledges = URL.format({
			protocol: KBSERVER.protocol,
			hostname: KBSERVER.hostname,
			port: KBSERVER.port,
			pathname: '/kb_api/v2/knowledges/names/' + encodeURIComponent(names.join(';')),
			search: qs.stringify({
				api_key: req.apiKey,
			})
		});

		request({
			url: getKnowledges
		}, function (err, response, body) {

			if (err) {
				Logger.error(err.message);
				return responseWrapper.error('HANDLE_ERROR', err.message);
			}

			if (response.statusCode !== 200) {
				Logger.error('code');
				return responseWrapper.error('HANDLE_ERROR');
			}

			var _body = JSON.parse(body);
			var callback = _getKnowledgeByKnowledgeName(_body);

			for (var ix in table.blocks) {
				if (!table.blocks[ix]) {
					continue;
				}
				var questions = [];
				for (var jx in table.blocks[ix].questions) {
					var question = table.blocks[ix].questions[jx];
					try {
						question.knowledges = _replaceKnowledgeField(question.knowledges,
							question.period,
							callback);
						question.period = question.knowledges[0].period;
						question.subject = question.knowledges[0].subject;
						_.each(question.knowledges, function (k) {
							delete k.period;
							delete k.subject;
						});
						if (question.knowledges.length === 0) {
							continue;
						}
					} catch (err) {
						continue;
					}
					questions.push(question);
				}
				table.blocks[ix].questions = questions;
			}

			var postBody = table;
			postBody.permission = 'private';
			postBody.relevance_type = null;
			postBody.relevance_id = null;

			return _createTable(postBody, req.user.id, req.apiKey, function (err, body) {
				if (err) {
					return responseWrapper.error(err);
				}

				return responseWrapper.succ(body);
			});
		});
	});
}

const getUnableKnowledge = (req, res) => {
	let responseWrapper = new ResponseWrapper(req, res);
	if (req.query.freq === 'true') {
		return responseWrapper.error('EXCEED_FRQ_ERROR', res.freq, []);
	}
	try {
		let postUrl = URL.format({
			protocol: KBSERVER.protocol,
			hostname: KBSERVER.hostname,
			port: KBSERVER.port,
			pathname: '/kb_api/v2/tw_specifications/knowledge/',
			search: qs.stringify({
				api_key: req.apiKey
			})
		});
		request.post({
			url: postUrl,
			headers: {
				'content-type': 'application/json'
			},
			body: JSON.stringify(req.body)
		}, function (err, response, body) {
			if (err) {
				Logger.error(err);
				return responseWrapper.error('HANDLE_ERROR', err.message);
			}

			if (response.statusCode !== 200) {
				return responseWrapper.error('HANDLE_ERROR', null);
			}

			let data = JSON.parse(body);
			return responseWrapper.succ(data);
		});
	} catch (err) {
		Logger.error(err);
		return responseWrapper.error('HANDLE_ERROR');
	}
};

module.exports = {
	createTable: createTable,
	getTableList: getTableListNew,
	getHotTable: getHotTable,
	updateTable: updateTable,
	getTableInfo: getTableInfo,
	getTableFromExampaper: getTableFromExampaper,
	createExampaper: createExampaper,
	createExampaperAsync: createExampaperAsync,
	fakeExampaper: fakeExampaper,
	fakeExampaperAsync: fakeExampaperAsync,
	deleteTable: deleteTable,
	downloadTable: downloadTable,
	createTableByExcel: createTableByExcel,
	getUnableKnowledge: getUnableKnowledge
};
