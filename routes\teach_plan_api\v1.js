/**
 * 教学计划
 */

const express = require('express');
const router = express.Router();
const teach_plan = require('../../modules/teach_plan_api/v1/index');

// 获取计划列表
router.get('/list', teach_plan.getList);
// 查看详细
router.get('/detail', teach_plan.getDetail);
// 查看最近的执行中的计划
router.get('/last', teach_plan.getLast);
// 保存计划
router.post('/', teach_plan.save);
// 取消关联
router.delete('/paper', teach_plan.delJoinPaper);

// 增加关联
router.put('/paper', teach_plan.addJoinPaper);

// 考试任务获取推荐试卷
router.get('/paper', teach_plan.getRecoPaper);
// 同步试卷到用户组卷
router.post('/paper/:id', teach_plan.syncToExampaper);

// 删除计划
router.delete('/:id', teach_plan.delPlan);

module.exports = router;
