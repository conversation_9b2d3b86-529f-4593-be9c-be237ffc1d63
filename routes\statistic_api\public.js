const express = require('express');
const router = express.Router();
const config = require('config');
const KBSERVCFG = config.get('KB_API_SERVER');

// const apikey = require('../../modules/middlewares/apikey.js');
const statistic = require('../../modules/statistic_api/v1/statistic.js');

// router.use(apikey('KB'));

// kb api key middleware
router.use(function (req, res, next) {
    req.apiKey = KBSERVCFG.appKey;
    next();

});

router.get('/update', statistic.update);
router.get('/hots', statistic.hots);
router.get('/region', statistic.getRegionExampapersV2);
// router.post('/access_spots', statistic.postAccessSpots);
// router.get('/access_spots', statistic.getAccessSpots);
// router.get('/statistic_event', statistic.event);
module.exports = router;
