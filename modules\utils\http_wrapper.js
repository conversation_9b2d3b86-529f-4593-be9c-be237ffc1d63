var http = require('http');
var https = require('https');
var qs = require('querystring');


function HttpWrapper() {
    this.request = request;
};

/***********************************************************
 * 发送http请求
 * options: {
 *     protocol: string,      // http or https; default = 'http'
 *     method : string,       // post, delete, put, patch,  get; default = 'get'
 *     hostname: string,      // ex: kb.gotiku.com
 *     port: int,             // port;
 *                            // if protocol is http, default = 80;
 *                            // if protocol is https, default = 443;
 *     path: string,          // ex: /kboe/v1/recom/exercises/
 *     query: object,         // ex: {'param1': 'value1'}
 *     headers: object,       // ex: {'coo': 'value1'}
 *     body: object,          // ex: {'param1': 'value1'}
 *     timeout: int,          // timeout: ms; default = 1000
 *     retryTimes: int,       // retry times; default = 0
 * }
 * ********************************************************/
function request(options, callback) {
    // init opt
    var _opt = options;
    _opt.protocol = _opt.protocol ? _opt.protocol : 'http';
    _opt.port = _opt.port ? _opt.port : ('http'==_opt.protocol ? 80 : 443);
    _opt.method = _opt.method ? _opt.method.toLowerCase() : 'get';
    _opt.timeout = _opt.timeout ? _opt.timeout : 1000;
    _opt.retryTimes = _opt.retryTimes ? _opt.retryTimes : 0;
    _opt.headers = _opt.headers ? _opt.headers : {};
	var opt = {
		method: _opt.method,
		hostname: _opt.hostname,
		port: _opt.port,
		path: _opt.path,
		headers: _opt.headers,
	}
	if (_opt.query) {
		opt.path = opt.path + "?" + qs.stringify(_opt.query);
	}
    var cond = ('post' == opt.method ||
                'put' == opt.method ||
                'patch' == opt.method);
    cond = cond && _opt.body;
	var body = null;
	if (cond) {
		body = options.body;
		opt.headers["Content-Length"] = Buffer.byteLength(body);
	}
    var protocol = eval(options.protocol);
    // request
	var req = protocol.request(opt, function (res) {
		res.setEncoding('utf8');
		var headers = res.headers;
		var statusCode = res.statusCode;
		var data = "";
		res.on('data', function (chunk) {
			data += chunk;
		});
		res.on('end', function(){
            var _res = {
                statusCode: statusCode,
                headers: headers,
                data: data,
            };
			return callback(null, _res);
		});
	});
	if (cond) {
		req.write(body+"\n", 'utf8');
	}
	req.on('error', function (e) {
		if (0 == _opt.retryTimes){
			return callback(e);
		} else {
            _opt.retryTimes--;
			request(_opt, callback);	
		}
	});
    // set timeout
	req.setTimeout(_opt.timeout, function() {
		req.abort();
	});
	req.end();
};

module.exports = HttpWrapper;
