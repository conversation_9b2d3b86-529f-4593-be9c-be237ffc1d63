let kb_v2 = require('./kb_api/v2');
let user_v1 = require('./user_api/v1');
let aplus_v1 = require('./aplus_api/v1');
let assemble_v1 = require('./assemble_api/v1');
let search_v2 = require('./search_api/v2');
let favorite_v1 = require('./favorite_api/v1');
let en_api = require('./english_api/v1');
let dmp_api = require('./dmp_api/v1');
let homework_api = require('./homework_api/v1');
let statistic_api = require('./statistic_api/v1');
let profile_api = require('./profile_api/v1');
let tiku_api = require('./tiku_api/v1');
let album_api = require('./album_api/v1');
const tiku_api_v1 = require('./tiku_api_v1/v1');
const goods_api_v1 = require('./goods_api/v1');
const order_api_v1 = require('./order_api/v1');
const payments_api_v1 = require('./payments_api/v1');
const notice_center_api_v1 = require('./notice_center_api/v1');
const resources_api_v1 = require('./resources_api/v1');
const coupons_api_v1 = require('./coupons_api/v1');
const basket_api_v2 = require('./assemble_api/basket');
const exam_api_v1 = require('./exam_api/v1');
const school_api_v1 = require('./school_api/v1');
const teach_plan_aip_v1 = require('./teach_plan_api/v1');
const feedback_api_v1 = require('./feedback_api/v1');
const micro_course_api_v1 = require('./micro_course_api/v1');
const edu_assistant_file_api_v1 = require('./edu_assistant_file_api/v1');
const edu_assistant_tool_api_v1 = require('./edu_assistant_tool_api/v1');
const exam_api_v2 = require('./exam_api/v2');
const exampaper_v1 = require('./exampaper_api/v1');
const resource_download_v1 = require('./resource_download_api/v1');
const learning_analysis_v1 = require('./learning_analysis/v1');
const album_api_v2 = require('./album_api/v2');

const { jiaoyanProxy, yjProxy } = require('../modules/middlewares/proxy');

function route(app) {
	// 运维心跳检测接口
	app.use('/health_check/auth_check', function(req, res){
		res.json({msg:'healthy'});
	});
    // favicon 接口
	app.get('/favicon.ico', function(req, res){
        let options = {
            root: __dirname + '/../public',
        };
        res.sendFile('favicon.ico', options);
	});

	app.get('/user_api/v1/greetings', function(req, res){
        return res.json({
			code: 0,
			msg: 'ok',
			data: {
				vip: req.isVip
			}
		});
    });

	// kb_api接口
	app.use('/kb_api/v2', kb_v2);
	app.use('/user_api/v1', user_v1);
	app.use('/aplus_api/v1', aplus_v1);
	app.use('/assemble_api/v1', assemble_v1);
	app.use('/se_kb/v2', search_v2);
    app.use('/favorite_api/v1', favorite_v1);
	app.use('/english_api/v1', en_api);   //
	app.use('/dmp_api/v1', dmp_api);
    app.use('/homework_api/v1', homework_api);
	app.use('/statistic_api/v1', statistic_api);
	app.use('/profile_api/v1', profile_api);
	app.use('/gateway/v1', tiku_api);
	app.use('/album_api/v1', album_api);
	app.use('/tiku_api/v2', tiku_api_v1);
	app.use('/goods_api/v1', goods_api_v1);
	app.use('/order_api/v1', order_api_v1);
	app.use('/payments_api/v1', payments_api_v1);
	app.use('/notice_center_api/v1', notice_center_api_v1);
	app.use('/resources_api/v1', resources_api_v1);
	app.use('/coupons_api/v1', coupons_api_v1);
	app.use('/assemble_api/v2', basket_api_v2);
	app.use('/exam_api/v1', exam_api_v1);
	app.use('/school_api/v1', school_api_v1);
	app.use('/teach_plan_api/v1', teach_plan_aip_v1);
	app.use('/feedback_api/v1', feedback_api_v1);
	app.use('/micro-course/v1', micro_course_api_v1);
	app.use('/edu_assistant_file/v1', edu_assistant_file_api_v1);
	app.use('/edu_assistant_tools/v1', edu_assistant_tool_api_v1);
	app.use('/exam_api/v2', exam_api_v2);
	app.use('/exampaper_api/v1', exampaper_v1);
	app.use('/resource_download/v1', resource_download_v1);
	app.use('/learning_analysis/v1', learning_analysis_v1);
	app.use('/album_api/v2', album_api_v2);

	// 转发
	jiaoyanProxy('/jiaoyan_api/', app);
	yjProxy('/yj_api/', app);
	

}

module.exports = route;
