const express = require('express');
const router = express.Router();
const statistic = require('../../modules/statistic_api/v1/statistic.js');
const cacheWrapper = require('../../modules/utils/cache_wrapper');

// 这些统计已不再使用，可以删除
// router.get('/update/exampaper', cacheWrapper(statistic.getExamUpdateStatistics, {validTime: 60 * 10}));
// router.get('/exampaper', cacheWrapper(statistic.getExampaperTotalStatistics, {validTime: 60 * 10}));
// router.get('/exampaper/school', cacheWrapper(statistic.getExampaperSchoolStatistics, {validTime: 60 * 10}));
// router.get('/exampaper/region', cacheWrapper(statistic.getExampaperRegionStatistics, {validTime: 60 * 10}));

module.exports = router;
