/*
 * Desc: redis wrapper
 * Author: guochanghui
 */

var Redis = require('ioredis');

function Rediser() {
    this.redis = null;
    this._status = false;
}

Rediser.prototype.init = function (conf, callback) {
    var self = this;
    return new Promise((resolve, reject) => {
        var redis = new Redis({
            port: conf.port,
            host: conf.host,
            db: conf.db,
            password: conf.password,
            retryStrategy: function (times) {
                return Math.min(times * 10, 2000); // delay in ms
            },
        });
        redis.on('connect', function () {
            self._status = true;
            self.redis = redis;
            callback(null);
            resolve();
        });
        redis.on('error', function (err) {
            self._status = false;
            callback(err);
            reject(err);
        });
        //删除锁
        redis.defineCommand('delLock', {
            numberOfKeys: 1,
            lua: 'if redis.call(\'get\',KEYS[1]) == ARGV[1] then return redis.call(\'del\',KEYS[1]) else return 0 end'
        });
    });

};

Rediser.prototype.get = async function (key, callback) {
    var self = this;
    callback = callback || function () { }
    return new Promise((resolve, reject) => {
        self.redis.get(key, function (err, value) {
            try {
                if (err) {
                    callback(err, null);
                    return reject(err);
                }
                if (!value) {
                    callback(null, null);
                    return resolve(null);
                }
                try {
                    var value = JSON.parse(value);
                } catch (err) {
                    callback(err, null);
                    return reject(err);
                }
                callback(null, value);
                return resolve(value);
            } catch (err) {
                return reject(err);
            }
        });
    });
}

Rediser.prototype.get = function (key, callback) {
    this.redis.get(key, function (err, value) {
        if (err) {
            return callback(err, null);
        }
        if (!value) {
            return callback(null, null);
        }
        try {
            var value = JSON.parse(value);
        } catch (err) {
            return callback(err, null);
        }
        return callback(null, value);
    });
}

Rediser.prototype.set = async function (key, value, time, callback) {
    var self = this;
    if (typeof time === 'function') {
        callback = time;
        time = null;
    }
    return new Promise((resolve, reject) => {
        try {
            if (!value) {
                return resolve();
            }
            value = JSON.stringify(value);
            callback = callback || function () { };
            if (!time) {
                self.redis.set(key, value, callback);
                return resolve();
            } else {
                self.redis.setex(key, time, value, callback);
                return resolve();
            }
        } catch (err) {
            return reject(err);
        }
    });
}

Rediser.prototype.del = function (key, callback) {
    let self = this;
    callback = callback || function () { };
    return new Promise((resolve, reject) => {
        try {
            self.redis.del(key, callback);
            resolve();
        } catch (err) {
            return reject(err);
        }
    });
}

Rediser.prototype.lock = function (lockKey, value, expireTime) {
    if (!value) {
        return Promise.reject(new Error('缓存value不能为空'));
    }
    try {
        if (typeof value === 'object') {
            value = JSON.stringify(value);
        }
    } catch (e) {
        return Promise.reject(e);
    }

    return this.redis.set(lockKey, value, 'NX', 'EX', expireTime);
}

Rediser.prototype.unlock = function (lockKey, value) {
    if (!value) {
        return Promise.reject(new Error('缓存value不能为空'));
    }
    return this.redis.delLock(lockKey, value);
}

module.exports = new Rediser();
