/*!
 * Author: guochanghui
 * Date: 2017-04-24
 */

/**
 * Module dependencies.
 * @private
 */
const URL = require('url');
const qs = require('qs');
const util = require('util');
const request = require('request');
const config = require('config');
const _ = require('underscore');
const Joi = require('@hapi/joi');
const axios = require('axios');
const lodash = require('lodash');
const ObjectID = require("mongodb").ObjectID;

const KBAPISERV = config.get('KB_API_SERVER');
const ZYK_SERVER = config.get('ZYK_SERVER');
const YJSERVER = config.get('YJ_API_SERVER');

const logger = require('../../utils/logger');
const utils = require('../../utils/utils');
const extend_ques = require('./extend_ques');
const scantron = require('./scantron');
const enums = require('../../../bin/enum');
const mongodber = require('../../utils/mongodber');
const rediser = require('../../utils/rediser');
const assets = require('../../assets_api/v1/index');
const ResponseWrapper = require('../../middlewares/response_wrapper');
const db = mongodber.use('tiku');
const db_kb_zyk = mongodber.use('kb_zyk');
const db_tiku_open = mongodber.use('tiku_open');

const { getQuestionsByIds } = require('../../kb_api/v2/downloader');
const user_log_service = require('../../user_api/v1/user_log_service');

const moment = require('moment');
const client = require('../../client');
const paper_utils = require('../../utils/paper_utils');
const basket_service = require('../../assemble_api/v2/basket.service');
/**
 * Module variables.
 * @private
 */
const collection = db.collection('exampaper');

const DifficultyRange = {
    '不限': [1, 2, 3, 4, 5],
    '容易': [1, 2, 3],
    '普通': [2, 3, 4],
    '困难': [3, 4, 5]
};

/**
 * Author: YuLei
 * @param {*} questionIds
 */
const _updateQuestionsUseTime2KB = (questionIds) => {
    if (questionIds.length > 0) {
        let postUrl = URL.format({
            protocol: KBAPISERV.protocol,
            hostname: KBAPISERV.hostname,
            port: KBAPISERV.port,
            pathname: '/kb_api/v2/assemble/questions/update_use_times',
            search: qs.stringify({ api_key: KBAPISERV.appKey })
        });
        request.post({
            url: postUrl,
            headers: {
                'content-type': 'application/json'
            },
            body: JSON.stringify(questionIds)
        }, function (error, response, _body) {
            if (error) {
                logger.error(error);
            }
        });
    }
};

/**
 * Module functions.
 */
async function post_exampaper(req, res) {
    let resWrapper = new ResponseWrapper(req, res);
    let user = req.user;
    const user_id = user.id;
    const open_user_id = utils.formatUserId(user_id);
    let exampaper = req.body;
    exampaper['user_id'] = open_user_id;
    exampaper['ctime'] = exampaper['utime'] = new Date();
    exampaper['invalid_time'] = moment().add(user.expireMonth, 'month').toDate();
    exampaper['province'] = user.province;
    exampaper['city'] = user.city;
    exampaper['sch_id'] = user.schoolId;
    exampaper['sch_name'] = user.schoolName;
    delete exampaper.exampaper_download_num;
    delete exampaper.view_count;
    delete exampaper.expired_time;
    delete exampaper.last_sync_time;
    delete exampaper.dtk_id;
    delete exampaper.yj_exams;

    //获取试卷中试题id
    let questionIds = [];
    let volumes = exampaper.volumes;
    if (volumes.length > 0) {
        for (let i = 0; i < volumes.length; i++) {
            let blocks = volumes[i].blocks;
            if (blocks.length > 0) {
                for (let j = 0; j < blocks.length; j++) {
                    let questions = blocks[j].questions;
                    if (questions.length > 0) {
                        for (let z = 0; z < questions.length; z++)
                            questionIds.push(questions[z].id);
                    }
                }
            }
        }
    }
    // 添加试题信息
    let questionInfos = await getQuestionsByIds(questionIds) || [];
    let questionMap = {};
    questionInfos.forEach(item => questionMap[item.id] = item);
    (volumes || []).forEach(volume => {
        (volume.blocks || []).forEach(block => {
            (block.questions || []).forEach(question => {
                question.blocks = (questionMap[question.id] && questionMap[question.id].blocks) || question.blocks;
            })
        })
    });
    const {from_year, to_year} = utils.getAcademicYear();
    // if (!params.from) params.from = enums.PaperFrom.GROUP; // 默认用户组卷
    exampaper.ctime = new Date();
    exampaper.utime = new Date();
    exampaper.valid = enums.BooleanNumber.YES;
    exampaper.source_type = enums.PaperSourceType.ASSEMBLE;
    exampaper.source = enums.PaperSourceType.ASSEMBLE;
    exampaper.source_id = enums.PaperSourceType.ASSEMBLE;
    exampaper.status = enums.PaperStatus.DONE;
    exampaper.exam_status = enums.ExamStatus.EDITABLE;
    if (!exampaper.from_year) exampaper.from_year = from_year;
    if (!exampaper.to_year) exampaper.to_year = to_year;
    const result = await db_tiku_open.collection(enums.OpenSchema.user_paper).insertOne(exampaper);
    // 更新试题引用
    _updateQuestionsUseTime2KB(questionIds);
    await user_log_service.assembleExampaper(req.user, result.insertedId.toString());
    return resWrapper.succ({ id: result.insertedId.toString() });
}

function get_exampaper_from_tiku(user_id, exampaper_id, callback) {
    let query = {
        _id: new ObjectID(exampaper_id)
    };
    let options = {};
    collection.findOne(query, options, function (err, exam) {
        if (err) {
            logger.error(err);
            return callback('HANDLE_ERROR', null);
        }
        if (!exam) {
            db.collection('upload_exampaper').findOne(query, options, function (err, up_exam) {
                if (err) {
                    logger.error(err);
                    return callback('HANDLE_ERROR', null);
                }
                if (!up_exam) {
                    return callback('NULL_ERROR', null);
                }
                if (user_id !== up_exam.user_id) {
                    return callback('HANDLE_ERROR', null);
                }
                extend_ques.extend_ques(up_exam, function (err) {
                    if (err) {
                        logger.error(err);
                        return callback('HANDLE_ERROR', null);
                    }
                    return callback(null, up_exam);
                });
            });
        } else {
            if (exam.share_sort) {
                extend_ques.extend_ques(exam, function (err) {
                    if (err) {
                        logger.error(err);
                        return callback('HANDLE_ERROR', null);
                    }
                    return callback(null, exam);
                });
            } else {
                if (user_id !== exam.user_id) {
                    return callback('HANDLE_ERROR', null);
                }
                extend_ques.extend_ques(exam, function (err) {
                    if (err) {
                        logger.error(err);
                        return callback('HANDLE_ERROR', null);
                    }
                    return callback(null, exam);
                });
            }
        }
    });
}

// 从 zyk 中获取试卷
function requestExampaperFromZyk(cookie, exampaperId, callback) {
    try {
        let exampaperUrl = URL.format({
            protocol: ZYK_SERVER.protocol,
            hostname: ZYK_SERVER.hostname,
            pathname: `/ptk_api/v1/assemble/exampapers/${exampaperId}`,
            port: ZYK_SERVER.port
        });
        request.get({
            url: exampaperUrl,
            timeout: 2000,
            headers: {
                'Content-Type': 'application/json',
                'cookie': cookie.replace(/tiku/g, 'ptk')
            }
        }, function (error, response, _body) {
            if (error || !response.hasOwnProperty('statusCode') || response.statusCode >= 500) {
                return callback('HANDLE_ERROR');
            }
            let body = JSON.parse(_body);
            if (!body.hasOwnProperty('data') || !body.data || !body.data.hasOwnProperty('exampaper')) {
                return callback('NULL_ERROR');
            }
            return callback(null, body.data.exampaper);
        });
    } catch (err) {
        callback(err);
    }
}


// 从 zyk 中获取试卷
// async function requestExampaperFromZykAsync(cookie, exampaperId) {
//     let exampaperUrl = URL.format({
//         protocol: ZYK_SERVER.protocol,
//         hostname: ZYK_SERVER.hostname,
//         pathname: `/ptk_api/v1/assemble/exampapers/${exampaperId}`,
//         port: ZYK_SERVER.port
//     });
//     let res = await axios.get(exampaperUrl, {
//         timeout: 2000,
//         headers: {
//             'Content-Type': 'application/json',
//             'cookie': cookie.replace(/tiku/g, 'ptk')
//         }
//     })
//
//     if (res.status >= 500) throw 'HANDLE_ERROR';
//     let data = res.data && res.data.data || {};
//     if (!data.exampaper) throw 'NULL_ERROR';
//     return data.exampaper;
// }

//
// function handleExampaper(exampaper) {
//     exampaper.score = 0;
//     let volumes = exampaper.volumes;
//     for (let v = 0; v < volumes.length; v++) {
//         let volume = volumes[v];
//         let blocks = volume.blocks;
//         for (let b = 0; b < blocks.length; b++) {
//             let block = blocks[b];
//             block.default_score = Number(block.default_score);
//             let questions = block.questions;
//             if (questions.length > 0) {
//                 let ts = 0;
//                 for (let i = 0; i < questions.length; i++) {
//                     questions[i].score = Number(questions[i].score);
//                     ts += Number(questions[i].score);
//                 }
//                 exampaper.score += Number(ts);
//             }
//         }
//     }
//
//     return exampaper;
// }

async function put_exampaper(req, res) {
    let resWrapper = new ResponseWrapper(req, res);
    let user_id = req.user.id;
    const open_user_id = utils.formatUserId(user_id);
    let exampaper_id = req.params.exampaper_id;
    let filter = {
        _id: new ObjectID(exampaper_id),
        user_id: open_user_id,
    };
    const paper = await db_tiku_open.collection(enums.OpenSchema.user_paper).findOne(filter);
    if (lodash.isEmpty(paper)
        || paper.source !== enums.PaperSourceType.UPLOAD
        || paper.status !== enums.PaperStatus.EDIT
    ) return resWrapper.error('HANDLE_ERROR', '不可编辑');
    let exampaper = req.body;
    if (exampaper.status === enums.PaperStatus.DONE) {
        await syncPaperQuestions(open_user_id, exampaper);
    }
    delete exampaper['_id'];
    exampaper['user_id'] = open_user_id;
    exampaper['utime'] = new Date();
    exampaper['invalid_time'] = moment().add(req.user.expireMonth, 'month').toDate();
    if (exampaper.hasOwnProperty('ctime')) {
        delete exampaper['ctime']
    }
    await db_tiku_open.collection(enums.OpenSchema.user_paper).updateOne(filter, { $set: exampaper });
    return resWrapper.succ({ id: exampaper_id });
    //对试卷内部数据进行处理
    // exampaper = handleExampaper(exampaper);

    // let options = {};
    // collection.find(filter).toArray(function (err, arr) {
    //     if (err) {
    //         logger.error(err);
    //         return resWrapper.error('HANDLE_ERROR');
    //     }
    //     console.log(exampaper)
    //     if (arr.length > 0) {
    //         collection.updateOne(filter, { $set: exampaper }, options, function (err, result) {
    //             if (err) {
    //                 logger.error(err);
    //                 return resWrapper.error('HANDLE_ERROR');
    //             }
    //             if (!result) {
    //                 return resWrapper.error('NULL_ERROR');
    //             }
    //             return resWrapper.succ({ id: exampaper_id });
    //         });
    //     } else {
    //         return resWrapper.error('NULL_ERROR');
    //     }
    // });
}

async function syncPaperQuestions(user_id, paper) {
    if (!paper) return;
    const { to_year } = utils.getAcademicYear();
    // 同步试题
    for (const volume of paper.volumes) {
        for (const block of volume.blocks) {
            for (const index in block.questions) {
                const ques = block.questions[index];
                if (ques.source !== enums.QuestionSource.UPLOAD) {
                    continue;
                }
                const newQues = _.pick(ques, [
                    'type', 'period', 'subject', 'grade', 'difficulty', 'score', 'description', 'comment', 'year',
                    'knowledges', 'audio', 'source', 'source_id', 'blocks'
                ]);
                newQues.grade = paper.grade;
                newQues.user_id = user_id;
                newQues.valid = enums.BooleanNumber.YES;
                newQues.ctime = new Date();
                newQues.utime = new Date();
                if (!newQues.year) newQues.year = to_year;
                if (!newQues.refer_exampapers) newQues.refer_exampapers = [];
                if (!newQues.difficulty) newQues.difficulty = 3;
                const insert = await db_tiku_open.collection(enums.OpenSchema.user_question).insertOne(newQues);
                block.questions[index] = {
                    id: insert.insertedId.toString(),
                    source: enums.QuestionSource.ZX,
                    source_id: insert.insertedId.toString(),
                    score: ques.score,
                    type: ques.type,
                    period: ques.period,
                    subject: ques.subject,
                };
            }
        }
    }
}


/**
 * delete a exampaper
 */
const delete_exampaper = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let user_id = req.user.id;
        const open_user_id = utils.formatUserId(user_id);
        let exampaper_id = req.params.exampaper_id;
        let filter = {
            _id: new ObjectID(exampaper_id),
            user_id: open_user_id,
        };
        const exampaperInfo = await db_tiku_open.collection(enums.OpenSchema.user_paper).findOne(filter);
        if (!exampaperInfo) return responseWrapper.error('PARAMETERS_ERROR', '用户无此考试');
        if (exampaperInfo.yj_exams || exampaperInfo.dtk_id || exampaperInfo.last_sync_time) return responseWrapper.error('PARAMETERS_ERROR', '已关联阅卷考试');
        const result = await db_tiku_open.collection(enums.OpenSchema.user_paper).updateOne(filter, {$set: {utime: new Date(), valid: enums.BooleanNumber.NO}});
        return responseWrapper.succ({ id: exampaper_id });
    } catch (err) {
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

/**
 * 获取用户对最近一次组卷信息
 */
const lastExampaper = async (req, res) => {
    let resWrapper = new ResponseWrapper(req, res);
    try {
        let user_id = req.user.id;
        const open_user_id = utils.formatUserId(user_id);
        const date = moment().subtract(180, 'day').toDate();
        let filter = {
            user_id: open_user_id,
            source: enums.PaperSourceType.ASSEMBLE,
            valid: enums.BooleanNumber.YES,
            ctime: {
                $gte: date
            }
        };

        const doc = await db_tiku_open.collection(enums.OpenSchema.user_paper).findOne(filter, { sort: { ctime: -1 } });
        return resWrapper.succ(lodash.isEmpty(doc) ? '' : { name: doc.name, id: doc._id });
    } catch (err) {
        console.log(err);
        return resWrapper.error('HANDLE_ERROR', err.message);
    }
}


/*
 * Author: YuLei
 * DESC:
 * 		获取试卷分类信息
 * URL:
 * 		/assemble_api/v2/user/{user_id}/exampapers/list/
 * Method:
 * 		GET
 */
const list_exampapers = async (req, res) => {
    let resWrapper = new ResponseWrapper(req, res);
    try {
        let user_id = req.user.id;
        const open_user_id = utils.formatUserId(user_id);
        let offset = parseInt(req.query.offset || 0);
        let limit = parseInt(req.query.limit || 20);
        if (isNaN(offset) || isNaN(limit)) {
            return resWrapper.error('PARAMETERS_ERROR');
        }
        let result = {};
        let cond = {
            user_id: open_user_id,
            source: enums.PaperSourceType.ASSEMBLE,
            valid: enums.BooleanNumber.YES
        };
        if (req.query.type)
            cond.type = req.query.type;
        if (req.query.grade)
            cond.grade = req.query.grade;
        if (req.query.subject)
            cond.subject = req.query.subject;
        if (req.query.period)  cond.period = req.query.period;
        let options = {};
        const count = await db_tiku_open.collection(enums.OpenSchema.user_paper).count(cond);
        result['total_num'] = count;
        let proj = { _id: 1, name: 1, utime: 1, download_time: 1, volumes: 1, period: 1, subject: 1, grade: 1, press_version: 1, share_sort: 1, expired_time: 1, invalid_time: 1, dtk_id: 1, last_sync_time: 1, type: 1 };
        const items = await db_tiku_open.collection(enums.OpenSchema.user_paper).find(cond).sort({ utime: -1 }).skip(offset).limit(limit).project(proj).toArray();
        let exampapers = [];

        // const dtkInfo = await util.promisify(scantron._GetDTKGateway)(req.user);
        for (let i = 0; i < items.length; i++) {
            let item = items[i];
            //最近更新时间
            let utimeStr = '';
            if (item['utime']) {
                utimeStr = new Date(item['utime']).toLocaleDateString();
                let time = new Date(item['utime']).toLocaleTimeString();
                let timeArr = time.split(':');
                utimeStr += ' ' + timeArr[0] + ':' + timeArr[1];
            }

            //最近下载时间
            let download_time = '未下载';
            if (item.download_time) {
                download_time = new Date(item.download_time).toLocaleDateString() + ' ';
                let time = new Date(item.download_time).toLocaleTimeString();
                let timeArr = time.split(':');
                download_time += timeArr[0] + ':' + timeArr[1];
            }

            // 获取dtk_id
            let dtk_id = item.dtk_id;
            // if (!dtk_id) {
            //     const scantronUrl = URL.format({
            //         protocol: config.get('SCANTRON').protocol,
            //         hostname: config.get('SCANTRON').hostname,
            //         pathname: `/zj/downloaded/${item['_id']}`,
            //         port: config.get('SCANTRON').port
            //     });
            //     const dtkData = await axios.post(scantronUrl, dtkInfo);
            //     dtk_id = dtkData.data && dtkData.data.data && dtkData.data.data.dtkId;
            // }

            exampapers.push({
                id: item['_id'],
                name: item['name'],
                utime: item.utime && new Date(item.utime).getTime(),
                question_num: paper_utils.get_question_num(item),
                download_time: item.download_time && new Date(item.download_time).getTime() || '未下载',
                grade: item['grade'],
                period: item['period'],
                subject: item['subject'],
                expired_time: item.expired_time && new Date(item.expired_time).getTime(),
                invalid_time: item.invalid_time && new Date(item.invalid_time).getTime(),
                last_sync_time: item.last_sync_time && new Date(item.last_sync_time).getTime(),
                dtk_id: dtk_id,
                press_version: item['press_version'],
                share_sort: item['share_sort'] ? item['share_sort'] : '',
                type: item.type,
            });
        }
        exampapers.sort((a, b) => (new Date(b.utime).getTime() - new Date(a.utime)));
        result['exampapers'] = exampapers;
        return resWrapper.succ(result);

    } catch (err) {
        logger.error(err);
        return resWrapper.error('HANDLE_ERROR');
    }
}
// 弃用
function knowledges_exampaper(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    if (req.query.freq === 'true') {
        return responseWrapper.error('EXCEED_FRQ_ERROR', res.freq, []);
    }
    try {
        let exampaperUrl = URL.format({
            protocol: KBAPISERV.protocol,
            hostname: KBAPISERV.hostname,
            port: KBAPISERV.port,
            pathname: '/kb_api/v2/assemble/exampaper',
            search: qs.stringify({
                api_key: req.apiKey
            })
        });

        let questionNum = 0;
        for (let index = 0; index < req.body.blocks.length; index++) {
            let curBlock = req.body.blocks[index];
            questionNum += curBlock['num'];
        }
        let difficulty = req.body.difficulty;
        if (difficulty === '一般') difficulty = '普通';
        let postBody = {
            difficulty: difficulty,
            knowledges_ids: req.body.knowledges_ids,
            blocks: req.body.blocks,
            province: req.body.province,
            city: req.body.city
        };
        request.post({
            url: exampaperUrl,
            headers: {
                'content-type': 'application/json'
            },
            body: JSON.stringify(postBody)
        }, function (error, response, _body) {
            if (response.statusCode >= 500 || error) {
                return responseWrapper.error('HANDLE_ERROR');
            } else if (response.statusCode >= 400 && response.statusCode < 500) {
                let ret = JSON.parse(_body);
                return responseWrapper.send(ret);
            }
            let body = JSON.parse(_body);
            if (questionNum !== body.length) {
                return responseWrapper.error('NULL_ERROR', '', body);
            }
            return responseWrapper.succ(body);
        });
    } catch (err) {
        logger.error(err);
        return responseWrapper.error('HANDLE_ERROR');
    }
}

/**
 * 算法知识点组卷
 * @param req
 * @param res
 * @returns {Promise<void>}
 */
async function algo_knowledges_exampaper(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const result = [];
        const algoDiffMap = {
            '容易': '容易',
            '一般': '中等',
            '普通': '中等',
            '困难': '困难',
            '不限': '不限',
        };
        const knowledges  = await client.kb.getKnowledgeByIds(req.body.knowledges_ids);
        let difficulty = req.body.difficulty;
        const period = req.body.period;
        const subject = req.body.subject;
        if (!period || !subject) return responseWrapper.error('PARAMETERS_ERROR', '学科学段不能为空');
        let params = {
            period: period,
            subject: subject,
            difficulty: algoDiffMap[difficulty] || '中等',
            knowledges: knowledges,
            ques_num: req.body.blocks,
            // province: req.body.province,
            // city: req.body.city
        };
        const algoPaper = await client.algo.knowledgesPaper(params);
        if (_.isEmpty(algoPaper)) return responseWrapper.succ(result);
        // 清空试题篮
        const del_res = await basket_service.delete_basket(req.user.id, period, subject);
        if (del_res.error) return responseWrapper.error(del_res.type, del_res.desc);
        // 添加试题
        const question_ids = [];
        for (const b of algoPaper.blocks) {
            question_ids.push(...b.questions.map(q => q.id));
        }
        const kb_questions = await client.kb.getQuestions(question_ids);
        const questions = [];
        for (const id of question_ids) {
            const q = kb_questions.find(e=> e.id === id);
            if (!q) continue;
            questions.push({
                id: q.id,
                type: q.type,
                period: period,
                subject: subject,
                source: enums.QuestionSource.SYS,
                source_id: q.id
            })
        }
        await basket_service.post_questions(req.user.id, period, subject, questions);
        return responseWrapper.succ(paper_utils.get_profile_basket(req.user.id, questions));
    } catch (e) {
        logger.error(e);
        return responseWrapper.error('HANDLE_ERROR');
    }
}


function get_exampaper(req, res) {
    let resWrapper = new ResponseWrapper(req, res);
    let userId = req.user.id;
    let exampaperId = req.params.exampaper_id;
    return get_exampaper_from_tiku(userId, exampaperId, function (err, exam) {
        if (err != null && err !== 'NULL_ERROR') {
            return resWrapper.error(err);
        }
        if (exam && exam.length !== 0) {
            // 处理试题题号和去除题内题号
            ques_process(exam);
            // 隐藏试题答案，解析，解答信息
            (exam.volumes || []).forEach(volume => {
                (volume.blocks || []).forEach(block => {
                    (block.questions || []).forEach(question => {
                        question.blocks = {
                            types: question.blocks.types,
                            stems: question.blocks.stems,
                            knowledges: question.blocks.knowledges,
                        }
                    })
                })
            });
            if (res.freq !== '' && res.freq !== undefined) {
                paper_utils.deleteQuestionBlocks(exam.volumes);
                return resWrapper.send({
                    code: 6,
                    msg: res.freq,
                    data: exam
                });
            }
            let options = {
                $inc: {
                    view_count: 1
                }
            }
            let cond = {
                _id: new ObjectID(exampaperId),
            };
            db.collection('exampaper').update(cond, options);
            return resWrapper.succ(exam);
        }

        requestExampaperFromZyk(req.headers.cookie, exampaperId, function (error, exampaper) {
            if (error) {
                return resWrapper.error(error);
            }
            // 处理试题题号和去除题内题号
            ques_process(exampaper)
            if (res.freq !== '' && res.freq !== undefined) {
                paper_utils.deleteQuestionBlocks(exampaper.volumes);
                return resWrapper.send({
                    code: 6,
                    msg: res.freq,
                    data: exampaper
                });
            }
            return resWrapper.succ(exampaper);
        });
    });
}

const get_exampaper_async = async function (req, res) {
    let resWrapper = new ResponseWrapper(req, res);
    try {
        // const isFilter = req.query.filter === 'false' ? false : true;
        // let userId = req.user.id;
        let exampaper_id = req.params.exampaper_id;
        const paper = await get_exampaper_by_id_async(exampaper_id);
        if (lodash.isEmpty(paper)) {
            return resWrapper.error('NULL_ERROR');
        }
        // 隐藏试题答案、解析、解答
        // paper_utils.deleteQuestionBlocks(paper.volumes);
        if (res.freq) {
            paper_utils.deleteQuestionBlocks(paper.volumes);
        }
        return resWrapper.succ(paper);
    } catch (e) {
        logger.error(e);
        return resWrapper.error('HANDLE_ERROR', e.message);
    }
}

const getQuestionNumByKnowledge = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        if (!req.body.diff || !req.body.kids || !req.body.type) {
            return responseWrapper.error('PARAMETERS_ERROR');
        }
        let diff = DifficultyRange[req.body.diff];
        let type = req.body.type;
        let cond = {};
        // kid means knowledge ids
        if (req.body.kids) {
            cond._id = { $in: req.body.kids };
        }
        let knowledges = await db.collection('question_num').find(cond).toArray();
        let retObj = {};
        _.each(_.pluck(knowledges, 'questions'), (question) => {
            for (let tp in question) {
                if (!retObj[tp]) {
                    retObj[tp] = 0;
                }
                retObj[tp] += _.reduce(_.pick(question[tp], diff), function (memo, num) {
                    return memo + num;
                }, 0)
            }
        });
        retObj = _.pick(retObj, type);
        responseWrapper.succ(retObj);
    } catch (err) {
        Logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

const postExampaperToYjSchema = Joi.object({
    exam_name: Joi.string().required(),
    start_time: Joi.date().required(),
    end_time: Joi.date().required(),
    // subject: Joi.string().valid(...Object.values(enums.GoodsType)).required(),
    // grade: Joi.string().valid(...Object.values(enums.GoodsType)).required(),
    // subject: Joi.string().required(),
    grade: Joi.string().required(),
}).unknown(true);

const postExampaperToYj = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let { exam_name, start_time, end_time, grade } = await Joi.validate(req.body, postExampaperToYjSchema);
        const examId = req.params.exampaper_id;

        const exampaperInfo = await db.collection('exampaper').findOne({ _id: new ObjectID(examId), });
        if (!exampaperInfo || exampaperInfo.user_id !== req.user.userId) return responseWrapper.error('PARAMETERS_ERROR', '用户无此考试');
        const subject = exampaperInfo.subject;

        // 获取答题卡id
        const dtkInfo = await util.promisify(scantron._GetDTKGateway)(req.user);
        const scantronUrl = URL.format({
            protocol: config.get('SCANTRON').protocol,
            hostname: config.get('SCANTRON').hostname,
            pathname: `/zj/downloaded/${examId}`,
            port: config.get('SCANTRON').port
        });
        const dtkData = await axios.post(scantronUrl, dtkInfo);
        if (dtkData.data && dtkData.data.code === 1 && dtkData.data.msg === '没有关联的答题卡') return responseWrapper.error('NULL_ERROR', '需要下载答题卡');

        // 消耗下载次数
        if (req.user.vipType === enums.MemberType.TIKU_PERSONAL && ((exampaperInfo.expired_time || 0) < new Date())) {
            await assets.deductionTicketItem(1, req.user.userId, req.user.ucId, enums.ResourceType.ASSEMBLE_EXAMPAPER, [req.body.resource_ids[0]]);
            await db.collection('user').updateOne({ _id: req.user.userId }, {
                $set: { use_time: new Date() },
                $inc: { assemble_download_num: 1 }
            });
            await db.collection('exampaper').update({ _id: new ObjectID(examId) }, { $set: { expired_time: moment().add(1, 'month').startOf('day').toDate() } });
        }
        if (Object.values(enums.SchoolVipType).includes(req.user.vipType) && ((exampaperInfo.expired_time || 0) < new Date())) {
            const userInfo = await db.collection('user').findOne({ _id: req.user.userId });
            const schoolInfo = await db.collection('school_info').findOne({ _id: req.user.schoolId });
            if ((schoolInfo && schoolInfo.assemble_download_num || 0) <= (userInfo && userInfo.assemble_download_num || 0)) {
                return responseWrapper.error('OVER_LIMIT_ERROR', '您的账号已达本月数量上限');
            }
            await db.collection('user').updateOne({ _id: req.user.userId }, {
                $set: { use_time: new Date() },
                $inc: { assemble_download_num: 1 }
            });
            await db.collection('exampaper').update({ _id: new ObjectID(examId) }, { $set: { expired_time: moment().add(1, 'month').startOf('day').toDate() } });
        }

        let data = {
            examName: exam_name,
            startTime: start_time,
            endTime: end_time,
            examType: '普通',
            examScope: '校内',
            scanMode: 0,
            reviewMode: dtkData.data.data.reviewMode,
            uploadMode: 0,
            isFenCeng: 0,
            isZujuan: true,
            subjects: [
                {
                    grade: grade,
                    pType: '0-0',
                    course: subject,
                    isZujuan: true,
                    zujuanId: examId,
                    dtkId: dtkData.data.data.dtkId,
                }
            ],
            schools: [
                {
                    schoolId: req.user.schoolId,
                    schoolName: req.user.schoolName
                }
            ],
            examMode: 'normal',
            examRange: 0,
            autoAdd: {
                autoAddExamManager: false,
                autoAddKaosheng: false,
                autoAddReviewers: false
            }
        };
        const token = await utils.getYjToken(data, req.user);

        let yjUrl = URL.format({
            protocol: YJSERVER.protocol,
            hostname: YJSERVER.hostname,
            pathname: '/v1/zujuan/exam/create',
            port: YJSERVER.port,
        });
        let yjDate = await axios.post(yjUrl, data, { headers: { token } }).then(utils.yjHandler);
        await db.collection('exampaper').update({ _id: new ObjectID(examId), }, {
            $set: {
                last_sync_time: new Date(),
                dtk_id: dtkData.data.data.dtkId,
            },
            $push: {
                'yj_exams': {
                    subject_name: `${grade}${subject}`,
                    exam_name,
                    exam_id: +yjDate.examId,
                    subject_id: +yjDate.paperId,
                }
            }
        });

        await db.collection('@SyncYjRecord').insertOne({
            sync_time: new Date(),
            operator: req.user.userId,
            type: enums.SyncYjExamType.CREATE,
            exampaper_id: examId,
            dtk_id: dtkData.data.data.dtkId,

            subject_name: `${grade}${subject}`,
            exam_name,
            exam_id: +yjDate.examId,
            subject_id: +yjDate.paperId,
        });
        return responseWrapper.succ({});
    } catch (err) {
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

const postExampaperBindYjSchema = Joi.object({
    exam_name: Joi.string().required(),
    subject_name: Joi.string().required(),
    exam_id: Joi.number().required(),
    subject_id: Joi.number().required(),
}).unknown(true);

const postExampaperBindYj = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let { exam_name, subject_name, exam_id, subject_id } = await Joi.validate(req.body, postExampaperBindYjSchema);
        const exampaperId = req.params.exampaper_id;

        const exampaperInfo = await db.collection('exampaper').findOne({ _id: new ObjectID(exampaperId), });
        if (!exampaperInfo || exampaperInfo.user_id !== req.user.userId) return responseWrapper.error('PARAMETERS_ERROR', '用户无此考试');

        // 获取答题卡id
        const dtkInfo = await util.promisify(scantron._GetDTKGateway)(req.user);
        const scantronUrl = URL.format({
            protocol: config.get('SCANTRON').protocol,
            hostname: config.get('SCANTRON').hostname,
            pathname: `/zj/downloaded/${exampaperId}`,
            port: config.get('SCANTRON').port
        });
        const dtkData = await axios.post(scantronUrl, dtkInfo);
        if (dtkData.data && dtkData.data.code === 1 && dtkData.data.msg === '没有关联的答题卡') return responseWrapper.error('NULL_ERROR', '需要下载答题卡');

        // 消耗下载次数
        if (req.user.vipType === enums.MemberType.TIKU_PERSONAL && ((exampaperInfo.expired_time || 0) < new Date())) {
            await assets.deductionTicketItem(1, req.user.userId, req.user.ucId, enums.ResourceType.ASSEMBLE_EXAMPAPER, [req.body.resource_ids[0]]);
            await db.collection('user').updateOne({ _id: req.user.userId }, {
                $set: { use_time: new Date() },
                $inc: { assemble_download_num: 1 }
            });
            await db.collection('exampaper').update({ _id: new ObjectID(exampaperId) }, { $set: { expired_time: moment().add(1, 'month').startOf('day').toDate() } });
        }
        if (Object.values(enums.SchoolVipType).includes(req.user.vipType) && ((exampaperInfo.expired_time || 0) < new Date())) {
            const userInfo = await db.collection('user').findOne({ _id: req.user.userId });
            const schoolInfo = await db.collection('school_info').findOne({ _id: req.user.schoolId });
            if ((schoolInfo && schoolInfo.assemble_download_num || 0) <= (userInfo && userInfo.assemble_download_num || 0)) {
                return responseWrapper.error('OVER_LIMIT_ERROR', '您的账号已达本月数量上限');
            }
            await db.collection('user').updateOne({ _id: req.user.userId }, {
                $set: { use_time: new Date() },
                $inc: { assemble_download_num: 1 }
            });
            await db.collection('exampaper').update({ _id: new ObjectID(exampaperId) }, { $set: { expired_time: moment().add(1, 'month').startOf('day').toDate() } });
        }

        let data = {
            examId: exam_id,
            subjectId: subject_id,
            zujuanId: exampaperId,
            dtkId: dtkData.data.data.dtkId,
        };
        const token = await utils.getYjToken(data, req.user);
        let yjUrl = URL.format({
            protocol: YJSERVER.protocol,
            hostname: YJSERVER.hostname,
            pathname: '/v1/bind/subject',
            port: YJSERVER.port,
        });
        let yjDate = await axios.post(yjUrl, data, { headers: { token } }).then(utils.yjHandler);

        await db.collection('exampaper').updateOne({ _id: new ObjectID(exampaperId), }, {
            $set: {
                last_sync_time: new Date(),
                dtk_id: dtkData.data.data.dtkId,
            },
        });

        let curExam = (exampaperInfo.yj_exams || []).find(e => e.exam_id === exam_id && e.subject_id === subject_id);
        if (!curExam) {
            await db.collection('exampaper').updateOne({ _id: new ObjectID(exampaperId), }, {
                $push: {
                    'yj_exams': {
                        subject_name,
                        exam_name,
                        exam_id,
                        subject_id,
                    }
                }
            });
        }
        await db.collection('@SyncYjRecord').insertOne({
            sync_time: new Date(),
            operator: req.user.userId,
            type: enums.SyncYjExamType.BIND,
            exampaper_id: exampaperId,
            dtk_id: dtkData.data.data.dtkId,

            subject_name,
            exam_name,
            exam_id,
            subject_id,
        });
        return responseWrapper.succ({});
    } catch (err) {
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

const getExampaperYjExamList = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const exampaperId = req.params.exampaper_id;
        const exampaperInfo = await db.collection('exampaper').findOne({ _id: new ObjectID(exampaperId), });
        if (!exampaperInfo || exampaperInfo.user_id !== req.user.userId) return responseWrapper.error('PARAMETERS_ERROR', '用户无此考试');
        if (!exampaperInfo.yj_exams) return responseWrapper.succ([]);

        const yjExams = lodash.reverse(exampaperInfo.yj_exams.slice(0, 10));
        const subjectIds = yjExams.map(e => e.subject_id);

        let data = {
            zujuanBindInfos: subjectIds.map(e => {
                return {
                    subjectId: e,
                    zujuanId: exampaperId,
                }
            })
        };

        const token = await utils.getYjToken(data, req.user);
        let yjUrl = URL.format({
            protocol: YJSERVER.protocol,
            hostname: YJSERVER.hostname,
            pathname: '/v1/check/bind/zujuan',
            port: YJSERVER.port,
        });
        let yjDate = await axios.post(yjUrl, data, { headers: { token } }).then(utils.yjHandler);

        const retObj = yjExams.filter(e => {
            let curYjData = yjDate.find(item => item.subjectId === e.subject_id);
            return curYjData.zujuanBind;
        });
        return responseWrapper.succ(retObj);
    } catch (err) {
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

function get_exampaper_by_id(exampaper_id, callback) {
    let query = {
        _id: new ObjectID(exampaper_id)
    };
    let options = {};
    collection.findOne(query, options, function (err, exam) {
        if (err) {
            logger.error(err);
            return callback('HANDLE_ERROR', null);
        }
        if (!exam) {
            db.collection('upload_exampaper').findOne(query, options, function (err, up_exam) {
                if (err) {
                    logger.error(err);
                    return callback('HANDLE_ERROR', null);
                }
                if (!up_exam) {
                    db_kb_zyk.collection('assemble').findOne(query, options, function (err, zyk_exam) {
                        if (err) {
                            logger.error(err);
                            return callback('HANDLE_ERROR', null);
                        }
                        if (!zyk_exam) {
                            db_tiku_open.collection('user_paper').findOne(query, options, function (err, open_exam) {
                                if (err) {
                                    logger.error(err);
                                    return callback('HANDLE_ERROR', null);
                                }
                                if (!open_exam) {
                                    return callback('NULL_ERROR', null);
                                }
                                extend_ques.extend_ques(open_exam, function (err) {
                                    if (err) {
                                        logger.error(err);
                                        return callback('HANDLE_ERROR', null);
                                    }
                                    return callback(null, open_exam);
                                });
                            })
                        } else {
                            extend_ques.extend_ques(zyk_exam, function (err) {
                                if (err) {
                                    logger.error(err);
                                    return callback('HANDLE_ERROR', null);
                                }
                                return callback(null, zyk_exam);
                            });
                        }
                    });
                } else {
                    extend_ques.extend_ques(up_exam, function (err) {
                        if (err) {
                            logger.error(err);
                            return callback('HANDLE_ERROR', null);
                        }
                        return callback(null, up_exam);
                    });
                }
            });
        } else {
            extend_ques.extend_ques(exam, function (err) {
                if (err) {
                    logger.error(err);
                    return callback('HANDLE_ERROR', null);
                }
                return callback(null, exam);
            });
        }
    });
}

async function get_exampaper_by_id_async(exampaper_id) {
    let query = {
        _id: new ObjectID(exampaper_id)
    };
    let from = 'zx';
    let paper = await db_tiku_open.collection(enums.OpenSchema.user_paper).findOne(query);
    if (!lodash.isEmpty(paper)) {
        let options = {
            $inc: {
                view_count: 1
            }
        }
        await db_tiku_open.collection(enums.OpenSchema.user_paper).updateOne({ _id: new ObjectID(exampaper_id) }, options);
    }
    if (lodash.isEmpty(paper)) {
        paper = await db_kb_zyk.collection('assemble').findOne(query);
        from = 'zyk';
        if (lodash.isEmpty(paper)) {
            paper = await db_kb_zyk.collection('exampapers').findOne(query);
        }
    }
    if (lodash.isEmpty(paper)) return null;
    await extend_ques.extend_ques_async(paper, from);
    // 处理试题题号和去除题内题号
    paper_utils.ques_process(paper);
    if (paper.hasOwnProperty('_id')) paper.id = paper._id.toString();
    return paper;
}



module.exports = {
    get_exampaper_from_tiku: get_exampaper_from_tiku,
    post_exampaper: post_exampaper,
    get_exampaper: get_exampaper,
    get_exampaper_async: get_exampaper_async,
    put_exampaper: put_exampaper,
    delete_exampaper: delete_exampaper,
    list_exampapers: list_exampapers,
    knowledges_exampaper: knowledges_exampaper,
    getQuestionNumByKnowledge: getQuestionNumByKnowledge,
    postExampaperToYj,
    postExampaperBindYj,
    getExampaperYjExamList,
    lastExampaper,
    get_exampaper_by_id,
    get_exampaper_by_id_async,
    algo_knowledges_exampaper,
};
