const kbApi = require('./kb_api');
const ResponseWrapper = require('../middlewares/response_wrapper');
const logger = require('../utils/logger');

const getExampaperCF = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let period = req.query.period;
        let subject = req.query.subject;
        let all = req.query.all === 'false' ? false : true;
        if (period === undefined || subject === undefined) {
            return responseWrapper.error('PARAMETERS_ERROR', 'period 和 subject为必传字段！');
        }
        let retval = await kbApi.getExampaperCategory(period, subject, all);
        return responseWrapper.succ(retval);
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR');
    }
};

module.exports = {
    getExampaperCF,
};