const mapGrade = function(data) {
    let mapObj = {
        '小升初专题': '六年级下',
        '中考专题': '九年级下',
        '高考专题': '高三下'
    };
    return mapObj[data] || data;
};

const mapYear = function(from, to) {
    let year = [from, to].filter(i => i);
    let l = year.length;
    if (l) {
        switch (l) {
            case 1:
                year = year.join('') + '年';
                break;
            case 2:
                if (Number(from) === Number(to)) {
                    year = from + '年';
                } else {
                    year = year.join('-') + '学年';
                }
                break;
        }
    } else {
        year = '';
    }
    return year;
};

const mapExampaperName = function(data) {
    let name = data.name;
    if (['mkp', 'drm_yuanpei', 'drm'].includes(data.from)) {
        name = [
            mapYear(data.from_year, data.to_year),
            data.province, 
            data.city && data.city.indexOf('全部') > -1 ? '' : data.city, 
            mapGrade(data.grade),
            data.subject, 
            data.type
        ].filter(i => i).join('');
    }
    return name;
};

module.exports = mapExampaperName;