/*
 * Desc: api key middleware
 * Author: guochanghui
 */

let config = require('config');
let qs = require('querystring');
let aes = require('../utils/aes');
let jwt = require('../utils/jwt');
const http = require('http');

let TIMEOUT = 1000;  // ms
const KBSERVCFG = config.get('KB_API_SERVER');

function getKBApiKey(userTag, server, callback){
    let query = {
        app_key: server.appKey,
        user_tag: userTag,
    };
    let path = '/kb_api/v2/api_key/?' + qs.stringify(query);
    let options = {
        method: 'GET',
        hostname: server.hostname,
        port: server.port,
        path: path,
    };
    let _req = http.request(options, function(_res){
        _res.setEncoding('utf8');
        let chunck = '';
        _res.on('data', (data) => chunck += data);
        _res.on('end', () => {
            if (_res.statusCode === 200){
                try {
                    let data = JSON.parse(chunck);
                    return callback(null, data);
                } catch (err){
                    return callback(err);
                }
            } else {
                return callback(new Error('get api key: failed'));
            }
        });
    });
    _req.on('error', function(err){
        callback(err);
    });
    _req.setTimeout(TIMEOUT, function(){
        _req.abort();
    });
    _req.end();
}

function apikey(app){
    return function(req, res, next){

		if (app === 'DMP'){
			req.apiKey = KBSERVCFG.appKey;
			return next();
		} else if (app === 'KB'){
            let cookie = config.get('TIKU_SERVER').apiKeyCookie;
            let cookieName = cookie.name;
            let cookieValue = req.cookies[cookieName];
            if (cookieValue){
                try {
                    let value = jwt.decode(aes.decript(cookieValue));
                    req.apiKey = KBSERVCFG.appKey;
                    return next();
                } catch (err) {
                    console.error(err);
                }
            }

            // not has valid cookie
            let userTag = `TIKU:KBAPIKEY:${req.user.id}`;
            let server = config.get(`${app}_API_SERVER`);
            getKBApiKey(userTag, server, function(err, data){
                if (err){
                    return next(err);
                }
                let apiKey = data.api_key;
                let duration = data.duration * 1000;
                let value = {
                    apiKey: apiKey,
                    exp: new Date(Date.now() + duration),
                };
                let cookieValue = aes.encript(jwt.encode(value));
                let cookieOptions = cookie.options;
                cookieOptions['maxAge'] = duration;
                res.cookie(cookieName, cookieValue, cookieOptions);
                // set apiKey
                req.apiKey = KBSERVCFG.appKey;
                return next();
            });
        } else if (app === 'KBOE'){
            let server = config.get(`${app}_API_SERVER`);
            req.apiKey = server.appKey;
            return next();
        } else {
            return next(new Error(`${app} not has api key`));
        }
    } 
}

module.exports = apikey;
