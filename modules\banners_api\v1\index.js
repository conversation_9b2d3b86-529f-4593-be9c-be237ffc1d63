let mongodber = require('../../utils/mongodber');
let ResponseWrapper = require('../../middlewares/response_wrapper');
let logger = require('../../utils/logger');
let enums = require('../../../bin/enum');
const _ = require('lodash');
let db = mongodber.use('tiku');
const utils = require('../../utils/utils');

const getList = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const nowDate = new Date();
        const cond = {
            valid_from: {
              $lte: nowDate,
            },
            valid_to: {
                $gte: nowDate
            },
            shelf_status: 2, // 已发布
            del_status: 0, // 未删除,
            platform: 'tiku'
        };
        let resObj = {
            total: 0,
            list: []
        };
        let list = await db.collection('banners').find(cond).sort({ sort_num: 1 }).toArray();
        if (!_.size(list)) {
            return responseWrapper.succ(resObj);
        }
        const { period, subject } =  req.query;
        const cookieUser = utils.getTikuSessionUser(req);
        list = list.filter(it => {
            if (_.size(it.limit_period)) { // 学段
                if (!period || !it.limit_period.includes(period)) return false;
            }
            if (_.size(it.limit_subject)) { // 科目
                if (!subject || !it.limit_subject.includes(subject)) return false;
            }
            if (_.size(it.limit_school)) { // 学校
                if (_.isEmpty(cookieUser)) return false;
                const sch = it.limit_school.find(e => e.id === cookieUser.schoolId);
                if (_.isEmpty(sch)) return false;
            }
            return true;
        })
        resObj.total = _.size(list);
        resObj.list = list.map(it => {
            const obj = {
                id: it._id.toString(),
                name: it.name,
                img_name: it.img_name, // 图片名称(utilbox上传)
                img_url: it.img_url, // 图片地址(utilbox上传)
                href: it.href, // 跳转链接
                target: it.target, // 跳转方式(_self：默认当前页面跳转, _blank：新窗口打开 )
                interval_time: it.interval_time || 0, // 间隔时间(秒)
                desc: it.desc || '', // 描述
                size: it.size || '',  // 尺寸，large: 大 | meduim：中 | small: 小
                platform: it.platform
            }
            return obj;
        });
        return responseWrapper.succ(resObj);
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

module.exports = {
    getList,
}
