/*
 * Desc: kb api transmiter
 * Author: guochanghui
 */
const qs = require('querystring');
const config = require('config');
const request = require('request');
const moment = require('moment');
const pathToRegexp = require('path-to-regexp');

const ResponseWrapper = require('../../middlewares/response_wrapper');
const rediser = require('../../utils/rediser');
const Logger = require('../../utils/logger');
let enums = require('../../../bin/enum');
const url = require('url');
const KBSERVER = config.get('KB_API_SERVER');
const mongodber = require('../../utils/mongodber');
const db = mongodber.use('tiku');
const db_open = mongodber.use('tiku_open');
const axios = require('axios');
const _ = require('underscore');
const lodash = require('lodash');
const util = require('util');
const Joi = require('@hapi/joi');

const extendQues = require('../../assemble_api/v1/extend_ques');
const extendExam = require('../../favorite_api/v1/extend_exampaper');
const assets = require('../../assets_api/v1/index');
const ObjectID = require('mongodb').ObjectID;
const mapExampaperName = require('../../utils/mapExampaperName.js');
let client = require('zipkin-middleware').client;
const utils = require('../../utils/utils');

const questionTemplate = require('../../assemble_api/v1/question_template');
const user_log_service = require('../../user_api/v1/user_log_service');
const user_vip_service = require('../../user_api/v1/user_vip_service');
const user_right_service = require('../../user_api/v2/user_right_service');
const kb_client = require('../../client').kb;
const user_download_service = require('../../user_api/v2/user_download_service');


const TIMEOUT = 5000;
const RETRY_TIMES = 1;
const ATTACH = {
    '新高考题': 'is_new_gaokao',
    '母题': 'is_mom',
    '经典题': 'is_classic',
    '名优校题': 'is_famous'
}

/**
 * 记录推荐数据日志
 * @param {Object} logData - 要记录的日志数据
 * @param {string} logData.user_id - 用户ID
 * @param {string} logData.type - 日志类型 (question_search/exampaper_search)
 * @param {Object} logData.request - 请求信息 {path, query, body}
 * @param {Object} logData.response - 响应信息 {status_code, total_num, result_count, result_ids, error_info}
 */
const recordRecoDataLog = async (logData) => {
    try {
        const data = {
            ...logData,
            ctime: new Date(),
            utime: new Date(),
        };
        await db.collection('reco_data_log').insertOne(data);
    } catch (err) {
        Logger.error(`记录推荐数据日志失败: ${err.stack}, ${JSON.stringify(logData)}`);
    }
}

/**
 * 递归替换对象中所有key的点号为下划线
 * @param {*} obj - 需要处理的对象或数组
 * @returns {*} 处理后的对象或数组
 */
const replaceDotInKeys = (obj) => {
    if (obj === null || obj === undefined) {
        return obj;
    }
    
    if (Array.isArray(obj)) {
        return obj.map(item => replaceDotInKeys(item));
    }
    
    if (typeof obj === 'object' && obj.constructor === Object) {
        const newObj = {};
        for (const [key, value] of Object.entries(obj)) {
            // 将key中的点号替换为下划线
            const newKey = key.replace(/\./g, '_');
            newObj[newKey] = replaceDotInKeys(value);
        }
        return newObj;
    }
    
    return obj;
}

/**
 * 构建推荐数据日志对象
 * @param {Object} req - Express请求对象
 * @param {string} type - 日志类型
 * @param {number} statusCode - HTTP状态码
 * @param {Object} data - 响应数据
 * @returns {Object} 日志数据对象
 */
const buildRecoLogData = (req, type, statusCode, data = {}) => {
    const logData = {
        user_id: req.user ? req.user.id : null,
        type: type,
        request: {
            path: req.path,
            query: req.query,
            body: req.body
        },
        response: {
            status_code: statusCode,
            total_num: 0,
            result_count: 0,
            result_data: null,
            error_info: null
        }
    };

    if (statusCode === 200) {
        // 成功响应，根据类型处理数据
        if (type === enums.RecoDataLogType.QUESTION_SEARCH) {
            // 题目搜索：存储去除 blocks 字段的题目数据
            const questions = (data.questions || []).map(q => {
                const questionCopy = { ...q };
                delete questionCopy.blocks;
                return questionCopy;
            });
            logData.response.total_num = data.total_num || 0;
            logData.response.result_count = questions.length;
            logData.response.result_data = questions;
        } else if (type === enums.RecoDataLogType.EXAMPAPER_SEARCH) {
            // 试卷搜索：直接存储完整的 ret 数据
            logData.response.total_num = data.total_num || 0;
            logData.response.result_count = (data.exampapers || []).length;
            logData.response.result_data = data;
        }
    } else {
        // 错误响应
        logData.response.error_info = {
            status_code: statusCode,
            error_code: data.code || null,
            error_msg: data.msg || data.error || (statusCode >= 500 ? '服务器内部错误' : '客户端错误')
        };
    }

    // 处理整个logData对象，将所有key中的点号替换为下划线
    return replaceDotInKeys(logData);
}

function gen_key(value) {
    return 'kb:docx:' + value;
}

const getExampaperById = async (exampaper_id) => {
    var uri = url.format({
        protocol: config.get('KB_API_SERVER').protocol,
        hostname: config.get('KB_API_SERVER').hostname,
        port: config.get('KB_API_SERVER').port,
        pathname: `/kb_api/v2/exampapers/${exampaper_id}`,
        search: qs.stringify({
            api_key: config.get('KB_API_SERVER').appKey,
            download: 'true'
        })
    });
    let response = await axios.get(uri);
    return response.data;
}

const inscreaceDownTimes = (resource_ids, resource_type, album_id) => {
    if (['question', 'exampaper'].indexOf(resource_type) === -1) return;
    if (resource_ids && resource_type) {
        let uri = url.format({
            protocol: config.get('KB_API_SERVER').protocol,
            hostname: config.get('KB_API_SERVER').hostname,
            port: config.get('KB_API_SERVER').port,
            pathname: '/kb_api/v2/resources/downloads',
            search: qs.stringify({
                api_key: config.get('KB_API_SERVER').appKey
            })
        });
        axios.put(uri, {
            resource_ids: resource_ids,
            resource_type: resource_type,
            album_id: album_id
        }).catch(err => {
            Logger.error(err.message);
        });
    }
}

const getQuestionsByIds = async (question_ids) => {
    var uri = url.format({
        protocol: config.get('KB_API_SERVER').protocol,
        hostname: config.get('KB_API_SERVER').hostname,
        port: config.get('KB_API_SERVER').port,
        pathname: `/kb_api/v2/questions/`,
        search: qs.stringify({
            api_key: config.get('KB_API_SERVER').appKey,
            download: 'true'
        })
    });
    let response = await axios.post(uri, {
        question_ids: question_ids,
        fields_type: 'full'
    });
    return response.data;
}

const createMemoryDocx = async (response) => {
    try {
        let cd = response.headers['content-disposition'] ? response.headers['content-disposition'].split('=') : '';
        let filename = (cd.length >= 2) ? cd[1] : (new Date()).getTime() + '.docx'; //已经编码后安全字符
        var key = gen_key(decodeURIComponent(filename));
        var value = response.data.toString('base64');
        await rediser.set(key, value, 120);
        var serv = config.get('TIKU_SERVER');
        return serv.webUrl + '/kb_api/v2/docx/' + filename;
    } catch (err) {
        throw err;
    }
}

const queryResourcesByType = async (ids, type) => {
    try {
        let typeHandle = {
            question: util.promisify(extendQues.get_kb_questions),
            exampaper: extendExam.getExampapersByIds,
            assemble_exampaper: queryAssembleExampaperById,
        };
        if (!typeHandle[type]) {
            throw new Error(type + '类型的统计数据未定义');
        }
        if (!ids.length) {
            return [];
        }
        let _body = await typeHandle[type](ids);
        return _body;
    } catch (err) {
        console.error(err.stack);
    }

}

const queryAssembleExampaperById = async (ids) => {
    const data = await db_open.collection(enums.OpenSchema.user_paper).find({ _id: { $in: ids.map(e => new ObjectID(e)) } }).toArray();
    if (lodash.size(data)) {
        for (const d of data) {
            d.id = d._id.toString();
        }
    }
    return data;
}

const incDownTimes = async (user, ids, type) => {
    if (['question', 'exampaper', 'assemble_exampaper'].indexOf(type) === -1 || !ids.length) {
        return;
    }
    let col = db.collection('user_download');

    let infos = await queryResourcesByType(ids, type);
    let newArr = _.map(infos, (info) => {
        const saveKeys = ['period', 'subject', 'type', 'grade', 'press_version'];
        let tem = {
            user_id: user.id,
            sch_id: user.schoolId,
            resource_type: type,
            download_times: 1,
            ctime: new Date(),
            utime: new Date()
        };
        if (type === enums.ResourceType.EXAMPAPER) {
            tem.category = 1;
        } else if (type === enums.ResourceType.ASSEMBLE_EXAMPAPER) {
            tem.resource_type = enums.ResourceType.EXAMPAPER;
            tem.category = 2;
        }
        let t = _.clone(tem);
        _.each(saveKeys, (key) => {
            if (info[key]) {
                t[key] = info[key];
            }
        });
        t.resource_id = info.id;
        return t;
    });
    await col.insertMany(newArr);
}

const assembleExampaper = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    const isVip = req.user.isVip;
    if (!isVip) {
        return responseWrapper.error('NEED_VIP_ERROR', '需要开通会员');
    }

    try {
        const instance = axios.create({
            baseURL: `${config.get('UTIL_SERV').protocol}://${config.get('UTIL_SERV').hostname}:${config.get('UTIL_SERV').port}`,
            timeout: 60000,
            responseType: 'arraybuffer'
        });
        //记录组卷次数
        inscreaceDownTimes(req.body.resource_ids, req.body.resource_type, req.body.album_id);
        await incDownTimes(req.user, req.body.resource_ids, req.body.resource_type);
        if (req.body.resource_type === 'assemble' &&
            req.body.resource_ids.length === 1 &&
            isNaN(Number(req.body.resource_ids[0]))) {
            let cond = {
                _id: new ObjectID(req.body.resource_ids[0])
            };
            let options = {
                $set: {
                    download_time: new Date()
                },
                $inc: {
                    exampaper_download_num: 1
                }
            }
            let exampaperInfo = await db.collection('exampaper').findOne(cond);
            if (!exampaperInfo) {
                throw new Error('没有数据');
            }
            if (req.user.vipType === enums.MemberType.TIKU_PERSONAL && ((exampaperInfo.expired_time || 0) < new Date())) {
                await assets.deductionTicketItem(1, req.user.userId, req.user.ucId, enums.ResourceType.ASSEMBLE_EXAMPAPER, [req.body.resource_ids[0]]);
                await db.collection('user').updateOne({ _id: req.user.userId }, {
                    $set: { use_time: new Date() },
                    $inc: { assemble_download_num: 1 }
                });
                await db.collection('exampaper').update(cond, { $set: { expired_time: moment().add(1, 'month').startOf('day').toDate() } });
            }
            if (Object.values(enums.SchoolVipType).includes(req.user.vipType) && ((exampaperInfo.expired_time || 0) < new Date())) {
                const userInfo = await db.collection('user').findOne({ _id: req.user.userId });
                const schoolInfo = await db.collection('school_info').findOne({ _id: req.user.schoolId });
                if ((schoolInfo && schoolInfo.assemble_download_num || 0) <= (userInfo && userInfo.assemble_download_num || 0)) {
                    return responseWrapper.error('OVER_LIMIT_ERROR', '您的账号已达本月数量上限');
                }
                await db.collection('user').updateOne({ _id: req.user.userId }, {
                    $set: { use_time: new Date() },
                    $inc: { assemble_download_num: 1 }
                });
                await db.collection('exampaper').update(cond, { $set: { expired_time: moment().add(1, 'month').startOf('day').toDate() } });
            }
            db.collection('exampaper').update(cond, options);
        }

        let response = await instance.post(`/utilbox_api/v1/paper/download?api_key=${config.get('UTIL_SERV').appKey}`, {
            typeset: true,
            math: false,
            ...req.body,
            html: req.body.content,
            name: req.body.filename,
            source_plat: 'tiku'
        });

        let retval = await createMemoryDocx(response);
        return responseWrapper.succ({
            'url': retval
        });
    } catch (err) {
        Logger.error(err.stack);
        Logger.error(err.message);
        if (err.message === '您的账号已达本月数量上限') return responseWrapper.error('OVER_LIMIT_ERROR', err.message);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
}


const getDownloadInfo = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);

    try {
        const user = await user_vip_service.get_vip_user(req.user.id);
        let body = req.body || {};
        const { resource_type, resource_ids, parent_id } = body;

        if (!resource_type || !resource_ids) {
            return responseWrapper.error('PARAM_ERROR', '参数错误');
        }

        let data;
        let isAssemble = false;
        let type = enums.ResourceType.QUESTION;
        let download_type = enums.NewRightType.ques_download_num;
        if (resource_type === 'question' && resource_ids.length === 1 && !isNaN(Number(resource_ids[0]))) {  // 试题类型
            let questionIds = resource_ids.map(item => +item);
            data = await getQuestionsByIds(questionIds);
            if (data) {
                data = data[0];
            }
        } else if (resource_type === 'exampaper' && resource_ids.length === 1 && !isNaN(Number(resource_ids[0]))) {  // 试卷（组卷）类型
            data = await getExampaperInfoById(+resource_ids[0])
            type = enums.ResourceType.EXAMPAPER;
            download_type = enums.NewRightType.exampaper_download_num;
        } else if (resource_type === 'assemble' && resource_ids.length === 1 && isNaN(Number(resource_ids[0]))) {  // 组卷

            data = await db_open.collection(enums.OpenSchema.user_paper).findOne({ _id: new ObjectID(resource_ids[0]) });
            if (!_.isEmpty(data)) {
                data.id = data._id.toString();
                delete data._id;
            }
            await extendQues.extend_ques_async(data);
            isAssemble = true;
            type = enums.ResourceType.ASSEMBLE_EXAMPAPER;
            download_type = enums.NewRightType.assemble_download_num;
        } else if (resource_type === enums.ResourceType.RESOURCE_ALBUM) {
            type = enums.ResourceType.RESOURCE_ALBUM;
            download_type = enums.NewRightType.exampaper_download_num;
            data = await getAlbumInfo(resource_ids[0]);
        }

        if (!data) {
            return responseWrapper.error('RESOURCE_NOT_EXIST_ERROR', '资源不存在');
        }
        const fun_params = {
            type: enums.NewRightType.resource_download_fun,
            period: data.period,
            subject: data.subject,
        }
        let album = null;
        if (resource_type === enums.ResourceType.RESOURCE_ALBUM) {
            album = await db.collection('resource_album').findOne({ _id: new ObjectID(parent_id) });
            fun_params.period = album.period;
            fun_params.subject = album.subject;
            fun_params.exclude = [enums.NewVipType.personal_plus]; // plus 会员不能下载
        }
        const fun_status = await user_right_service.get_fun_right_status(req.user.id, fun_params);
        if (!fun_status) return responseWrapper.error('NEED_VIP_ERROR', '开通会员后使用');
        const target = {
            id: data.id,
            type: type
        };
        const right_params = _.assign({}, fun_params, { type: download_type }, { target });
        const right = await user_right_service.get_use_right(req.user.id, right_params);
        if (!_.isEmpty(right) && (right.group === enums.NewVipType.school || right.group === enums.NewVipType.school_plus)) {
            const school_download_status = await user_download_service.check_school_download_limit(user.schoolId);
            if (!school_download_status) {
                return responseWrapper.error('TIMES_NOT_ENOUGH_ERROR', `学校每日最多可下载1000份资源`);
            }
        }
        const right_status = await user_right_service.use_right(req.user.id, right_params, right);
        if (!right_status) return responseWrapper.error('OVER_LIMIT_ERROR', '下载次数不足');
        //记录组卷次数
        inscreaceDownTimes(req.body.resource_ids, req.body.resource_type, req.body.album_id);
        await incDownTimes(user, req.body.resource_ids, type);
        if (isAssemble) {
            let cond = {
                _id: new ObjectID(req.body.resource_ids[0])
            };
            let options = {
                $set: {
                    download_time: new Date()
                },
                $inc: {
                    exampaper_download_num: 1
                }
            }
            await db_open.collection(enums.OpenSchema.user_paper).updateOne(cond, options);
        }
        if (resource_type === enums.ResourceType.RESOURCE_ALBUM) {
            // 增加下载次数
            const ds = { $set: { utime: new Date() }, $inc: { download_times: 1 } };
            await db.collection('resource_album').updateOne({ _id: new ObjectID(parent_id) }, ds);
            await db.collection('resource_album_data').updateOne({ _id: new ObjectID(resource_ids[0]) }, ds);
            // 增加下载记录
            const album_index = findRootIndex(album, resource_ids[0]);
            const downloadDs = {
                user_id: user.id,
                sch_id: user.schoolId,
                period: album.period || '',
                grade: album.grade || '',
                subject: album.subject || '',
                resource_id: resource_ids[0],
                resource_name: data.name,
                resource_type: enums.ResourceType.RESOURCE_ALBUM,
                resource_info: {
                    album_name: album.name,
                    album_id: parent_id,
                    album_index: album_index,
                },
                download_times: 1,
                ctime: new Date(),
                utime: new Date()
            }
            await db.collection('user_download').insertOne(downloadDs);
        }
        await user_log_service.download(req.user, resource_type === 'assemble' ? 'assemble_exampaper' : resource_type, resource_ids[0]);
        return responseWrapper.succ(data);
    } catch (err) {
        Logger.error(err.stack);
        Logger.error(err.message);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
}

const getDownload = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        const instance = axios.create({
            baseURL: `${config.get('UTIL_SERV').protocol}://${config.get('UTIL_SERV').hostname}:${config.get('UTIL_SERV').port}`,
            timeout: 60000,
            responseType: 'arraybuffer'
        });

        let response = await instance.post(`/utilbox_api/v1/paper/download?api_key=${config.get('UTIL_SERV').appKey}`, {
            typeset: true,
            math: false,
            size: req.body.size,
            seal: req.body.seal,
            html: req.body.content,
            name: req.body.filename,
            source_plat: 'tiku'
        });

        let retval = await createMemoryDocx(response);
        return responseWrapper.succ({
            'url': retval
        });
    } catch (err) {
        Logger.error(err.stack);
        Logger.error(err.message);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
}


const downloadDocument = async (data, fieldsType) => {
    try {
        const instance = axios.create({
            baseURL: `${config.get('UTIL_SERV').protocol}://${config.get('UTIL_SERV').hostname}:${config.get('UTIL_SERV').port}`,
            timeout: 60000,
            responseType: 'arraybuffer'
        });
        let uri = '/utilbox_api/v1/exampapers/documents?type=json';
        if (fieldsType === 'full') {
            uri = [uri, 'fields_type=full'].join('&');
        }
        let response = await instance.post(uri, data);
        let retval = await createMemoryDocx(response);
        return retval;
    } catch (err) {
        throw err;
    }
}

const downloadExampaper = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        // TODO
        if (req.user.role === '教师' || req.isVip === false) throw new Error('非会员不可下载');

        let exampaperId = Number(req.params.exampaper_id);
        let data = await getExampaperById(exampaperId);
        let retval = await downloadDocument(data, req.query.fields_type);
        return responseWrapper.succ({
            'url': retval
        });
    } catch (err) {
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
}

const downloadQuestion = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        // TODO
        if (req.user.role === '教师' || req.isVip === false) throw new Error('非会员不可下载');

        let question_ids = _.map(req.params.question_id.split(','), (x) => {
            return Number(x);
        });
        let data = await getQuestionsByIds(question_ids);
        let retval = await downloadDocument({
            name: String(Date.now()),
            blocks: [{
                title: '',
                questions: data
            }]
        }, req.query.fields_type);
        return responseWrapper.succ({
            'url': retval
        });
    } catch (err) {
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
}


function downloadDocx(req, res) {
    var resWrapper = new ResponseWrapper(req, res);
    var filename = req.params.filename;
    var key = gen_key(decodeURIComponent(filename));
    filename = encodeURIComponent(filename);
    rediser.get(key, function (err, docx) {
        if (err || docx == null) {
            if (err) {
                console.error(err.message)
            }
            return resWrapper.error('HANDLE_ERROR');
        }
        docx = Buffer.from(docx, 'base64');
        res.set({
            'Content-Type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'Content-Disposition': 'attachment; filename=' + filename,
        });
        return res.status(200).send(docx);
    });
}

const getExampaperInfoById = async exampaperId => {
    try {
        if (!exampaperId) {
            Logger.error('parameters error: ' + exampaperId);
            return
        }
        let query = {
            api_key: KBSERVER.appKey
        }

        let uri = url.format({
            protocol: KBSERVER.protocol,
            hostname: KBSERVER.hostname,
            port: KBSERVER.port,
            pathname: `/kb_api/v2/exampapers/${exampaperId}`,
            search: qs.stringify(query)
        });

        let response = await axios.get(uri);

        return response.data;
    } catch (err) {
        Logger.error(err);
        throw new Error(err);
    }
};

const getExampaperInfo = (req, res) => {
    let resWrap = new ResponseWrapper(req, res);
    try {
        if (!req.params.exampaper_id) {
            Logger.error('parameters error: ' + req.params.exampaper_id);
            return resWrap.error('PARAMETERS_ERROR');
        }

        let query = req.query;
        query['api_key'] = KBSERVER.appKey;
        if (req.params.exampaper_id === 'profile') {
            delete query.freq;
        }

        let uri = url.format({
            protocol: KBSERVER.protocol,
            hostname: KBSERVER.hostname,
            port: KBSERVER.port,
            pathname: `/kb_api/v2/exampapers/${req.params.exampaper_id}`,
            search: qs.stringify(query)
        });

        request({
            url: uri
        }, function (err, response, body) {
            try {
                if (err) {
                    Logger.error(err);
                    return resWrap.error('HANDLE_ERROR');
                }

                body = JSON.parse(body);
                //body.name = mapExampaperName(body);
                if (response.statusCode === 200) {
                    if (res.freq !== '' && res.freq !== undefined) {
                        return resWrap.send({
                            code: 6,
                            msg: res.freq,
                            data: body
                        });
                    }

                    let blocks = body.blocks || [];
                    blocks.forEach(block => {
                        (block.questions || []).forEach(question => {
                            question.blocks = {
                                types: question.blocks.types,
                                stems: question.blocks.stems,
                                knowledges: question.blocks.knowledges,
                            }
                        })
                    });
                    return resWrap.succ(body);
                } else if (body.data !== undefined) {
                    return resWrap.send(body);
                }
                return resWrap.error('HANDLE_ERROR');
            } catch (err) {
                Logger.error(err);
                return resWrap.error('HANDLE_ERROR');
            }
        });
    } catch (err) {
        Logger.error(err);
        return resWrap.error('HANDLE_ERROR');
    }
};


function findRootIndex(album, id) {
    const fun = (children) => {
        if (!_.size(children)) return false;
        for (const data of children) {
            if (data.id === id) {
                return true;
            }
            fun(children.children);
        }
    };
    let result = -1;
    for (const index in album.children) {
        const data = album.children[index];
        if (fun(data.children)) {
            result = index;
        }
    }
    return result;
}

/**
 * 获取专辑数据
 * @param albumDataId
 * @returns {Promise<void>}
 */
const getAlbumInfo = async (albumDataId) => {
    const data = await db.collection('resource_album_data').findOne({ _id: new ObjectID(albumDataId) });
    if (!data) return null;
    data.id = data._id.toString();
    data.ctime = data.ctime.getTime();
    data.utime = data.utime.getTime();
    delete data._id;
    delete data.valid;
    // 系统资源统一加载
    await buildSystemResource(data);
    return data;

}

async function buildSystemResource(data) {
    const id_map = {
        knowledge_ids: [],
        question_ids: [],
        exampaper_ids: [],
        edu_file_ids: [],
        edu_tool_ids: [],
        text_question_ids: []
    };

    getIds = (children) => {
        if (!_.size(children)) return;
        for (const chapter of children) {
            if (chapter.type === enums.AlbumResourceType.KNOWLEDGE) {
                for (const content of chapter.content || []) {
                    if (content.id) id_map.knowledge_ids.push(content.id);
                }
            } else if (chapter.type === enums.AlbumResourceType.QUESTION) {
                for (const content of chapter.content || []) {
                    if (content.id) id_map.question_ids.push(content.id);
                }
            } else if (chapter.type === enums.AlbumResourceType.EXAMPAPER) {
                for (const content of chapter.content || []) {
                    if (content.id) id_map.exampaper_ids.push(content.id);
                }
            } else if (chapter.type === enums.AlbumResourceType.EDU_FILE) {
                for (const content of chapter.content || []) {
                    if (content.id) id_map.edu_file_ids.push(content.id);
                }
            } else if (chapter.type === enums.AlbumResourceType.EDU_TOOL) {
                for (const content of chapter.content || []) {
                    if (content.id) id_map.edu_tool_ids.push(content.id);
                }
            } else if (chapter.type === enums.AlbumResourceType.TEXT_QUESTION) {
                for (const content of chapter.content || []) {
                    if (content.id) id_map.text_question_ids.push(content.id);
                }
            }
            getIds(chapter.children);
        }
    };
    getIds(data.children);
    // 获取系统资源
    const value_map = {};
    if (_.size(id_map.knowledge_ids)) {
        value_map.knowledges = await kb_client.getKnowledgeByIds(id_map.knowledge_ids);
    }
    if (_.size(id_map.question_ids)) {
        value_map.questions = await kb_client.getQuestions(id_map.question_ids);
    }
    if (_.size(id_map.exampaper_ids)) {
        value_map.exampapers = await kb_client.getExampapers(id_map.exampaper_ids);
    }
    if (_.size(id_map.edu_file_ids)) {
        value_map.edu_files = await kb_client.getEduFileByIds(id_map.edu_file_ids);
        for (const data of value_map.edu_files) {
            delete data.url;
            delete data.host;
        }
    }
    if (_.size(id_map.edu_tool_ids)) {
        value_map.edu_tools = await kb_client.getEduToolByIds(id_map.edu_tool_ids);
        for (const data of value_map.edu_tools) {
            delete data.url;
            delete data.host;
        }
    }
    if (_.size(id_map.text_question_ids)) {
        value_map.text_questions = await kb_client.getQuestions(id_map.text_question_ids);
    }
    const handler = (children) => {
        if (!_.size(children)) return;
        for (const chapter of children) {
            if (chapter.type === enums.AlbumResourceType.KNOWLEDGE) {
                for (const content of chapter.content || []) {
                    if (content.id) content.value = (value_map.knowledges || []).find(e => e.id === content.id);
                }
            } else if (chapter.type === enums.AlbumResourceType.QUESTION) {
                for (const content of chapter.content || []) {
                    if (content.id) content.value = (value_map.questions || []).find(e => e.id === content.id);
                }
            } else if (chapter.type === enums.AlbumResourceType.EXAMPAPER) {
                for (const content of chapter.content || []) {
                    if (content.id) content.value = (value_map.exampapers || []).find(e => e.id === content.id);
                }
            } else if (chapter.type === enums.AlbumResourceType.EDU_FILE) {
                for (const content of chapter.content || []) {
                    if (content.id) content.value = (value_map.edu_files || []).find(e => e.id === content.id);
                }
            } else if (chapter.type === enums.AlbumResourceType.EDU_TOOL) {
                for (const content of chapter.content || []) {
                    if (content.id) content.value = (value_map.edu_tools || []).find(e => e.id === content.id);
                }
            } else if (chapter.type === enums.AlbumResourceType.TEXT_QUESTION) {
                for (const content of chapter.content || []) {
                    if (content.id) content.value = (value_map.text_questions || []).find(e => e.id === content.id);
                }
            }
            handler(chapter.children);
        }
    }
    handler(data.children);
}

const addAttach = (res, questionTemplateList) => {
    for (let info in res) {
        let subjects = res[info].subjects
        for (let subject_info in subjects) {
            let subjectInfo = subjects[subject_info];
            subjectInfo.category = ['常考题', '经典题', '压轴题', '精品题', '名优校题']
            subjectInfo.question_template = questionTemplateList.filter(item => item.period === res[info].period && item.subject === subjectInfo.subject);
        }
    }
    return res
}

const questionsFilters = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    // transmit
    let query = req.query;
    if (!query.api_key) {  // trick, for exampaper answer
        query['api_key'] = req.apiKey;
    }
    let path = `/kb_api/v2${req.path}`;
    let timeout = TIMEOUT;
    let server = config.get('KB_API_SERVER');
    let options = {
        uri: url.format({
            protocol: server.protocol,
            hostname: server.hostname,
            port: server.port,
            pathname: path,
            search: qs.stringify(query),
        }),
        method: req.method,
        timeout: timeout,
        retryTimes: RETRY_TIMES,
    };
    let body = JSON.stringify(req.body);
    if (body != '{}') {
        options['headers'] = {
            'content-type': 'application/json',
            'content-length': Buffer.byteLength(body),
        };
        options['body'] = body;
    }
    const questionTemplateList = await questionTemplate.getSysList();
    client.request(options, function (err, response, body) {
        if (err) {
            return responseWrapper.error('HANDLE_ERROR', err.message);
        }
        if (response.statusCode == 200) {
            let ret = JSON.parse(body);

            // 处理 试题类型
            for (let one_ret of ret) {
                for (let one_subject of one_ret.subjects || []) {
                    let tmp = {};
                    for (let one_type of one_subject.type || []) {
                        if (one_type.includes('_')) {
                            let [father_type, child_type] = one_type.split('_');
                            if (father_type in tmp) {
                                if (tmp[father_type].childs.length === 0) {
                                    tmp[father_type].childs.push('全部');
                                }
                                tmp[father_type].childs.push(child_type);
                            } else {
                                tmp[father_type] = {
                                    name: father_type,
                                    childs: ['全部', child_type]
                                }
                            }
                        } else {
                            if (!(one_type in tmp)) {
                                tmp[one_type] = {
                                    name: one_type,
                                    childs: [],
                                }
                            }
                        }
                    }
                    one_subject.type = Object.values(tmp);
                }
            }
            ret = addAttach(ret, questionTemplateList)
            if (ret.code === 6) {
                if (res.freq != '') {
                    ret.msg = res.freq;
                }
                return responseWrapper.send(ret);
            }
            return responseWrapper.succ(ret);
        } else if (response.statusCode >= 400 && response.statusCode < 500) {
            let ret = JSON.parse(body);
            if (ret.code === 6 && res.freq != '') {
                ret.msg = res.freq;
            }
            return responseWrapper.send(ret);
        } else if (response.statusCode >= 500) {
            return responseWrapper.error('HANDLE_ERROR');
        }
    });
}


const fixBodyAttach = (body) => {
    body_obj = JSON.parse(body)
    body_obj.attach = [ATTACH[body_obj.attach]]
    return JSON.stringify(body_obj)
}

const questionBySearch = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);

    // set specitial timeout
    let timeout = TIMEOUT;

    // transmit
    let query = req.query;
    if (!query.api_key) {  // trick, for exampaper answer
        query['api_key'] = req.apiKey;
    }
    let path = `/kb_api/v2${req.path}`; // ori path

    let server = config.get('KB_API_SERVER');
    let options = {
        uri: url.format({
            protocol: server.protocol,
            hostname: server.hostname,
            port: server.port,
            pathname: path,
            search: qs.stringify(query),
        }),
        method: req.method,
        timeout: timeout,
        retryTimes: RETRY_TIMES,
    };
    // 过滤mkp 题目、卷子
    req.body.filter_mkp = 'true';
    let offset_value = req.body.offset || 0;
    let limit_value = req.body.limit || 10;
    // if ((offset_value + limit_value) <= 500) {
    //     req.body['offset'] = 0;
    //     req.body['limit'] = 500;
    // }
    let sort_by = req.body.sort_by || '';
    let user_period = req.body.period;
    let user_province = '';
    if (req.body.provinces) {
        user_province = req.body.provinces && req.body.provinces[0] && req.body.provinces[0].name;
    }

    // if (req.body.year) {
    //
    // }
    // 试卷添加年份查询，其中有更早标签，取2015年之后，距今5年之外的试题
    if (req.body.year && req.body.year === 'other') {
        let year = new Date().getFullYear();
        let temp = [];
        for (let i = 2015; i <= year - 5; i++) {
            temp.push(i);
        };
        req.body.year = temp.join(',');
    }

    let body = JSON.stringify(req.body);


    if (body != '{}') {
        options['headers'] = {
            'content-type': 'application/json',
            'content-length': Buffer.byteLength(body),
        };
        options['body'] = body;
    }

    // 使用 axios 发送请求
    try {
        const response = await axios({
            url: options.uri,
            method: options.method,
            headers: options.headers,
            data: options.body,
            timeout: options.timeout
        });

        if (response.status === 200) {
            let ret = response.data;
            // 排序
            // if (sort_by !== 'year') {
            //     if ((offset_value + limit_value) <= 500) {
            //         ret.questions = sortQuestions({ user_period, user_province, sort_by, offset_value, limit_value, questions: ret.questions });
            //     }
            // }
            // 提取标签
            req.body.province = req.body.province || user_province;
            addShowTags(ret.questions, req.body);

            // 记录成功的搜索日志，等待完成
            await recordRecoDataLog(buildRecoLogData(req, enums.RecoDataLogType.QUESTION_SEARCH, 200, ret));

            if (ret.code === 6) {
                if (res.freq != '') {
                    ret.msg = res.freq;
                }
                return responseWrapper.send(ret);
            }
            return responseWrapper.succ(ret);
        }
    } catch (err) {
        if (err.response) {
            // 服务器响应了错误状态码
            const response = err.response;
            if (response.status >= 400 && response.status < 500) {
                let ret = response.data;
                if (ret.code === 6 && res.freq != '') {
                    ret.msg = res.freq;
                }
                // 记录4xx错误的日志（客户端错误），等待完成
                await recordRecoDataLog(buildRecoLogData(req, enums.RecoDataLogType.QUESTION_SEARCH, response.status, ret));
                return responseWrapper.send(ret);
            } else if (response.status >= 500) {
                // 记录5xx错误的日志（服务器错误），等待完成
                await recordRecoDataLog(buildRecoLogData(req, enums.RecoDataLogType.QUESTION_SEARCH, response.status));
                return responseWrapper.error('HANDLE_ERROR');
            }
        } else {
            // 网络错误或其他错误
            return responseWrapper.error('HANDLE_ERROR', err.message);
        }
    }
}

const exampaperBySearch = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    // set specitial timeout
    let timeout = TIMEOUT;
    // transmit
    let query = req.query;
    if (!query.api_key) {  // trick, for exampaper answer
        query['api_key'] = req.apiKey;
    }
    let path = `/kb_api/v2${req.path}`; // ori path
    let server = config.get('KB_API_SERVER');
    let options = {
        uri: url.format({
            protocol: server.protocol,
            hostname: server.hostname,
            port: server.port,
            pathname: path,
            search: qs.stringify(query),
        }),
        method: req.method,
        timeout: timeout,
        retryTimes: RETRY_TIMES,
    };
    // 过滤mkp 题目、卷子
    req.body.filter_mkp = 'true';
    req.body.need_ques_num = 'true';
    if (req.body.year) {
        req.body.to_year = req.body.year;
    }
    // 试卷添加年份查询，其中有更早标签，取2015年之后，距今5年之外的试卷
    if (req.body.to_year && req.body.to_year === 'other') {
        let year = new Date().getFullYear();
        let temp = [];
        for (let i = 2015; i <= year - 5; i++) {
            temp.push(i);
        };
        req.body.to_year = temp.join(',');
    }
    const limit = req.body.limit || 10;
    const offset = req.body.offset || 0;
    if (req.body.page === 'textbook') { // 同步练习获取全部
        req.body.sort = 'year'; // 综合排序
        req.body.limit = 9999;
        req.body.offset = 0;
    }


    let body = JSON.stringify(req.body);
    if (body != '{}') {
        options['headers'] = {
            'content-type': 'application/json',
            'content-length': Buffer.byteLength(body),
        };
        options['body'] = body;
    }
    // 使用 axios 发送请求
    try {
        const response = await axios({
            url: options.uri,
            method: options.method,
            headers: options.headers,
            data: options.body,
            timeout: options.timeout
        });

        if (response.status === 200) {
            let ret = response.data;
            if (ret.code === 6) {
                if (res.freq != '') {
                    ret.msg = res.freq;
                }
                return responseWrapper.send(ret);
            }
            if (req.body.page === 'textbook') { // 同步练习获取全部
                let { total_num, exampapers } = ret;
                let teacher_names = ['刘建雪', '陈晓慧', '隋晓妍', '莉莉', '孙敏', '花瑞琴', '李光英', '杨乃仟', '刘帅'];
                exampapers = exampapers.filter(e => {
                    if (e.city === '安阳' && e.hasOwnProperty('user_name') && teacher_names.includes(e.user_name)) return false;
                    return true;
                });
                // 排序
                const sort_map = {
                    '高频题集': 4,
                    '易错题集二': 5,
                    '易错题集一': 6,
                    '课后作业二': 7,
                    '课后作业一': 8,
                    '课堂练习二': 9,
                    '课堂练习一': 10,
                }
                if (lodash.size(exampapers)) {
                    for (const paper of exampapers) {
                        paper.score = 1; // 默认给个值
                        for (const key in sort_map) {
                            if (paper.name.includes(key)) {
                                paper.score = sort_map[key];
                            }
                        }
                    }
                    exampapers = lodash.chain(exampapers).sortBy('to_year', 'score').reverse().value();
                    for (const paper of exampapers) {
                        delete paper.score;
                    }
                    total_num = lodash.size(exampapers);
                    exampapers = exampapers.splice(offset, offset + limit);
                } else {
                    total_num = 0;
                    exampapers = [];
                }
                // 记录同步练习搜索的日志（内存分页），等待完成
                await recordRecoDataLog(buildRecoLogData(req, enums.RecoDataLogType.EXAMPAPER_SEARCH, 200, { total_num, exampapers }));

                return responseWrapper.succ({ total_num, exampapers });
            }

            // 记录普通试卷搜索的日志，等待完成
            await recordRecoDataLog(buildRecoLogData(req, enums.RecoDataLogType.EXAMPAPER_SEARCH, 200, ret));

            return responseWrapper.succ(ret);
        }
    } catch (err) {
        if (err.response) {
            // 服务器响应了错误状态码
            const response = err.response;
            if (response.status >= 400 && response.status < 500) {
                let ret = response.data;
                if (ret.code === 6 && res.freq != '') {
                    ret.msg = res.freq;
                }
                // 记录4xx错误的日志（客户端错误），等待完成
                await recordRecoDataLog(buildRecoLogData(req, enums.RecoDataLogType.EXAMPAPER_SEARCH, response.status, ret));
                return responseWrapper.send(ret);
            } else if (response.status >= 500) {
                // 记录5xx错误的日志（服务器错误），等待完成
                await recordRecoDataLog(buildRecoLogData(req, enums.RecoDataLogType.EXAMPAPER_SEARCH, response.status));
                return responseWrapper.error('HANDLE_ERROR');
            }
        } else {
            // 网络错误或其他错误
            return responseWrapper.error('HANDLE_ERROR', err.message);
        }
    }
}

/**
 * 试题增加标签展示
 * @param questions
 */
function addShowTags(questions = [], params) {
    let currYear = new Date().getFullYear();
    for (const question of questions) {
        const show_tags = [];
        if (question.type) show_tags.push(question.type); // 题型
        // 标签顺序 1 年份标签
        // 处理展示年份，保证试题年份和查询年份对应
        let year = null;
        if (params.year) {
            let years = params.year.split(',');
            let refer_exampapers = question.refer_exampapers || [];
            refer_exampapers.forEach(exampaper => {
                if (exampaper.year && years.includes('' + exampaper.year) && (!year || exampaper.year > year)) year = exampaper.year;
            });
        } else {
            year = question.year;
        }
        if (year) {
            // if (year > currYear) {
            //     year = currYear;
            // }
            show_tags.push(year.toString());
        }
        // if (lodash.size(question.refer_exampapers)) {
        //     const categorySet = new Set();
        //     for (const ref of question.refer_exampapers) {
        //         categorySet.add(ref.category);
        //     }
        //     // 标签顺序 2 地区
        //     if (params.province) {
        //         show_tags.push(params.province.toString());
        //     } else if (question.refer_exampapers[0].region || question.refer_exampapers[0].province) {
        //         show_tags.push(question.refer_exampapers[0].region || question.refer_exampapers[0].province); // 地区
        //     }
        //     // 标签顺序 3 地区
        //     // 考试类型  真题、模拟、联考、期末、期中、月考
        //     if (params.exam_type) {
        //         show_tags.push(lodash.slice(params.exam_type, 0, 2).join(''));
        //     } else {
        //         let examCategory = [];
        //         for (const category of categorySet.values()) {
        //             if (category === '高考真卷' || category === '中考真卷') {
        //                 examCategory.push({
        //                     name: '真卷',
        //                     order: 1
        //                 });
        //             } else if (category === '高考模拟' || category === '中考模拟') {
        //                 examCategory.push({
        //                     name: '模拟',
        //                     order: 2
        //                 });
        //             } else if (category === '期末试卷') {
        //                 examCategory.push({
        //                     name: '期末',
        //                     order: 3
        //                 });
        //             } else if (category === '期中试卷') {
        //                 examCategory.push({
        //                     name: '期中',
        //                     order: 4
        //                 });
        //             } else if (category === '月考试卷') {
        //                 examCategory.push({
        //                     name: '月考',
        //                     order: 5
        //                 });
        //             }
        //         }
        //         //
        //         if (lodash.size(examCategory)) {
        //             examCategory = lodash.sortBy(examCategory, 'order');
        //             examCategory = examCategory.filter((val, index) => index < 3);
        //             for (const category of examCategory) {
        //                 show_tags.push(category.name);
        //             }
        //         }
        //     }
        // }

        // if (params.type) { // 题型
        //     show_tags.push(question.type); // 题型
        // }

        if (question.difficulty) show_tags.push(enums.QuestionDifficultyName[question.difficulty]); // 难度
        if (question.elite) show_tags.push('精品题'); // 是否精品题
        question.show_tags = show_tags;
    }
}
function sortQuestions(params) {
    const { user_period, user_province, sort_by, offset_value, limit_value, questions = [] } = params;
    let current_year = new Date().getFullYear();
    let score_index = {};
    for (let index in questions) {
        let ques_score = 0;
        let one_ques = questions[index];
        // 按组卷次数 or 引用次数排序
        if (sort_by === 'use_times') {
            let use_times = one_ques.use_times || 0;
            ques_score = use_times * 1000;
        } else if (sort_by === 'cite_num') {
            let refer_times = one_ques.refer_times || 0;
            ques_score = refer_times * 1000;
        } else if (sort_by === 'year') {
            let ques_year = one_ques.year || 0;
            ques_score = ques_year * 1000;
        }

        // 年份加权
        let ques_year = one_ques.year || 0;
        if (ques_year && typeof ques_year == 'number') {
            ques_score += 200 * Math.exp(-0.5 * Math.abs(ques_year - current_year));
        }
        // 精品题加权
        let ques_elite = one_ques.elite || 0;
        if (ques_elite === 1) {
            ques_score += 5;
        }
        // 试卷引用
        let locale_score = 0;
        let category_score = [];
        let refer_exampapers = one_ques.refer_exampapers || [];
        for (let s = 0; s < refer_exampapers.length; s++) {
            let one_refer = refer_exampapers[s];
            // 过滤平台组的试卷
            let paper_from = one_refer.from || '';
            if (paper_from === 'ai_organize_paper') {
                continue
            }
            // 地区
            let province = one_refer.province || '';
            if (user_period === '初中' && user_province === province) {
                locale_score = 3
            }
            // 类型
            let paper_name = one_refer.name || '';
            let category = one_refer.category || '';
            if (category.match('真题|真卷') || paper_name.match('真题|真卷')) {
                category_score.push(1);
            } else if (category.match('联考|模拟') || paper_name.match('联考|模拟')) {
                category_score.push(0.8);
            } else if (category.match('期中|期末') || paper_name.match('期中|期末')) {
                category_score.push(0.6);
            }
        }
        ques_score += locale_score;
        if (category_score.length > 0) {
            ques_score += category_score.reduce((a, b) => Math.max(a, b), -Infinity);
        }
        // 试题难度加权
        let difficulty = one_ques.difficulty || 1;
        if (typeof difficulty == 'number') {
            ques_score += 1 / (Math.abs(difficulty - 4) + 1) * 2;
        }
        // 有解答
        let has_solutions = one_ques.has_solutions || 0;
        if (has_solutions === 1) {
            ques_score += 1.5
        }

        // 有解析
        let has_explanations = one_ques.has_explanations || 0;
        if (has_explanations === 1) {
            ques_score += 1.5
        }

        // 压轴题
        let is_final = one_ques.is_final || 0;
        if (is_final === 1) {
            ques_score += 0.2
        }

        // 试题题干
        let description = one_ques.description || '';
        let stems = one_ques.blocks.stems || [];
        for (let t = 0; t < stems.length; t++) {
            description += stems[t].stem || ''
        }


        // 名优校试题
        let attach = one_ques.attach || {};
        let is_famous = attach.is_famous || 0;
        if (is_famous === 1) {
            ques_score += 0.2
        }

        // 经典题
        let is_classic = attach.is_classic || 0;
        if (is_classic === 1) {
            ques_score += 0.2
        }
        score_index[index] = ques_score
        one_ques['recommend_score'] = ques_score
    }
    let new_questions = []
    // 排序返回指定的范围数据
    if (questions.length > 0) {
        let result = Object.keys(score_index).sort(function (a, b) { return score_index[b] - score_index[a] });
        let result_slice = result.slice(offset_value, Math.min(offset_value + limit_value, result.length));
        for (let j = 0; j < result_slice.length; j++) {
            let new_ques = questions[result_slice[j]];
            new_questions.push(new_ques)
        }
    }
    return new_questions
}

const getQuestionAnswerById = async (req, res, next) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let questionId = req.params.question_id;
        if (!questionId) responseWrapper.error('questionId不能为空');
        const right_params = {
            type: enums.NewRightType.ques_detail_num,
        }
        // if (req.user.source === enums.UserSource.HFS_APP_JS) {
        //     return responseWrapper.succ({blocks: {}});
        // }
        if (req.user.source !== enums.UserSource.HFS_APP_JS) {
            const right = await user_right_service.get_use_right(req.user.id, right_params);
            if (!lodash.isEmpty(right) && right.limit === 1000 && right.count >= 300) {
                return responseWrapper.error('OVER_LIMIT_ERROR', '今日查看试题详情次数已达上限');
            }
            const right_status = await user_right_service.use_right(req.user.id, right_params);
            if (!right_status) return responseWrapper.error('OVER_LIMIT_ERROR', '今日查看试题详情次数已达上限');
        }
        // 图片模式
        // let path = `/utilbox_api/v2/questions/${questionId}/image2`;
        // let query = req.query || {};
        // let server = config.get('UTIL_SERV');
        // let utilboxUrl = url.format({
        //     protocol: server.protocol,
        //     hostname: server.hostname,
        //     pathname: path,
        //     port: server.port,
        // });
        // let opstions = { headers: {}, timeout: 50000 };
        // query.appid = server.tmpAppid;
        // let response = await axios.get(utilboxUrl, { params: query }, opstions);
        // if (response.status === 200) {
        //     return responseWrapper.succ(response.data);
        // } else {
        //     return responseWrapper.error('HANDLE_ERROR');
        // }
        // 文本模式
        const questions = await kb_client.getQuestions([questionId]);
        if (lodash.size(questions)) {
            return responseWrapper.succ({ blocks: questions[0].blocks });
        }
        return responseWrapper.succ({});

    } catch (err) {
        console.log(err);
        return responseWrapper.error('HANDLE_ERROR');
    }
};

const getQuestionsKnowledgeById = async (req, res, next) => {
    let responseWrapper = new ResponseWrapper(req, res);
    let questionId = req.params.question_id;
    let path = `/kb_api/v2/questions/${questionId}`; // ori path
    let query = req.query;
    if (!query.api_key) {  // trick, for exampaper answer
        query['api_key'] = req.apiKey;
    }
    const right_params = {
        type: enums.NewRightType.ques_detail_num,
    }
    if (req.user.source !== enums.UserSource.HFS_APP_JS) {
        const right = await user_right_service.get_use_right(req.user.id, right_params);
        if (_.isEmpty(right) || (right.limit !== enums.Infinite && right.limit <= right.count)) return responseWrapper.error('OVER_LIMIT_ERROR', '今日查看试题详情次数已达上限');
    }
    let server = config.get('KB_API_SERVER');
    let kbUrl = url.format({
        protocol: server.protocol,
        hostname: server.hostname,
        pathname: path,
        port: server.port,
    });
    let opstions = { headers: {}, timeout: 50000 };
    let response = await axios.get(kbUrl, { params: req.query }, opstions);
    if (response.status === 200) {
        let data = {
            blocks: {
                knowledges: (response.data.blocks || {}).knowledges || [],
            },
            knowledges: (response.data.knowledges || []),
        }
        return responseWrapper.succ(data);
    } else {
        return responseWrapper.error('HANDLE_ERROR');
    }
};


const getQuestionsById = async (req, res, next) => {
    let responseWrapper = new ResponseWrapper(req, res);
    let path = `/kb_api/v2${req.path}`; // ori path

    // 过滤路径非/questions/:question_id/接口
    let reg = pathToRegexp('/kb_api/v2/questions/:question_id(\\d+)', []);
    if (!reg.exec(path)) return next();

    let query = req.query;
    let user = req.user;
    if (!query.api_key) {  // trick, for exampaper answer
        query['api_key'] = req.apiKey;
    }

    if (path.indexOf('profile') >= 0) {
        delete query.freq;
    }

    let server = config.get('KB_API_SERVER');
    let kbUrl = url.format({
        protocol: server.protocol,
        hostname: server.hostname,
        pathname: path,
        port: server.port,
    });
    let opstions = { headers: {}, timeout: 50000 };
    req.query.freq = false;

    let response = await axios.get(kbUrl, { params: req.query }, opstions);
    if (response.status == 200) {
        if (response.data && (response.data.code === 6)) {
            return responseWrapper.send({ code: 6, msg: '您的账号已达本月数量上限', data: {} });
        }
        // req.query.freq 为是否超限字段
        if (response.data && req.query.freq) {
            return responseWrapper.send({ code: 6, msg: '您的账号已达本月数量上限', data: {} });
        }
        response.data.blocks = {
            types: response.data.blocks.types,
            stems: response.data.blocks.stems,
            knowledges: response.data.blocks.knowledges,
        }
        return responseWrapper.succ(response.data);
    } else {
        return responseWrapper.error('HANDLE_ERROR');
    }
};

const getKnowledgeBbySearchSchema = Joi.object({
    key: Joi.string().required(),
    period: Joi.string().required(),
    subject: Joi.string().valid(...Object.values(enums.Subject)).required(),
}).unknown(true);

const getKnowledgeBbySearch = async (req, res) => {
    let resWrap = new ResponseWrapper(req, res);
    try {

        let { key, period, subject } = await Joi.validate(req.query, getKnowledgeBbySearchSchema);

        let booksDetails;
        let str = await rediser.redis.get(`tiku:kb:books:${period}:${subject}`);
        booksDetails = JSON.parse(str);
        if (!booksDetails) {
            let KBAPISERV = config.get('KB_API_SERVER');
            let booksUri = url.format({
                protocol: KBAPISERV.protocol,
                hostname: KBAPISERV.hostname,
                port: KBAPISERV.port,
                pathname: `/kb_api/v2/books`,
                search: qs.stringify({
                    api_key: KBAPISERV.appKey,
                })
            });
            let books = await axios.get(booksUri).then(utils.kbHandler);
            let curPeriodTree = books && books.book && books.book.children.find(e => e.name === period) || {};
            let curSubjectTree = curPeriodTree && curPeriodTree.children.find(e => e.name === subject) || {};
            let curBookTree = curSubjectTree && curSubjectTree.children || [];
            let bookIds = lodash.flatten(curBookTree.map(e => e.children && e.children.map(e_grade => e_grade.id)));

            let booksDetailUri = url.format({
                protocol: KBAPISERV.protocol,
                hostname: KBAPISERV.hostname,
                port: KBAPISERV.port,
                pathname: `/kb_api/v2/books/detail`,
                search: qs.stringify({
                    ids: lodash.join(bookIds, ','),
                    api_key: KBAPISERV.appKey,
                })
            });
            booksDetails = await axios.get(booksDetailUri).then(utils.kbHandler);
            rediser.redis.setex(`tiku:kb:books:${period}:${subject}`, 60 * 60, JSON.stringify(booksDetails));
        }

        let resObj = {};
        for (let detailInfo of booksDetails) {
            const curKnowledges = getKnowledgeBbyKey(detailInfo.book.children, key);
            if (curKnowledges.length > 0)
                resObj[`${detailInfo.id}`] = {
                    id: detailInfo.id,
                    press_version: detailInfo.press_version,
                    grade: detailInfo.grade,
                    knowledges: curKnowledges,
                }

        }
        return resWrap.succ(resObj);
    } catch (err) {
        Logger.error(err);
        return resWrap.error('HANDLE_ERROR', err.message);
    }
}

function getKnowledgeBbyKey(data, key) {
    let knowledges = [];
    for (let info of data) {
        if (info.key === 'knowledge') {
            if (info.name.indexOf(key) >= 0)
                knowledges.push(lodash.pick(info, ['name', 'ques_num', 'id']));
        } else if (info.children && info.children.length > 0) {
            const curKnowledges = getKnowledgeBbyKey(info.children, key);
            knowledges = knowledges.concat(curKnowledges);
        }
    }
    return knowledges;
}

async function getQuestionReco(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    const questionId = req.params.question_id;
    const cacheKey = `question:reco:${questionId}`;
    let cacheQuestions = await rediser.redis.get(cacheKey);
    if (cacheQuestions) return responseWrapper.succ(JSON.parse(cacheQuestions));
    let path = `/kb_api/v2/questions/${questionId}`;
    const query = {
        api_key: req.apiKey
    }

    let server = config.get('KB_API_SERVER');
    let kbUrl = url.format({
        protocol: server.protocol,
        hostname: server.hostname,
        pathname: path,
        port: server.port,
    });
    let opstions = { headers: {}, timeout: 50000 };
    let response = await axios.get(kbUrl, { params: query }, opstions);
    if (response.status === 200) {
        const question = response.data;
        if (lodash.isEmpty(question)) {
            return responseWrapper.error('HANDLE_ERROR', `题目不存在[${questionId}]`);
        }
        const questionIds = question.reco_questions || [];
        if (!lodash.size(questionIds)) {
            let ai_ques_server = config.get('AI_QUES_SERVER');
            let ai_ques_url = url.format({
                protocol: ai_ques_server.protocol,
                hostname: ai_ques_server.hostname,
                pathname: `/sim_ques/v1/recom_sim_ques`
            })
            const ai_params = {
                sam_kb_ques_id: questionId
            }
            let opstions = { headers: {}, timeout: 50000 };
            let result = await axios.get(ai_ques_url, { params: ai_params }, opstions);
            if (result.status === 200) {
                let reco_sim_questions = result.data.data.reco_sim_questions || [];
                for (let i = 0; i < reco_sim_questions.length; i++) {
                    questionIds.push(reco_sim_questions[i].id);
                    if (lodash.size(questionIds) >= 10) {
                        break;
                    }
                }
            }
        }
        if (!lodash.size(questionIds)) return responseWrapper.succ([]);
        let questions = await getQuestionsByIds(questionIds);
        if (!lodash.size(questions)) return responseWrapper.succ([]);
        // 删除答案、解析、解答
        questions = questions.map(e => {
            if (!lodash.isEmpty(e.blocks)) {
                delete e.blocks.answers;
                delete e.blocks.solutions;
                delete e.blocks.explanations;
            }
            return e;
        });
        addShowTags(questions, {}); // 增加标签
        rediser.set(cacheKey, questions, 60 * 60 * 24 * 2); // 两天有效期
        return responseWrapper.succ(questions);
    } else {
        return responseWrapper.error('HANDLE_ERROR');
    }

}

module.exports = {
    downloadQuestion: downloadQuestion,
    downloadExampaper: downloadExampaper,
    assembleExampaper: assembleExampaper,
    downloadDocx: downloadDocx,
    getExampaperInfo: getExampaperInfo,
    questionsFilters: questionsFilters,
    questionBySearch: questionBySearch,
    exampaperBySearch: exampaperBySearch,
    getQuestionsById: getQuestionsById,
    getKnowledgeBbySearch: getKnowledgeBbySearch,
    getQuestionReco: getQuestionReco,
    getQuestionAnswerById,
    getQuestionsKnowledgeById,
    getDownloadInfo,
    getDownload,
    getQuestionsByIds,
};
