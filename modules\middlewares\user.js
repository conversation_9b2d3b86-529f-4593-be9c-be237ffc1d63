var config  = require('config');

module.exports = function(req, res, next){

	if(req.url.indexOf('dmp_api') !== -1)
		return next();

	var userInfo = config.get('TIKU_SERVER').userInfo;
	var user = req.user;
	if(!req.cookies[userInfo.name]){
		user.userId = user.id;
		res.cookie(userInfo.name, JSON.stringify(user), userInfo.options);
	}
	user.userId = user.id;
	res.cookie(userInfo.name, JSON.stringify(user), userInfo.options);
	next();
}
