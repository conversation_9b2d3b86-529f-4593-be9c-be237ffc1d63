let mongodber = require('../../utils/mongodber');
let ResponseWrapper = require('../../middlewares/response_wrapper');
let logger = require('../../utils/logger');
let enums = require('../../../bin/enum');
let db = mongodber.use('tiku');
const Joi = require('@hapi/joi');

const getOrderListSchema = Joi.object({
    // user_id: Joi.string().optional(),
    // uc_id: Joi.string().optional(),

    // type: Joi.string().valid(...Object.values(enums.GoodsType)).default(enums.GoodsType.MEMBER).optional(),
    limit: Joi.number().default(10).optional(),
    offset: Joi.number().default(0).optional(),
    type: Joi.string().default('').optional(),
    ctime_from: Joi.number().default(0).optional(),
    ctime_to: Joi.number().default(0).optional(),

}).unknown(true);


const getOrderList = async (req, res) => {
    let responseWrapper = new ResponseWrapper(req, res);
    try {
        let { limit, offset, type, ctime_from, ctime_to } = await Joi.validate(req.query, getOrderListSchema);

        const cond = { status: enums.OrderStatus.SUCCESS, user_id: req.user.id };
        if (type) cond.type = type;
        if (ctime_from && ctime_to) {
            cond.ctime = {
                $gte: new Date(ctime_from),
                $lte: new Date(ctime_to)
            }
        }
        let total = await db.collection('@Order').count(cond);
        let data = await db.collection('@Order').find(cond).sort({ ctime: -1 }).skip(offset).limit(limit).toArray();

        if (!data) {
            throw new Error('没有数据');
        }
        let resObj = {
            total: total,
            list: []
        };

        for (const item of data) {
            const amount = item.final_price - (item.coupon_fee || 0) - (item.discount_fee || 0);
            const obj = {
                id: item._id.toString(),
                no: item.no,
                amount: amount < 0 ? 0 : amount,
                ctime: item.ctime && item.ctime.getTime(),
                utime: item.utime && item.utime.getTime(),
                transaction_id: item.transaction_id,
            }
            const goods = {
                id: item.goods.id || item.goods.sku.id,
                name: item.goods.name || item.goods.spu.name,
                info: '',
                items: item.goods.items
            }
            if (item.goods) {
                if (item.target) {
                    goods.info = `ID: ${item.target.id}`;
                } else if (item.goods.resource_id) {
                    goods.info = `ID: ${item.goods.resource_id}`;
                } else if (item.goods.month) {
                    goods.info = `${item.goods.month}个月`;
                } else if (item.goods.sku) {
                    goods.info = item.goods.sku.name;
                }
            }
            obj.goods = goods;
            resObj.list.push(obj);
        }
        return responseWrapper.succ(resObj);
    } catch (err) {
        logger.error(err.stack);
        return responseWrapper.error('HANDLE_ERROR', err.message);
    }
};

module.exports = {
    getOrderList,
};
