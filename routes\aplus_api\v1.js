var express = require('express');
var router = express.Router();
var apikey = require('../../modules/middlewares/apikey.js');

var aplus = require('../../modules/aplus_api/v1/aplus.js');

// kboe api key middleware
router.use(apikey('KBOE'));

// 取单试卷信息
router.get('/papers/:paper_id/', aplus.paper);
// 批量获取试题识别信息
router.post('/questions/', aplus.postQuestions);
// 更新试卷下载次数
router.put('/papers/:paper_id/download_times/', aplus.paperDownloadTimes);


module.exports = router;
