/*
 * Desc: kb api transmiter
 * Author: guo<PERSON><PERSON>
 */
let qs = require('querystring');
let url = require('url');
let config = require('config');
let isMobileByUa = require('../../utils/utils').isMobileByUa;
let ResponseWrapper = require('../../middlewares/response_wrapper');
let client = require('zipkin-middleware').client;

const TIMEOUT = 5000;
const RETRY_TIMES = 1;

// resource protection
const RESOURCE_VIP = [
    /^\/questions\/\d+/,
    /^\/exampapers\/\d+\/download/,
    /^\/questions\/\d+(,\d+)*\/download/,
    /^\/assemble\/exampaper\/download/,
];

const RESOURCE_TIMEOUT = [{
    path: /^\/exampapers\/\d+\/download/,
    timeout: 30000,
}, {
    path: /^\/questions\/\d+(,\d+)*\/download/,
    timeout: 30000,
}, {
    path: /^\/assemble\/exampaper\/download/,
    timeout: 30000,
}];



function transmit(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);

    // protect resource
    // if (!req.isVip && req.user.role != '教师'){
    //     for (let pat in RESOURCE_VIP){
    //         if (req.path.search(RESOURCE_VIP[pat]) > -1){
    //             return responseWrapper.error('NEED_VIP_ERROR');
    //         }
    //     }
    // }

    // set specitial timeout
    let timeout = TIMEOUT;
    for (let pat in RESOURCE_TIMEOUT) {
        let item = RESOURCE_TIMEOUT[pat];
        if (req.path.search(item.path) > -1) {
            timeout = item.timeout;
        }
    }

    // transmit
    let query = req.query;
    if (!query.api_key) {  // trick, for exampaper answer
        query['api_key'] = req.apiKey;
    }
    let path = `/kb_api/v2${req.path}`; // ori path
    // 判断如果是请求 book 详情的接口，则添加上 device 字段
    // 为了方便 kb_api 获取特定英语口语教材的数据
    let mobileBooks = false;
    if (isMobileByUa(req.headers['user-agent']) && path.indexOf('/kb_api/v2/books') !== -1) {
        delete query.freq;
        query['device'] = 'mobile';
        if (path.length < 19) { // 判断是获取列表 /kb_api/v2/books，（16） /kb_api/v2/books/ （17）
            delete query.device; // 2023-2-21好分数需要返回教材版本，放开限制
            mobileBooks = true;
        }
    }

    if (path.indexOf('profile') >= 0) {
        delete query.freq;
    }

    let server = config.get('KB_API_SERVER');
    let options = {
        uri: url.format({
            protocol: server.protocol,
            hostname: server.hostname,
            port: server.port,
            pathname: path,
            search: qs.stringify(query),
        }),
        method: req.method,
        timeout: timeout,
        retryTimes: RETRY_TIMES,
    };
    let body = JSON.stringify(req.body);
    if (body != '{}') {
        options['headers'] = {
            'content-type': 'application/json',
            'content-length': Buffer.byteLength(body),
        };
        options['body'] = body;
        // 过滤mkp 题目、卷子
        options['body']['filter_mkp'] = 'true';
    }

    client.request(options, function (err, response, body) {
        if (err) {
            return responseWrapper.error('HANDLE_ERROR', err.message);
        }
        if (response.statusCode == 200) {
            let ret = JSON.parse(body);
            if (mobileBooks) {
                filterNullArrPressVersion(ret);
            }
            if (ret.code === 6) {
                if (res.freq != '') {
                    ret.msg = res.freq;
                }
                return responseWrapper.send(ret);
            }
            return responseWrapper.succ(ret);
        } else if (response.statusCode >= 400 && response.statusCode < 500) {
            let ret = JSON.parse(body);
            if (ret.code === 6 && res.freq != '') {
                ret.msg = res.freq;
            }
            return responseWrapper.send(ret);
        } else if (response.statusCode >= 500) {
            return responseWrapper.error('HANDLE_ERROR');
        }
    });
}

// 如果教材下press_version的children [] === 0,则删除这个教材
const filterNullArrPressVersion = function (ret) {
    if (ret && ret.book && ret.book.children && ret.book.children.length > 0) {
        for (let period of ret.book.children) {
            if (period.children && period.children.length > 0) {
                for (let subject of period.children) {
                    if (Array.isArray(subject.children) && subject.children.length > 0) {
                        let pressVersionArr = [];
                        for (let press_version of subject.children) {
                            if (Array.isArray(press_version.children) && press_version.children.length > 0) {
                                pressVersionArr.push(press_version);
                            }
                        }
                        subject.children = pressVersionArr;
                    }
                }
            }
        }
    }
}

module.exports = {
    transmit: transmit,
}
