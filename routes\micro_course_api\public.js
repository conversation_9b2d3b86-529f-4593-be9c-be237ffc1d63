/*
 * Desc: the route of kb api
 * Author: guo<PERSON><PERSON>
 */
const router = require('express').Router();
const course = require('../../modules/micro-course/v1');

/**
 * 获取首页信息
 */
router.get('/home', course.getHomeData);
/**
 * 获取筛选项列表
 */
router.get('/options', course.getOptions);
// 查询视频课程数量
router.get('/search/total', course.getTotal);
// 查询视频课程包列表
router.get('/search', course.search);
// 查询视频包详细
router.get('/pack', course.getPackDetail);
// 增加访问量
router.post('/pack/visit', course.addVisit);

module.exports = router;
