/*
 * @Descripttion: 
 * @Date: 2024-03-05 17:05:02
 */
const express = require('express')
const router = express.Router()
const statistic = require('../../modules/statistic_api/v2/index.js')
const multer = require('multer')
const upload = multer()

router.post('/access_spots', upload.any(), statistic.postAccessSpots)

router.put('/access_spots', upload.any(), statistic.updateStayTime)

module.exports = router