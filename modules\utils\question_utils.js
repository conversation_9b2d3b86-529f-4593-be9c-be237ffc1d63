
const _ = require('lodash');
const enums = require('../../bin/enum');

module.exports = {
    addShowTags,
}

/**
 * 试题增加标签展示
 * @param questions
 * @param params
 */
function addShowTags(questions = [], params = {}) {
    if (!_.size(questions)) return;
    for (const question of questions) {
        const show_tags = [];
        if (question.type) show_tags.push(question.type); // 题型
        // 标签顺序 1 年份标签
        if (params.year) {
            let years = params.year.split(',');

            let refer_exampapers = question.refer_exampapers || [];

            let year;
            refer_exampapers.forEach(exampaper => {
                if (exampaper.year && years.includes('' + exampaper.year) && (!year || exampaper.year > year)) year = exampaper.year;
            });
            show_tags.push(year ? '' + year : '' + question.year);
        } else {
            show_tags.push(question.year ? '' + question.year : '');
        }
        if (question.difficulty) show_tags.push(enums.QuestionDifficultyName[question.difficulty]); // 难度
        if (question.elite) show_tags.push('精品题'); // 是否精品题
        question.show_tags = show_tags;
    }
}
