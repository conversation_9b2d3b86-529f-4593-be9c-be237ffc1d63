var express = require('express');
var router = express.Router();

var apikey = require('../../modules/middlewares/apikey.js');
var statistic = require('../../modules/statistic_api/v1/statistic.js');

router.use(apikey('KB'));

router.get('/update', statistic.update);
router.get('/hots', statistic.hots);
router.post('/access_spots', statistic.postAccessSpots);
router.get('/access_spots', statistic.getAccessSpots);
router.get('/statistic_event', statistic.event);
module.exports = router;
