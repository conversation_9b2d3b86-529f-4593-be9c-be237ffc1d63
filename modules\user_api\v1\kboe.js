/*
 * Desc: yuanpei api
 * Author: guo<PERSON><PERSON>@iyunxiao.com
 */

'use strict';
const _ = require('underscore');
const qs = require('querystring');
const config = require('config');
const Thenjs = require('thenjs');
const Logger = require('../../utils/logger');
const ResponseWrapper = require('../../middlewares/response_wrapper');
const HttpWrapper = require('../../utils/http_wrapper');

const utils = require('./utils')
const KBOE_API_SERVER = config.get('KBOE_API_SERVER');
const YUANPEI_API_SERVER = config.get('YUANPEI_API_SERVER');
const TIMEOUT = 1000;
const RETRY_TIMES = 1;

function excellentAnswerFilter(stuId, quesIds, callback) {
    var server = config.get('HFS_VIP_SERVER'); 
    var _body = {
        studentId: stuId,
        questions: quesIds,
    };
    var options = {
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        method: 'POST',
        path: '/v1/questions/xb-answers/configs',
        timeout: TIMEOUT,
        body: JSON.stringify(_body),
        headers: {
            'content-type': 'application/json',
        },
    };
    var httpWrapper = new HttpWrapper();
    httpWrapper.request(options, function(err, ret){
        if (err){
            Logger.error('hfs xb-answers api failed' + err);
            return callback(null, []);
        }
        if (ret.statusCode == 200){
            try {
                // KBOE更改结构，兼容app
                ret = JSON.parse(ret.data);
                if (ret.code != 0) {
                    var err = new Error('Verify student:' + ret.msg);
                    return callback('hfs xb-answers api parsing failed' + err);
                    return callback(null, []);
                }
                return callback(null, ret.data);
            } catch (err){
                Logger.error('hfs xb-answers api parsing failed' + err);
                return callback(null, []);
            }
        } else {
            Logger.error('hfs xb-answers api failed');
            return callback(null, []);
        }
    });
}

function aplusPapers(req, res){
    var resWrapper = new ResponseWrapper(req, res);
    try {
        var user = req.user;
        var subject = req.query.subject;
        var grade = req.query.grade;
        if (!grade){
            grade = user.grade
        } else {
            if (!utils.checkGrade(grade)){
                throw Error(`grade: ${grade} is unlegal`);
            }
        }
        if (grade.search(/(上|下)$/) < 0){
            grade += utils.getSemester(new Date()); 
        }
        var scene = req.query.scene || '全部';
        var offset = parseInt(req.query.offset || 0);
        var limit = parseInt(req.query.limit || 10);
    } catch(err){
        return resWrapper.error('PARAMETERS_ERROR', err.message);
    }

    var _query = {
        owner: user.schoolId, // 84
        grade: grade, // '高一下'
        type: '试卷',
        subject: subject,
        scene: scene,
        offset: offset,
        limit: limit,
        api_key: req.apiKey,
    };

    var _path = '/yuanpei_api/v3/aplus/questions?';
    _path += qs.stringify(_query);
    var options = {
        protocol: YUANPEI_API_SERVER.protocol,
        hostname: YUANPEI_API_SERVER.hostname,
        port: YUANPEI_API_SERVER.port,
        method: 'GET',
        path: _path,
        timeout: TIMEOUT,
        retryTimes: RETRY_TIMES,
    };
    var httpWrapper = new HttpWrapper();
    httpWrapper.request(options, function(err, ret){
        if (err){
            return resWrapper.error('HANDLE_ERROR', err.message);
        }
        if (ret.statusCode == 200){
            var data = JSON.parse(ret.data);
            return resWrapper.succ(data);
        } else if (ret.statusCode >= 400 && ret.statusCode < 500){
            var data = JSON.parse(ret.data);
            return resWrapper.send(data);
        } else if (ret.statusCode >= 500){
            return resWrapper.error('HANDLE_ERROR');
        } else {
            return resWrapper.error('HANDLE_ERROR');
        }
    });
}

function knowledge(req, res){
    var resWrapper = new ResponseWrapper(req, res);
    // 参数校验
    try {
        var know_id = parseInt(req.params.knowledge_id);
        var offset = parseInt(req.query.offset || 0);
        var limit = parseInt(req.query.limit || 20);
    } catch(err){
        return resWrapper.error('PARAMETERS_ERROR', err.message);
    }

    var user = req.user;
    var _query = {
        stu_id: user.id, //stu_id: 20636740,
        offset: offset,
        limit: limit,
        api_key: req.apiKey,
    };
    var _path = `/kboe_api/v2/student/knowledges/${know_id}/questions/?`;
    _path += qs.stringify(_query);
    var options = {
        protocol: KBOE_API_SERVER.protocol,
        hostname: KBOE_API_SERVER.hostname,
        port: KBOE_API_SERVER.port,
        method: 'GET',
        path: _path,
        timeout: TIMEOUT,
        retryTimes: RETRY_TIMES,
    };
    var httpWrapper = new HttpWrapper();
    Thenjs(function(cont) {
        httpWrapper.request(options, function(err, ret){
            if (err){
                return resWrapper.error('HANDLE_ERROR', err.message);
            }
            if (ret.statusCode == 200){
                var data = JSON.parse(ret.data);
                if(data.code && data.code !== 0) {
                    if(5 === data.code) { // kboe空数据返回成功
                       return resWrapper.succ({});
                    }
                    return cont(data.msg);
                }
                return cont(null, data);
            } else if (ret.statusCode >= 400 && ret.statusCode < 500){
                var data = JSON.parse(ret.data);
                return resWrapper.send(data);
            } else if (ret.statusCode >= 500){
                return resWrapper.error('HANDLE_ERROR');
            } else {
                return resWrapper.error('HANDLE_ERROR');
            }
        });
    }).then(function(cont, data) {
        var ques_ids = [];
        for (var i=0; i<data.exam_questions.length; i++) {
            ques_ids.push(data.exam_questions[i].id);
        }
        excellentAnswerFilter(user.id, ques_ids, function(err, filter) {
            if (err) {
                Logger.error('filter error');
                return resWrapper.error('HANDLE_ERROR');
            }
            return cont(null, data, filter);
        });
    }).then(function(cont, data, filter) {
        var filterDict = {};
        for (var i=0; i<filter.length; i++) {
            filterDict[filter[i]['questionKey']] = filter[i]['hideXbAnswer'];
        }
        for (var i=0; i<data.exam_questions.length; i++) {
            var _id = data.exam_questions[i]['id'];
            data.exam_questions[i]['hideXbAnswer'] = filterDict[_id];
            if (filterDict[_id]==true || filterDict[_id]==null) {
                if (data.exam_questions[i]['answer'] && data.exam_questions[i]['answer']['type'] == 'excellent') {
                    data.exam_questions[i]['answer']['contents'] = [[]];
                }
            }
        }
        return resWrapper.succ(data);
    }).fail(function(cont, err) {
        Logger.error(err);
        return resWrapper.error('HANDLE_ERROR');
    }).finally(function(cont, err) {
        Logger.error(err);
        return resWrapper.error('HANDLE_ERROR');
    });
}

/*
 * Desc: 获取用户的薄弱知识点
 */
function _getWeakKnowledges(stuId, callback){
    var _query = {
        stu_id: stuId,
        api_key: KBOE_API_SERVER.appKey,
    }
    var _path = `/kboe_api/v2/student/weak/knowledges/?`;
    _path += qs.stringify(_query);

    var options = {
        protocol: KBOE_API_SERVER.protocol,
        hostname: KBOE_API_SERVER.hostname,
        port: KBOE_API_SERVER.port,
        path: _path,
        method: 'GET',
        timeout: TIMEOUT,
        retryTimes: RETRY_TIMES,
    };
    var httpWrapper = new HttpWrapper();
    httpWrapper.request(options, function(err, ret){
        if (err){
            callback(err);
        }
        if (ret.statusCode == 200){
            callback(null, JSON.parse(ret.data));
        } else if (ret.statusCode >= 400 && ret.statusCode < 500){
            callback(null, null);
        } else if (ret.statusCode >= 500){
            var err = JSON.parse(ret.data);
            callback(new Error(err.msg));
        }
    });
}

function weakKnowledges(req, res){
    var resWrapper = new ResponseWrapper(req, res);
    var stuId = req.user.id;
    _getWeakKnowledges(stuId, function(err, ret){
        if (err){
            return resWrapper.error('HANDLE_ERROR', err.message);
        }
        if (!ret){
            return resWrapper.error('NULL_ERROR');
        }
        return resWrapper.succ(ret);
    });
}

function recoWeakKnowledges(req, res){
    var resWrapper = new ResponseWrapper(req, res);
    try {
        var limit = req.query.limit || 1; // default is 1
    } catch(err) {
        resWrapper.error('PARAMETERS_ERROR', err.message);
    }

    var stuId = req.user.id;
    _getWeakKnowledges(stuId, function(err, ret){
        if (err){
            return resWrapper.error('HANDLE_ERROR', err.message);
        }
        if (!ret){
            return resWrapper.error('NULL_ERROR');
        }
        var knows = [];
        for (var i in ret){
            var subject = ret[i]['subject'];
            var items = ret[i]['knowledges'];
            for (var j in items){
                if (items[j].lscore < 1){
                    continue;
                }
                items[j]['subject'] = subject;
                knows.push(items[j]);
            }
        }
        if (knows.length == 0){
            return resWrapper.error('NULL_ERROR');
        }
        var vk = _.sample(knows, limit);
        return resWrapper.succ(vk);
    });
}

module.exports = {
    aplusPapers: aplusPapers,
	knowledge: knowledge,
    weakKnowledges: weakKnowledges,
    recoWeakKnowledges: recoWeakKnowledges,
}
