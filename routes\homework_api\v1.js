var express = require('express');
var router = express.Router();

var apikey = require('../../modules/middlewares/apikey.js');
var homework = require('../../modules/homework_api/v1/homework.js');

router.use(apikey('KB'));

router.get('/subjects', homework.getSubjects);
router.get('/subjects/:subject/book', homework.getBookStruct);
router.get('/chapters/:chapter_id', homework.getChapterAnswers);
router.get('/books', homework.getBooks);
module.exports = router;
