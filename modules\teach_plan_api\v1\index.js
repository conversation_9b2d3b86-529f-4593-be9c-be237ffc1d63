const _ = require('lodash');
const ObjectID = require("mongodb").ObjectID;
const mongodber = require('../../utils/mongodber');
const db = mongodber.use('tiku');
const db_tiku_open = mongodber.use('tiku_open');
const ResponseWrapper = require('../../middlewares/response_wrapper');
const logger = require('../../utils/logger');
const enums = require('../../../bin/enum');
const Joi = require('@hapi/joi');
const client = require('../../client');
const constants = require('../../utils/constants');
const paper_utils = require('../../utils/paper_utils');
// 教学计划表
const collection = db.collection(constants.schema.user_teach_plan);
// 用户组卷表
const collection_exampaper= db_tiku_open.collection(enums.OpenSchema.user_paper);
// 组卷草稿表
const collection_exampaper_draft= db.collection(constants.schema.exampaper_draft);

const type_name = {
    "周考": '周考试卷',
    "月考": '月考试卷',
    "期中考试": '期中试卷',
    "期末考试": '期末试卷'
}

module.exports = {
    getList,
    getDetail,
    getLast,
    save,
    addJoinPaper,
    delJoinPaper,
    getRecoPaper,
    syncToExampaper,
    delPlan,
};

const JOI_LIST = Joi.object({
    period: Joi.string().required(),
    subject: Joi.string().required(),
}).unknown(true);

async function getList(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const user_id = req.user.id;
        const { period, subject } = await JOI_LIST.validate(req.query);
        const user_list = await collection.find({ user_id, period, subject, deleted: enums.BooleanNumber.NO }).project({ name: 1, grade: 1, book_version:1, knowledge_tree_id: 1, knowledge_tree_name: 1, stime: 1, etime: 1, ctime: 1, utime: 1 }).sort({ctime: -1}).toArray();
        if (!_.size(user_list)) return responseWrapper.succ([]);
        const result = user_list.map(e => {
            return {
                id: e._id.toString(),
                name: e.name,
                book_version: e.book_version, // 教材版本-使用知识树时非必填
                knowledge_tree_id: e.knowledge_tree_id, // 知识树ID-使用教材时非必填
                knowledge_tree_name: e.knowledge_tree_name, //
                grade: e.grade,
                stime: e.stime,
                etime: e.etime,
                ctime: e.ctime.getTime(),
                utime: e.utime.getTime(),
            };
        });
        return responseWrapper.succ(result);
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const JOI_DETAIL = Joi.object({
    id: Joi.string().required(),
}).unknown(true);


async function getDetail(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const user_id = req.user.id;
        const { id } = await JOI_DETAIL.validate(req.query);
        const data = await collection.findOne({_id: new ObjectID(id)});
        if (_.isEmpty(data) || data.user_id !== user_id || data.deleted === enums.BooleanNumber.YES) {
            return responseWrapper.error('HANDLE_ERROR', '数据不存在');
        }
        const result = _.assign({}, data);
        result.id = data._id.toString();
        result.ctime = data.ctime.getTime();
        result.utime = data.utime.getTime();
        delete result._id;
        delete result.deleted;
        delete result.user_id;
        return responseWrapper.succ(result);
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

async function getLast(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    return responseWrapper.succ(null);
    // try {
    //     const user_id = req.user.id;
    //     const list = await collection.find({user_id: user_id}).project({grade:1, stime: 1, etime: 1}).sort({ctime: -1}).toArray();
    //     if (!_.size(list)) return responseWrapper.succ('');
    //     let id = null;
    //     const now = Date.now();
    //     for (const d of list) {
    //         if (now >= d.stime && now < d.etime) {
    //             id = d._id;
    //             break;
    //         }
    //     }
    //     if (!id) return responseWrapper.succ('');
    //     const data = await collection.findOne({_id: id});
    //     const result = _.assign({}, data);
    //     result.id = data._id.toString();
    //     result.ctime = data.ctime.getTime();
    //     result.utime = data.utime.getTime();
    //     delete result._id;
    //     return responseWrapper.succ(result);
    // } catch (e) {
    //     logger.error(e.stack);
    //     return responseWrapper.error('HANDLE_ERROR', e.message);
    // }
}

const JOI_SAVE = Joi.object({
    id: Joi.string().optional(),
    name: Joi.string().required(), // 考试名称
    period: Joi.string().required(), // 学段
    subject: Joi.string().required(), // 科目
    grade: Joi.string().required(), // 年级
    book_version: Joi.string().optional(), // 教材版本-使用知识树时非必填
    knowledge_tree_id: Joi.number().optional(), // 知识树ID-使用教材时非必填
    knowledge_tree_name: Joi.string().optional(), // 知识树ID-使用教材时非必填
    init_week_num: Joi.number().required(), // 教学开始周次
    stime: Joi.number().required(), // 开始时间
    etime: Joi.number().required(), // 结束时间
    weeks: Joi.array().items(Joi.object({
        stime: Joi.number().required(), // 周开始时间
        etime: Joi.number().required(), // 周结束时间
        child: Joi.array().items(Joi.object({
            category: Joi.string().optional(), // 类型 'teach'-教学，'exam'-考试
            parent: Joi.object({
                book_id: Joi.number().optional(), // 教材ID
                book_name: Joi.string().optional(), // 教材名称
                id: Joi.number().required(), // 章节ID
                name: Joi.string().required(), // 章节名称
            }).optional(),
            id: Joi.number().optional(), // 章节ID
            name: Joi.string().optional(), // 章节名称
            type: Joi.string().optional(), // 考试类型
            papers: Joi.array().items(Joi.object({ // 关联试卷
                id: [
                    Joi.number().required(),
                    Joi.string().required()
                ],
                name: Joi.string().optional(),
                time: Joi.number().optional(),
                link: Joi.number().required(),
            })).optional(),
            reco_papers: Joi.array().items(Joi.object({ // 推荐试卷(仅考试)
                id: [
                    Joi.number().required(),
                    Joi.string().required()
                ],
                name: Joi.string().optional(),
                time: Joi.number().optional(),
            })).optional(),
        })).optional()
    })).required().min(1)
});


async function save(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const user_id = req.user.id;
        const params = await JOI_SAVE.validate(req.body);
        let id =  params.id;
        const now = new Date();
        if (id) {
            const data = await collection.findOne({_id: new ObjectID(id)});
            if (_.isEmpty(data) || data.user_id !== user_id || data.deleted === enums.BooleanNumber.YES) {
                return responseWrapper.error('HANDLE_ERROR', '数据不存在');
            }
            params.utime = now;
            delete params.id;
            await collection.updateOne({ _id: new ObjectID(id) }, { $set: params });
        } else {
            params.deleted = enums.BooleanNumber.NO;
            params.user_id = user_id;
            params.ctime = now;
            params.utime = now;
            const insert_result = await collection.insertOne(params);
            id = insert_result.insertedId.toString();
        }
        return responseWrapper.succ({id});
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const JOI_ADD_JOIN_PAPER = Joi.object({
    id: Joi.string().required(),
    index: Joi.string().required(), // 索引(week_child)
    paper_id: [ // 试卷ID
        Joi.string().required(),
        Joi.number().required()
    ],
    name: Joi.string().required(), // 试卷名称
}).unknown(true);

async function addJoinPaper(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const user_id = req.user.id;
        const { id, index, paper_id, name } = await JOI_ADD_JOIN_PAPER.validate(req.body);
        const now = new Date();
        const data = await collection.findOne({ _id: new ObjectID(id) });
        if (_.isEmpty(data) || data.user_id !== user_id || data.deleted === enums.BooleanNumber.YES) {
            return responseWrapper.error('HANDLE_ERROR', '数据不存在');
        }
        const week_index = parseInt(index.split('_')[0]);
        const child_index = parseInt(index.split('_')[1]);
        const child = _.get(data, `weeks[${week_index}].child[${child_index}]`, null);
        if (_.isEmpty(child)) return responseWrapper.error('HANDLE_ERROR', '教学计划未设置不能进行该操作!');
        const papers = child.papers || [];
        const same_data = papers.find(e => e.id === paper_id);
        if (!_.isEmpty(same_data)) return responseWrapper.succ({id});
        let link = 1; // 默认推荐
        if (!_.isNumber(paper_id)) {
            const link_paper = await collection_exampaper.findOne({_id: new ObjectID(paper_id)});
            if (!_.isEmpty(link_paper) && link_paper.user_id === user_id && link_paper.display !== 0) {
                link = 2; // 用户组卷
            }
        }
        const paper = {
            id: paper_id,
            name: name,
            time: now.getTime(),
            link: link
        };
        papers.push(paper);
        child.papers = papers;
        await collection.updateOne({ _id: data._id }, { $set: { weeks: data.weeks, utime: now } });

        return responseWrapper.succ({
            id: id,
            papers: child.papers || []
        });
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const JOI_DEL_JOIN_PAPER = Joi.object({
    id: Joi.string().required(),
    index: Joi.string().required(), // 索引(week_child)
    paper_id: [ // 试卷ID
        Joi.string().required(),
        Joi.number().required()
    ],
}).unknown(true);

async function delJoinPaper(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const user_id = req.user.id;
        const { id, index, paper_id } = await JOI_DEL_JOIN_PAPER.validate(req.body);
        const data = await  collection.findOne({ _id: new ObjectID(id) });
        if (_.isEmpty(data) || data.user_id !== user_id || data.deleted === enums.BooleanNumber.YES) {
            return responseWrapper.error('HANDLE_ERROR', '数据不存在');
        }
        const week_index = parseInt(index.split('_')[0]);
        const child_index = parseInt(index.split('_')[1]);
        const child = _.get(data, `weeks[${week_index}].child[${child_index}]`, null);
        if (_.isEmpty(child)) return responseWrapper.error('HANDLE_ERROR', '教学计划未设置不能进行该操作!');
        const papers = child.papers;
        if (!_.size(papers)) return responseWrapper.succ({id});
        child.papers = papers.filter(e => e.id !== paper_id);
        await collection.updateOne({ _id: data._id }, { $set: { weeks: data.weeks, utime: new Date() } });
        return responseWrapper.succ({
            id: id,
            papers: child.papers || []
        });
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const JOI_GET_RECO_PAPER = Joi.object({
    id: Joi.string().required(),
    index: Joi.string().required(), // 索引(week_child)
}).unknown(true);

async function getRecoPaper(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const user_id = req.user.id;
        const { id, index } = await JOI_GET_RECO_PAPER.validate(req.query);
        const arr = index.split('_');
        if (arr.length !== 2) return responseWrapper.error('PARAMETERS_ERROR', '任务索引错误');
        const week_index = parseInt(arr[0]);
        const child_index = parseInt(arr[1]);
        const data = await collection.findOne({ _id: new ObjectID(id) });
        if (_.isEmpty(data) || data.user_id !== user_id || data.deleted === enums.BooleanNumber.YES) {
            return responseWrapper.error('HANDLE_ERROR', '数据不存在');
        }
        const child = _.get(data, `weeks[${week_index}].child[${child_index}]`, null);
        if (_.isEmpty(child)) return responseWrapper.error('PARAMETERS_ERROR', '任务索引错误');
        if (child.category !== 'exam') return responseWrapper.error('PARAMETERS_ERROR', `任务类型错误`);

        const result = [];
        const week = _.get(data, `weeks[${week_index}]`, null);
        const algo_params = await _build_algo_params(req.user, data, week, child);
        // const algo_params = await _build_algo_params();
        // 算法组卷
        const algo_result = await client.algo.recommendPaper(algo_params);
        // 处理结果
        const now = Date.now();
        if (algo_result.real_papers) { // 推荐系统试卷
            const ids = [];
            for (const paper of algo_result.real_papers) {
                ids.push(paper.id);
                result.push({
                    id: paper.id,
                    name: paper.name,
                    time: now,
                    ques_num: 0,
                });
            }
            const kb_papers = await client.kb.getExampapers(ids);
            if (_.size(kb_papers)) {
                for (const p of result) {
                    const kb_paper = kb_papers.find(e => e.id === p.id);
                    p.ques_num = _.get(kb_paper, 'ques_num', 0);
                }
            }
        }
        if (algo_result.ai_papers) { // 算法组卷
            for (const paper of algo_result.ai_papers) {
                paper.type = type_name[child.type];
                const paper_draft = await paper_utils.build_algo_paper(req.user, paper);
                paper_draft.period = data.period;
                paper_draft.grade = data.grade;
                paper_draft.subject = data.subject;
                // paper_utils.set_display(0); // 默认不展示
                const insert_result = await collection_exampaper_draft.insertOne(paper_draft);
                result.push({
                    id: insert_result.insertedId.toString(),
                    name: paper_draft.name,
                    time: now,
                    draft: 1,
                    ques_num: paper_utils.get_question_num(paper_draft),
                });
            }
        }
        // 返回结果
        return responseWrapper.succ(result);
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const JOI_SYNC_PAPER = Joi.object({
    id: Joi.string().required(),
});

async function syncToExampaper(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const user_id = req.user.id;
        const { id } = await JOI_SYNC_PAPER.validate(req.params);
        const draft_paper = await collection_exampaper_draft.findOne({_id: new ObjectID(id)});
        if (_.isEmpty(draft_paper) || draft_paper.user_id !== user_id) return responseWrapper.error('HANDLE_ERROR', '数据不存在');
        const exampaper = _.assign({}, draft_paper);
        delete exampaper._id;
        const now = new Date();
        exampaper.ctime = now;
        exampaper.utime = now;
        // paper_utils.set_display(exampaper, 0);
        const insert_result = await collection_exampaper.insertOne(exampaper);
        const result = {
            id: insert_result.insertedId.toString(),
            name: exampaper.name,
            time: exampaper.ctime.getTime()
        }
        return responseWrapper.succ(result);
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

const JOI_DEL_PLAN = Joi.object({
    id: Joi.string().required(),
});
async function delPlan(req, res) {
    const responseWrapper = new ResponseWrapper(req, res);
    try {
        const user_id = req.user.id;
        const { id } = await JOI_DEL_PLAN.validate(req.params);
        const data = await collection.findOne({ _id: new ObjectID(id) });
        if (_.isEmpty(data) || data.user_id !== user_id || data.deleted === enums.BooleanNumber.YES) return responseWrapper.error('HANDLE_ERROR', '数据不存在');
        // 删除
        await collection.updateOne({_id: new ObjectID(id)}, { $set: { deleted: enums.BooleanNumber.YES, utime: new Date() } });
        return responseWrapper.succ({id});
    } catch (e) {
        logger.error(e.stack);
        return responseWrapper.error('HANDLE_ERROR', e.message);
    }
}

async function _build_algo_params(user, data, week, child) {

    // 构建算法参数
    const params = {
        period: data.period,
        subject: data.subject,
        effect: '教学计划',
        grade: data.grade,
        trees: [],
        ai_paper: 2, // 配置组卷数量
        real_paper: 2, // 选传，配置真卷数量
        type: type_name[child.type],
        user_id: user.id.toString(),
        province: user.province,
        city: user.city,
        school_id: user.schoolId,
        school_name: user.schoolName
    };

    const knowldege_map = new Map();
    const book_set = new Set();
    let knowledge_tree_id = null;
    for (const c of week.child) {
        if (c.category === 'exam') continue;
        const knowledge = {
            knowledges: [],
            chapter: [],
        };
        if (data.knowledge_tree_id) { // 教材
            knowledge.type = 'knowledge';
            knowledge.id = data.knowledge_tree_id;
            knowledge.name = data.knowledge_tree_name;
            knowledge_tree_id = knowledge.id;
            knowledge.version = '';
        } else {
            knowledge.type = 'book';
            knowledge.id = c.parent.book_id;
            knowledge.name = c.parent.book_name;
            knowledge.version = data.book_version;
            book_set.add(knowledge.id);
        }
        knowledge.chapter.push(c.id);
        knowldege_map.set(c.id, []);
        const same_data = params.trees.find(e => e.type === knowledge.type && e.id === knowledge.id);
        if (!_.isEmpty(same_data)) {
            same_data.chapter.push(c.id);
            continue;
        }
        params.trees.push(knowledge);
    }

    if (knowledge_tree_id) {
        let knowledge_tree = await client.kb.getKnowledgeTree(knowledge_tree_id);
        if (!_.isEmpty(knowledge_tree)) {
            _full_knowledges(knowldege_map, knowledge_tree.knowledge_tree);
        }
    }

    if (book_set.size > 0) {
        const books = await client.kb.getBooks([...book_set], 'knowledge');
        if (_.size(books)) {
            for (const book of books) {
                _full_knowledges(knowldege_map, book.book);
            }
        }
    }

    for (const k of params.trees) {
        if (!_.size(k.chapter)) continue;
        for (const c of k.chapter) {
            if (!knowldege_map.has(c)) continue;
            k.knowledges.push(...knowldege_map.get(c));
        }
        delete k.chapter;
    }
    return params
}

function _full_knowledges(knowledge_map, data) {
    if (!data.hasOwnProperty('children') || data.key === 'knowledge') {
        return;
    }
    for (const c of data.children) {
        if (!knowledge_map.has(c.id)) {
            _full_knowledges(knowledge_map, c);
        } else {
            _full_knowledge(c.id, knowledge_map, c);
        }
    }
}

function _full_knowledge(key, knowledge_map, data) {
    if (data.key !== 'knowledge') {
        if (!data.hasOwnProperty('children')) return;
        for (const c of data.children) {
            _full_knowledge(key, knowledge_map, c);
        }
    } else {
        knowledge_map.get(key).push({
            id: data.id,
            name: data.name
        });
    }
}











