/*
 * Desc: kb api transmiter
 * Author: guochanghui
 */
const config = require('config');
const ResponseWrapper = require('../../middlewares/response_wrapper');
const HttpWrapper = require('../../utils/http_wrapper.js');

const TIMEOUT = 5000;
const RETRY_TIMES = 1;

function transmit(req, res) {
    let responseWrapper = new ResponseWrapper(req, res);
    let server = config.get('SE_KB_SERVER');
    let query = req.query;
    query['api_key'] = server.appKey;
    // 过滤mkp 题目、卷子
    query['filter_mkp'] = true;
    let path = `/se_kb/v2${req.path}`; // ori path
    let options = {
        protocol: server.protocol,
        hostname: server.hostname,
        port: server.port,
        path: path,
        method: req.method,
        query: query,
        timeout: TIMEOUT,
        retryTimes: RETRY_TIMES,
    };
    let body = JSON.stringify(req.body);
    if (body !== '{}') {
        options['headers'] = {
            'content-type': 'application/json',
            'content-length': Buffer.byteLength(body),
        };
        options['body'] = body;
    }
    let httpWrapper = new HttpWrapper();
    httpWrapper.request(options, function(err, _res){
        if (err){
            return responseWrapper.error('HANDLE_ERROR', err.message);
        }
        if (_res.statusCode === 200){
            let ret = JSON.parse(_res.data);
            if (ret.code === 0){
                responseWrapper.succ(ret.data);
            } else {
                responseWrapper.send(ret);
            }
        } else {
            return responseWrapper.error('HANDLE_ERROR');
        }
    });
}

module.exports = {
    transmit: transmit,
}
